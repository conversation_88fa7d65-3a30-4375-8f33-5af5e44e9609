# Test Case: Fix cho lỗi search với ký tự đặc biệt

## Vấn đề gốc
- API: `/v1/user/marketing/audience-custom-fields?search=%C3%A2s` (search="âs")
- Lỗi: 9999 - "An unexpected error occurred"
- Nguyên nhân: Query SQL với trường `tags` kiểu `jsonb` và ký tự đặc biệt

## Các thay đổi đã thực hiện

### 1. <PERSON><PERSON>i thiện Query SQL
**Trước:**
```sql
customField.tags::text ILIKE :search
```

**Sau:**
```sql
EXISTS (SELECT 1 FROM jsonb_array_elements_text(customField.tags) AS tag WHERE tag ILIKE :search)
```

### 2. Sanitize Search Parameter
```typescript
const sanitizedSearch = search ? search.trim().replace(/[%_\\]/g, '\\$&') : undefined;
```

### 3. <PERSON><PERSON><PERSON> thiện Error Handling
```typescript
throw new AppException(
  MARKETING_ERROR_CODES.CUSTOM_FIELD_QUERY_FAILED,
  `Lỗi khi tìm kiếm trường tùy chỉnh: ${error.message}`,
  {
    userId,
    queryParams: queryDto,
    originalError: error.message,
  }
);
```

## Test Cases để kiểm tra

### 1. Test với ký tự tiếng Việt
```bash
GET /v1/user/marketing/audience-custom-fields?search=âs
GET /v1/user/marketing/audience-custom-fields?search=ê
GET /v1/user/marketing/audience-custom-fields?search=ô
```

### 2. Test với ký tự đặc biệt SQL
```bash
GET /v1/user/marketing/audience-custom-fields?search=%
GET /v1/user/marketing/audience-custom-fields?search=_
GET /v1/user/marketing/audience-custom-fields?search=\
```

### 3. Test với URL encoding
```bash
GET /v1/user/marketing/audience-custom-fields?search=%C3%A2s
GET /v1/user/marketing/audience-custom-fields?search=%C3%AA
```

### 4. Test với search rỗng hoặc null
```bash
GET /v1/user/marketing/audience-custom-fields?search=
GET /v1/user/marketing/audience-custom-fields?search=   
```

## Kết quả mong đợi
- Không còn lỗi 9999
- API trả về kết quả phân trang bình thường
- Log chi tiết hơn nếu có lỗi
- Xử lý an toàn với các ký tự đặc biệt
