-- Migration: Add job_ids column to user_campaigns table
-- Description: Thêm column job_ids để lưu trữ danh sách job IDs trong queue cho việc hủy job

-- Add job_ids column
ALTER TABLE user_campaigns 
ADD COLUMN job_ids JSONB NULL 
COMMENT '<PERSON>h sách ID của job trong queue để có thể hủy job khi cần';

-- Add index for better performance when querying by job_ids
CREATE INDEX idx_user_campaigns_job_ids ON user_campaigns USING GIN (job_ids);

-- Example data structure for job_ids:
-- job_ids: ["1234567890", "1234567891"]

-- Rollback script (if needed):
-- ALTER TABLE user_campaigns DROP COLUMN job_ids;
-- DROP INDEX IF EXISTS idx_user_campaigns_job_ids;
