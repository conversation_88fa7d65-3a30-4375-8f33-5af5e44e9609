/**
 * Enum định nghĩa các môi trường ứng dụng
 */
export enum Environment {
  Development = 'development',
  Production = 'production',
  Test = 'test',
  Staging = 'staging',
}

/**
 * Enum định nghĩa các loại cấu hình
 */
export enum ConfigType {
  App = 'app',
  Database = 'database',
  Storage = 'storage',
  Auth = 'auth',
  S3 = 's3',
  SecretKeyModel = 'secrectKeyModel',
  Facebook = 'facebook',
  Email = 'email',
  Redis = 'redis',
  Shipment = 'shipment',
}

/**
 * Các hằng số liên quan đến cấu hình
 */
export const CONFIG_CONSTANTS = {
  // Tên file cấu hình cho các môi trường
  ENV_FILES: {
    [Environment.Development]: '.env.development',
    [Environment.Production]: '.env',
    [Environment.Test]: '.env.test',
    [Environment.Staging]: '.env.staging',
    default: '.env',
  },

  // <PERSON><PERSON><PERSON> kh<PERSON>a cấu hình
  KEYS: {
    NODE_ENV: 'NODE_ENV',
    PORT: 'PORT',
    API_PREFIX: 'API_PREFIX',
  },

  // Giá trị mặc định
  DEFAULTS: {
    NODE_ENV: Environment.Development,
    PORT: 3000,
    API_PREFIX: 'api/v1',
  },
};
