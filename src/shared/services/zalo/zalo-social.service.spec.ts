import { Test, TestingModule } from '@nestjs/testing';
import { ConfigService } from '@nestjs/config';
import { ZaloSocialService } from './zalo-social.service';
import { ZaloService } from './zalo.service';
import { HttpService } from '@nestjs/axios';

describe('ZaloSocialService', () => {
  let service: ZaloSocialService;
  let zaloService: ZaloService;
  let configService: ConfigService;

  const mockZaloService = {
    getSocialAccessToken: jest.fn(),
    refreshSocialAccessToken: jest.fn(),
    getSocialUserInfo: jest.fn(),
    validateAccessToken: jest.fn(),
  };

  const mockConfigService = {
    get: jest.fn(),
  };

  const mockHttpService = {
    get: jest.fn(),
    post: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ZaloSocialService,
        {
          provide: ZaloService,
          useValue: mockZaloService,
        },
        {
          provide: ConfigService,
          useValue: mockConfigService,
        },
        {
          provide: HttpService,
          useValue: mockHttpService,
        },
      ],
    }).compile();

    service = module.get<ZaloSocialService>(ZaloSocialService);
    zaloService = module.get<ZaloService>(ZaloService);
    configService = module.get<ConfigService>(ConfigService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createAuthUrl', () => {
    it('should create auth URL with default scope', () => {
      const appId = 'test_app_id';
      const redirectUri = 'http://localhost:3000/callback';
      
      const authUrl = service.createAuthUrl(appId, redirectUri);
      
      expect(authUrl).toContain('https://oauth.zaloapp.com/v4/permission');
      expect(authUrl).toContain(`app_id=${appId}`);
      expect(authUrl).toContain(`redirect_uri=${encodeURIComponent(redirectUri)}`);
      expect(authUrl).toContain('scope=id%2Cname%2Cpicture');
    });

    it('should create auth URL with custom scope and state', () => {
      const appId = 'test_app_id';
      const redirectUri = 'http://localhost:3000/callback';
      const scope = 'id,name,picture,gender';
      const state = 'custom_state';
      
      const authUrl = service.createAuthUrl(appId, redirectUri, scope, state);
      
      expect(authUrl).toContain(`scope=${encodeURIComponent(scope)}`);
      expect(authUrl).toContain(`state=${state}`);
    });
  });

  describe('getAccessToken', () => {
    it('should call zaloService.getSocialAccessToken', async () => {
      const appId = 'test_app_id';
      const appSecret = 'test_app_secret';
      const code = 'test_code';
      const redirectUri = 'http://localhost:3000/callback';
      
      const mockTokens = {
        access_token: 'test_access_token',
        expires_in: 3600,
        refresh_token: 'test_refresh_token',
      };
      
      mockZaloService.getSocialAccessToken.mockResolvedValue(mockTokens);
      
      const result = await service.getAccessToken(appId, appSecret, code, redirectUri);
      
      expect(mockZaloService.getSocialAccessToken).toHaveBeenCalledWith(
        appId,
        appSecret,
        code,
        redirectUri
      );
      expect(result).toEqual(mockTokens);
    });
  });

  describe('refreshAccessToken', () => {
    it('should call zaloService.refreshSocialAccessToken', async () => {
      const appId = 'test_app_id';
      const refreshToken = 'test_refresh_token';
      
      const mockNewTokens = {
        access_token: 'new_access_token',
        expires_in: 3600,
        refresh_token: 'new_refresh_token',
      };
      
      mockZaloService.refreshSocialAccessToken.mockResolvedValue(mockNewTokens);
      
      const result = await service.refreshAccessToken(appId, refreshToken);
      
      expect(mockZaloService.refreshSocialAccessToken).toHaveBeenCalledWith(
        appId,
        refreshToken
      );
      expect(result).toEqual(mockNewTokens);
    });
  });

  describe('getUserInfo', () => {
    it('should call zaloService.getSocialUserInfo with basic fields', async () => {
      const accessToken = 'test_access_token';
      
      const mockUserInfo = {
        id: 'test_user_id',
        name: 'Test User',
        picture: { data: { url: 'http://example.com/avatar.jpg' } },
      };
      
      mockZaloService.getSocialUserInfo.mockResolvedValue(mockUserInfo);
      
      const result = await service.getUserInfo(accessToken);
      
      expect(mockZaloService.getSocialUserInfo).toHaveBeenCalledWith(
        accessToken,
        'id,name,picture'
      );
      expect(result).toEqual(mockUserInfo);
    });
  });

  describe('validateToken', () => {
    it('should return true for valid token', async () => {
      const accessToken = 'valid_token';
      
      mockZaloService.validateAccessToken.mockResolvedValue(true);
      
      const result = await service.validateToken(accessToken);
      
      expect(mockZaloService.validateAccessToken).toHaveBeenCalledWith(accessToken);
      expect(result).toBe(true);
    });

    it('should return false for invalid token', async () => {
      const accessToken = 'invalid_token';
      
      mockZaloService.validateAccessToken.mockResolvedValue(false);
      
      const result = await service.validateToken(accessToken);
      
      expect(result).toBe(false);
    });
  });

  describe('getUserInfoSafely', () => {
    it('should return user info on success', async () => {
      const accessToken = 'test_access_token';
      const mockUserInfo = {
        id: 'test_user_id',
        name: 'Test User',
        picture: { data: { url: 'http://example.com/avatar.jpg' } },
      };
      
      mockZaloService.getSocialUserInfo.mockResolvedValue(mockUserInfo);
      
      const result = await service.getUserInfoSafely(accessToken);
      
      expect(result).toEqual(mockUserInfo);
    });

    it('should return null on error', async () => {
      const accessToken = 'invalid_token';
      
      mockZaloService.getSocialUserInfo.mockRejectedValue(new Error('Token invalid'));
      
      const result = await service.getUserInfoSafely(accessToken);
      
      expect(result).toBeNull();
    });
  });

  describe('ensureValidToken', () => {
    it('should return existing token if valid', async () => {
      const appId = 'test_app_id';
      const accessToken = 'valid_token';
      const refreshToken = 'test_refresh_token';
      
      mockZaloService.validateAccessToken.mockResolvedValue(true);
      
      const result = await service.ensureValidToken(appId, accessToken, refreshToken);
      
      expect(result).toEqual({
        accessToken,
        isRefreshed: false,
      });
    });

    it('should refresh token if invalid', async () => {
      const appId = 'test_app_id';
      const accessToken = 'invalid_token';
      const refreshToken = 'test_refresh_token';
      
      const mockNewTokens = {
        access_token: 'new_access_token',
        expires_in: 3600,
        refresh_token: 'new_refresh_token',
      };
      
      mockZaloService.validateAccessToken.mockResolvedValue(false);
      mockZaloService.refreshSocialAccessToken.mockResolvedValue(mockNewTokens);
      
      const result = await service.ensureValidToken(appId, accessToken, refreshToken);
      
      expect(result).toEqual({
        accessToken: 'new_access_token',
        refreshToken: 'new_refresh_token',
        isRefreshed: true,
      });
    });
  });
});
