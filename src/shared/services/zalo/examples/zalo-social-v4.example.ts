import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ZaloSocialService } from '../zalo-social.service';
import { ZaloSocialUserInfo, ZaloAccessToken, ZaloRefreshTokenResponse } from '../zalo.interface';

/**
 * Ví dụ về cách sử dụng Zalo Social API V4 với User Access Token
 * 
 * Tài liệu tham khảo:
 * - https://developers.zalo.me/docs/social-api/tham-khao/user-access-token-v4
 * - https://developers.zalo.me/docs/social-api/tham-khao/mot-so-luu-y-voi-user-access-token-v4
 */
@Injectable()
export class ZaloSocialV4Example {
  private readonly logger = new Logger(ZaloSocialV4Example.name);
  private readonly appId: string;
  private readonly appSecret: string;
  private readonly redirectUri: string;

  constructor(
    private readonly zaloSocialService: ZaloSocialService,
    private readonly configService: ConfigService,
  ) {
    this.appId = this.configService.get<string>('ZALO_APP_ID') || '';
    this.appSecret = this.configService.get<string>('ZALO_APP_SECRET') || '';
    this.redirectUri = this.configService.get<string>('ZALO_REDIRECT_URI') || 'http://localhost:3000/auth/zalo/callback';
  }

  /**
   * Bước 1: Tạo URL xác thực để người dùng đăng nhập Zalo
   * Người dùng sẽ được chuyển hướng đến URL này để cấp quyền
   */
  createLoginUrl(state?: string): string {
    // Các quyền cơ bản: id, name, picture
    const basicScope = 'id,name,picture';
    
    // Các quyền mở rộng (cần xin phép từ Zalo)
    const extendedScope = 'id,name,picture,gender,birthday,location';

    return this.zaloSocialService.createAuthUrl(
      this.appId,
      this.redirectUri,
      basicScope, // Sử dụng scope cơ bản
      state
    );
  }

  /**
   * Bước 2: Xử lý callback từ Zalo sau khi người dùng cấp quyền
   * @param code Authorization code từ Zalo
   * @param state State parameter để xác thực
   * @returns Access token và refresh token
   */
  async handleCallback(code: string, state?: string): Promise<ZaloAccessToken> {
    try {
      this.logger.log(`Xử lý callback từ Zalo với code: ${code}`);
      
      // Lấy access token từ authorization code
      const tokens = await this.zaloSocialService.getAccessToken(
        this.appId,
        this.appSecret,
        code,
        this.redirectUri
      );

      this.logger.log('Lấy access token thành công');
      
      // Lưu tokens vào database hoặc cache
      await this.saveTokens(tokens);

      return tokens;
    } catch (error) {
      this.logger.error(`Lỗi khi xử lý callback: ${error.message}`);
      throw error;
    }
  }

  /**
   * Bước 3: Lấy thông tin người dùng
   * @param accessToken Access token của người dùng
   * @returns Thông tin người dùng
   */
  async getUserProfile(accessToken: string): Promise<ZaloSocialUserInfo> {
    try {
      // Lấy thông tin cơ bản
      const userInfo = await this.zaloSocialService.getUserInfo(accessToken);
      
      this.logger.log(`Lấy thông tin người dùng thành công: ${userInfo.name}`);
      
      return userInfo;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin người dùng: ${error.message}`);
      throw error;
    }
  }

  /**
   * Bước 4: Làm mới access token khi hết hạn
   * @param refreshToken Refresh token
   * @returns Access token mới
   */
  async refreshUserToken(refreshToken: string): Promise<ZaloRefreshTokenResponse> {
    try {
      const newTokens = await this.zaloSocialService.refreshAccessToken(
        this.appId,
        refreshToken
      );

      this.logger.log('Làm mới access token thành công');
      
      // Cập nhật tokens trong database
      await this.updateTokens(newTokens);

      return newTokens;
    } catch (error) {
      this.logger.error(`Lỗi khi làm mới token: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ về flow hoàn chỉnh: Kiểm tra token và tự động làm mới nếu cần
   * @param userId ID người dùng trong hệ thống
   * @returns Thông tin người dùng với token hợp lệ
   */
  async getUserWithValidToken(userId: string): Promise<{
    userInfo: ZaloSocialUserInfo;
    accessToken: string;
    isTokenRefreshed: boolean;
  }> {
    try {
      // Lấy tokens từ database
      const storedTokens = await this.getStoredTokens(userId);
      
      if (!storedTokens) {
        throw new Error('Người dùng chưa kết nối với Zalo');
      }

      // Kiểm tra và làm mới token tự động
      const validTokens = await this.zaloSocialService.ensureValidToken(
        this.appId,
        storedTokens.accessToken,
        storedTokens.refreshToken
      );

      // Nếu token được làm mới, cập nhật database
      if (validTokens.isRefreshed) {
        await this.updateTokens({
          access_token: validTokens.accessToken,
          refresh_token: validTokens.refreshToken,
          expires_in: 3600 // 1 giờ
        });
      }

      // Lấy thông tin người dùng
      const userInfo = await this.getUserProfile(validTokens.accessToken);

      return {
        userInfo,
        accessToken: validTokens.accessToken,
        isTokenRefreshed: validTokens.isRefreshed
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy thông tin người dùng với token hợp lệ: ${error.message}`);
      throw error;
    }
  }

  /**
   * Ví dụ về xử lý lỗi an toàn
   * @param accessToken Access token
   * @returns Thông tin người dùng hoặc null nếu có lỗi
   */
  async getUserSafely(accessToken: string): Promise<ZaloSocialUserInfo | null> {
    return this.zaloSocialService.getUserInfoSafely(accessToken);
  }

  /**
   * Lưu tokens vào database (mock implementation)
   */
  private async saveTokens(tokens: ZaloAccessToken): Promise<void> {
    // TODO: Implement database save logic
    this.logger.log('Lưu tokens vào database');
  }

  /**
   * Cập nhật tokens trong database (mock implementation)
   */
  private async updateTokens(tokens: ZaloRefreshTokenResponse): Promise<void> {
    // TODO: Implement database update logic
    this.logger.log('Cập nhật tokens trong database');
  }

  /**
   * Lấy tokens từ database (mock implementation)
   */
  private async getStoredTokens(userId: string): Promise<{
    accessToken: string;
    refreshToken: string;
  } | null> {
    // TODO: Implement database get logic
    this.logger.log(`Lấy tokens từ database cho user: ${userId}`);
    return null;
  }
}

/**
 * Ví dụ về Controller sử dụng Zalo Social API V4
 */
/*
@Controller('auth/zalo')
export class ZaloAuthController {
  constructor(private readonly zaloSocialExample: ZaloSocialV4Example) {}

  @Get('login')
  login(@Query('state') state?: string) {
    const loginUrl = this.zaloSocialExample.createLoginUrl(state);
    return { loginUrl };
  }

  @Get('callback')
  async callback(
    @Query('code') code: string,
    @Query('state') state?: string
  ) {
    const tokens = await this.zaloSocialExample.handleCallback(code, state);
    const userInfo = await this.zaloSocialExample.getUserProfile(tokens.access_token);
    
    return {
      success: true,
      user: userInfo,
      tokens
    };
  }

  @Get('profile/:userId')
  async getProfile(@Param('userId') userId: string) {
    const result = await this.zaloSocialExample.getUserWithValidToken(userId);
    return result;
  }
}
*/
