import { AppException } from '@common/exceptions';
import { Injectable, Logger } from '@nestjs/common';
import { AnthropicService } from '../anthropic.service';
import { DeepSeekService } from '../deepseek.service';
import { GoogleAIService } from '../google_ai.service';
import { MetaAIService } from '../metaai.service';
import { OpenAiService } from '../openai.service';
import { XAIService } from '../xai.service';
import { ListModelResponse, ModelResponse } from '../interfaces/model-response.interface';
import { ApiKeyEncryptionHelper } from '@/modules/models/helpers/api-key-encryption.helper';
import { MODEL_TRAINING_ERROR_CODES } from '@/modules/models/exceptions';
import { ProviderEnumq, TypeProviderUtil } from '../utils/type-provider.util';

/**
 * Helper service để xử lý các thao tác với các nhà cung cấp AI
 * Cung cấp các phương thức chung để tương tác với các nhà cung cấp AI khác nhau
 */
@Injectable()
export class AiProviderHelper {
  private readonly logger = new Logger(AiProviderHelper.name);

  constructor(
    private readonly openAIService: OpenAiService,
    private readonly metaAIService: MetaAIService,
    private readonly xAIService: XAIService,
    private readonly googleAIService: GoogleAIService,
    private readonly anthropicService: AnthropicService,
    private readonly deepSeekService: DeepSeekService,
    private readonly apiKeyEncryptionHelper: ApiKeyEncryptionHelper,
  ) { }

  /**
   * Lấy danh sách mô hình từ nhà cung cấp AI
   * @param type Loại nhà cung cấp AI
   * @param apiKey API key đã được mã hóa
   * @param limit Giới hạn số lượng mô hình trả về (tùy chọn)
   * @param isAdmin Xác định API key thuộc về admin hay user
   * @param userId ID của user (chỉ cần thiết khi isAdmin = false)
   * @returns Danh sách mô hình từ nhà cung cấp AI
   */
  async getModels(
    type: string,
    apiKey: string,
    limit?: number,
    isAdmin: boolean = true,
    userId?: number,
  ): Promise<ListModelResponse> {
    // Kiểm tra API key
    if (!apiKey) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Provider not found',
      );
    }

    // Giải mã API key
    let apiKeyDecode: string;
    try {
      if (isAdmin) {
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptAdminApiKey(apiKey);
      } else {
        if (!userId) {
          throw new Error('User ID is required for user API key decryption');
        }
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptUserApiKey(apiKey, userId);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã API key: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Invalid API key',
      );
    }

    // Lấy danh sách mô hình từ nhà cung cấp tương ứng
    try {
      let models: ListModelResponse;
      switch (TypeProviderUtil.getMimeType(type)) {
        case ProviderEnumq.OPENAI:
          models = await this.openAIService.listModels(apiKeyDecode);
          break;
        case ProviderEnumq.META:
          models = await this.metaAIService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.XAI:
          models = await this.xAIService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.GOOGLE:
          models = await this.googleAIService.getModels(apiKeyDecode, limit);
          break;
        case ProviderEnumq.DEEPSEEK:
          models = await this.deepSeekService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.ANTHROPIC:
          models = await this.anthropicService.getModels(apiKeyDecode);
          break;
        default:
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
            'Provider not found',
          );
      }

      return models;
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách model: ${error.message}`, error.stack);

      // Xử lý lỗi API key hết hạn
      if (error.status === 401 ||
          error.message.includes('unauthorized') ||
          error.message.includes('authentication') ||
          error.message.includes('invalid key') ||
          error.message.includes('expired')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.API_KEY_EXPIRED,
          'API key đã hết hạn hoặc không hợp lệ',
        );
      }

      // Xử lý lỗi quota exceeded
      if (error.status === 429 || error.message.includes('quota') || error.message.includes('rate limit')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.API_KEY_EXPIRED,
          'API key đã vượt quá giới hạn sử dụng',
        );
      }

      // Các lỗi khác
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.DATA_FETCH_ERROR,
        `Lỗi khi lấy danh sách model: ${error.message}`,
      );
    }
  }

  /**
   * Test kết nối với nhà cung cấp AI
   * @param apiKey API key thô (chưa mã hóa)
   * @param provider Loại nhà cung cấp AI
   * @param baseUrl Base URL tùy chọn
   * @returns Kết quả test connection với thông tin chi tiết
   */
  async testConnection(
    apiKey: string,
    provider: string,
    isAdmin: boolean = true,
    userId?: number,
  ): Promise<{
    success: boolean;
    responseTime?: number;
    error?: string;
  }> {
    const startTime = Date.now();

    try {
      // Validate API key format trước khi test
      if (!apiKey || apiKey.trim().length === 0) {
        return {
          success: false,
          error: 'API key không được để trống'
        };
      }

      // Giải mã API key
    let apiKeyDecode: string;
    try {
      if (isAdmin) {
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptAdminApiKey(apiKey);
      } else {
        if (!userId) {
          throw new Error('User ID is required for user API key decryption');
        }
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptUserApiKey(apiKey, userId);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã API key: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Invalid API key',
      );
    }

      // Test connection với từng provider
      switch (TypeProviderUtil.getMimeType(provider)) {
        case ProviderEnumq.OPENAI:
          await this.openAIService.listModels(apiKeyDecode);
          break;
        case ProviderEnumq.META:
          await this.metaAIService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.XAI:
          await this.xAIService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.GOOGLE:
          await this.googleAIService.getModels(apiKeyDecode, 10);
          break;
        case ProviderEnumq.DEEPSEEK:
          await this.deepSeekService.getModels(apiKeyDecode);
          break;
        case ProviderEnumq.ANTHROPIC:
          await this.anthropicService.getModels(apiKeyDecode);
          break;
        default:
          return {
            success: false,
            error: `Nhà cung cấp '${provider}' không được hỗ trợ`
          };
      }

      const responseTime = Date.now() - startTime;

      return {
        success: true,
        responseTime,
      };

    } catch (error) {
      this.logger.error(`Lỗi khi test connection với ${provider}: ${error.message}`, error.stack);

      const responseTime = Date.now() - startTime;
      let errorMessage = 'Lỗi không xác định';

      // Xử lý các loại lỗi phổ biến
      if (error.status === 401 ||
          error.message.includes('unauthorized') ||
          error.message.includes('authentication') ||
          error.message.includes('invalid key') ||
          error.message.includes('expired')) {
        errorMessage = 'API key không hợp lệ hoặc đã hết hạn';
      } else if (error.status === 429 ||
                 error.message.includes('quota') ||
                 error.message.includes('rate limit')) {
        errorMessage = 'API key đã vượt quá giới hạn sử dụng';
      } else if (error.status === 403) {
        errorMessage = 'API key không có quyền truy cập';
      } else if (error.status === 404) {
        errorMessage = 'Endpoint không tồn tại';
      } else if (error.code === 'ENOTFOUND' || error.code === 'ECONNREFUSED') {
        errorMessage = 'Không thể kết nối đến server';
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        success: false,
        responseTime,
        error: errorMessage
      };
    }
  }

  /**
   * Kiểm tra một mô hình cụ thể có tồn tại trong nhà cung cấp AI không
   * @param modelId ID của mô hình cần kiểm tra
   * @param type Loại nhà cung cấp AI
   * @param apiKey API key đã được mã hóa
   * @param isAdmin Xác định API key thuộc về admin hay user
   * @param userId ID của user (chỉ cần thiết khi isAdmin = false)
   * @returns Không có giá trị trả về, sẽ throw exception nếu mô hình không tồn tại
   */
  async retrieveModel(
    modelId: string,
    type: string,
    apiKey: string,
    isAdmin: boolean = true,
    userId?: number,
  ): Promise<void> {
    // Kiểm tra API key
    if (!apiKey) {
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Provider not found',
      );
    }

    // Giải mã API key
    let apiKeyDecode: string;
    try {
      if (isAdmin) {
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptAdminApiKey(apiKey);
      } else {
        if (!userId) {
          throw new Error('User ID is required for user API key decryption');
        }
        apiKeyDecode = this.apiKeyEncryptionHelper.decryptUserApiKey(apiKey, userId);
      }
    } catch (error) {
      this.logger.error(`Lỗi khi giải mã API key: ${error.message}`, error.stack);
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
        'Invalid API key',
      );
    }

    // Kiểm tra mô hình trong nhà cung cấp tương ứng
    try {
      switch (TypeProviderUtil.getMimeType(type)) {
        case ProviderEnumq.OPENAI:
          await this.openAIService.retrieveModel(modelId, apiKeyDecode);
          break;
        case ProviderEnumq.META:
          await this.metaAIService.retrieveModel(modelId, apiKeyDecode);
          break;
        case ProviderEnumq.XAI:
          await this.xAIService.retrieveModel(modelId, apiKeyDecode);
          break;
        case ProviderEnumq.GOOGLE:
          await this.googleAIService.retrieveModel(modelId, apiKeyDecode);
          break;
        case ProviderEnumq.ANTHROPIC:
          await this.anthropicService.retrieveModel(modelId, apiKeyDecode);
          break;
        case ProviderEnumq.DEEPSEEK:
          const model = await this.deepSeekService.getModels(apiKeyDecode);
          if (!model.find((m) => m.id === modelId)) {
            throw new AppException(
              MODEL_TRAINING_ERROR_CODES.MODEL_NOT_FOUND,
              'Model not found',
            );
          }
          break;
        default:
          throw new AppException(
            MODEL_TRAINING_ERROR_CODES.PROVIDER_NOT_FOUND,
            'Provider not found',
          );
      }
    } catch (error) {
      this.logger.error(`Lỗi khi kiểm tra model: ${error.message}`, error.stack);

      // Xử lý lỗi API key hết hạn
      if (error.status === 401 ||
          error.message.includes('unauthorized') ||
          error.message.includes('authentication') ||
          error.message.includes('invalid key') ||
          error.message.includes('expired')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.API_KEY_EXPIRED,
          'API key đã hết hạn hoặc không hợp lệ',
        );
      }

      // Xử lý lỗi quota exceeded
      if (error.status === 429 || error.message.includes('quota') || error.message.includes('rate limit')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.API_KEY_EXPIRED,
          'API key đã vượt quá giới hạn sử dụng',
        );
      }

      // Xử lý lỗi không tìm thấy model
      if (error.status === 404 || error.message.includes('not found')) {
        throw new AppException(
          MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
          `ID fine-tuning model ${modelId} không tồn tại trong provider tương ứng với base model ${type}`,
        );
      }

      // Các lỗi khác
      throw new AppException(
        MODEL_TRAINING_ERROR_CODES.INVALID_MODEL_DATA,
        `Lỗi khi kiểm tra model: ${error.message}`,
      );
    }
  }
}
