import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { TelegramService } from './telegram.service';
import { AppException, ErrorCode } from '@common/exceptions';
import {
  TelegramBotCommand,
  TelegramBotInfo,
  TelegramMessage,
  TelegramSendDocumentOptions,
  TelegramSendMessageOptions,
  TelegramSendPhotoOptions,
  TelegramSetWebhookOptions,
  TelegramWebhookInfo
} from './telegram.interface';

/**
 * Service cung cấp các phương thức để quản lý bot Telegram
 */
@Injectable()
export class TelegramBotService {
  private readonly logger = new Logger(TelegramBotService.name);
  private readonly webhookBaseUrl: string;

  constructor(
    private readonly telegramService: TelegramService,
    private readonly configService: ConfigService
  ) {
    this.webhookBaseUrl = this.configService.get<string>('TELEGRAM_WEBHOOK_BASE_URL', '');
  }

  /**
   * <PERSON><PERSON><PERSON> thực token bot
   * @param botToken Token của bot
   * @returns Thông tin về bot nếu token hợp lệ
   */
  async verifyBotToken(botToken: string): Promise<TelegramBotInfo> {
    try {
      return await this.telegramService.getMe(botToken);
    } catch (error) {
      this.logger.error(`Error verifying bot token: ${error.message}`, error.stack);
      throw new AppException(
        ErrorCode.VALIDATION_ERROR,
        'Token bot không hợp lệ hoặc đã hết hạn'
      );
    }
  }

  /**
   * Thiết lập webhook cho bot
   * @param botToken Token của bot
   * @param botId ID của bot trong hệ thống
   * @param options Tùy chọn bổ sung
   * @returns URL webhook đã thiết lập
   */
  async setupWebhook(
    botToken: string,
    botId: number,
    options?: TelegramSetWebhookOptions
  ): Promise<string> {
    try {
      if (!this.webhookBaseUrl) {
        throw new AppException(
          ErrorCode.CONFIGURATION_ERROR,
          'URL webhook cơ sở chưa được cấu hình'
        );
      }

      // Tạo URL webhook
      const webhookUrl = `${this.webhookBaseUrl}/webhook/${botId}`;

      // Thiết lập webhook
      const success = await this.telegramService.setWebhook(botToken, webhookUrl, {
        allowed_updates: ['message', 'callback_query', 'edited_message', 'channel_post', 'edited_channel_post'],
        ...options
      });

      if (!success) {
        throw new AppException(
          ErrorCode.EXTERNAL_SERVICE_ERROR,
          'Không thể thiết lập webhook'
        );
      }

      return webhookUrl;
    } catch (error) {
      this.logger.error(`Error setting up webhook: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thiết lập webhook'
      );
    }
  }

  /**
   * Xóa webhook của bot
   * @param botToken Token của bot
   * @returns Kết quả xóa webhook
   */
  async removeWebhook(botToken: string): Promise<boolean> {
    try {
      return await this.telegramService.deleteWebhook(botToken, true);
    } catch (error) {
      this.logger.error(`Error removing webhook: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi xóa webhook'
      );
    }
  }

  /**
   * Lấy thông tin webhook của bot
   * @param botToken Token của bot
   * @returns Thông tin về webhook
   */
  async getWebhookInfo(botToken: string): Promise<TelegramWebhookInfo> {
    try {
      return await this.telegramService.getWebhookInfo(botToken);
    } catch (error) {
      this.logger.error(`Error getting webhook info: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy thông tin webhook'
      );
    }
  }

  /**
   * Thiết lập các lệnh mặc định cho bot
   * @param botToken Token của bot
   * @returns Kết quả thiết lập lệnh
   */
  async setupDefaultCommands(botToken: string): Promise<boolean> {
    try {
      const commands: TelegramBotCommand[] = [
        { command: 'start', description: 'Bắt đầu trò chuyện với bot' },
        { command: 'help', description: 'Hiển thị trợ giúp' },
        { command: 'settings', description: 'Cài đặt bot' }
      ];

      return await this.telegramService.setMyCommands(botToken, commands);
    } catch (error) {
      this.logger.error(`Error setting up default commands: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thiết lập lệnh mặc định'
      );
    }
  }

  /**
   * Thiết lập các lệnh tùy chỉnh cho bot
   * @param botToken Token của bot
   * @param commands Danh sách các lệnh
   * @returns Kết quả thiết lập lệnh
   */
  async setCommands(botToken: string, commands: TelegramBotCommand[]): Promise<boolean> {
    try {
      return await this.telegramService.setMyCommands(botToken, commands);
    } catch (error) {
      this.logger.error(`Error setting commands: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi thiết lập lệnh'
      );
    }
  }

  /**
   * Lấy danh sách các lệnh của bot
   * @param botToken Token của bot
   * @returns Danh sách các lệnh
   */
  async getCommands(botToken: string): Promise<TelegramBotCommand[]> {
    try {
      return await this.telegramService.getMyCommands(botToken);
    } catch (error) {
      this.logger.error(`Error getting commands: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi lấy danh sách lệnh'
      );
    }
  }

  /**
   * Gửi tin nhắn văn bản
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param text Nội dung tin nhắn
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendMessage(
    botToken: string,
    chatId: number | string,
    text: string,
    options?: TelegramSendMessageOptions
  ): Promise<TelegramMessage> {
    try {
      return await this.telegramService.sendMessage(botToken, chatId, text, options);
    } catch (error) {
      this.logger.error(`Error sending message: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi tin nhắn'
      );
    }
  }

  /**
   * Gửi ảnh
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param photo URL hoặc file_id của ảnh
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendPhoto(
    botToken: string,
    chatId: number | string,
    photo: string,
    options?: TelegramSendPhotoOptions
  ): Promise<TelegramMessage> {
    try {
      return await this.telegramService.sendPhoto(botToken, chatId, photo, options);
    } catch (error) {
      this.logger.error(`Error sending photo: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi ảnh'
      );
    }
  }

  /**
   * Gửi document
   * @param botToken Token của bot
   * @param chatId ID của cuộc trò chuyện
   * @param document URL hoặc file_id của document
   * @param options Tùy chọn bổ sung
   * @returns Thông tin về tin nhắn đã gửi
   */
  async sendDocument(
    botToken: string,
    chatId: number | string,
    document: string,
    options?: TelegramSendDocumentOptions
  ): Promise<TelegramMessage> {
    try {
      return await this.telegramService.sendDocument(botToken, chatId, document, options);
    } catch (error) {
      this.logger.error(`Error sending document: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        ErrorCode.EXTERNAL_SERVICE_ERROR,
        'Lỗi khi gửi document'
      );
    }
  }
}
