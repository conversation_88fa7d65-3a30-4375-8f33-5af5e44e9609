import { Injectable, Logger, OnModuleD<PERSON>roy, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import Redis, { Redis as RedisClient } from 'ioredis'; // Import Redis và type RedisClient
import { AppException, ErrorCode } from '@/common';

@Injectable()
export class RedisService implements OnModuleInit, OnModuleDestroy {
    private readonly logger = new Logger(RedisService.name);
    private client: RedisClient;

    constructor(private readonly configService: ConfigService) {}

    onModuleInit() {
        const redisUrl = this.configService.get<string>('REDIS_URL');
        if (!redisUrl) {
            throw new AppException(ErrorCode.REDIS_ERROR, 'REDIS_URL environment variable is not defined.');
        }

        try {
            // Kết nối với URL mới không cần TLS
            this.client = new Redis(redisUrl, {
                // Bỏ cấu hình tls vì dùng redis://
                // tls: {
                //     rejectUnauthorized: false,
                // },
                // <PERSON><PERSON><PERSON> tùy chọn khác có thể thêm vào đây
                // lazyConnect: true,
                // maxRetriesPerRequest: 3,
            });

            this.client.on('connect', () => {
                this.logger.log('Connected to Redis successfully.');
            });

            this.client.on('error', (error) => {
                this.logger.error('Redis connection error:', error);
                 // Có thể thêm logic xử lý lỗi kết nối ở đây, ví dụ thử kết nối lại hoặc báo động
            });
        } catch (error) {
            this.logger.error('Failed to initialize Redis client:', error);
            throw new AppException(ErrorCode.REDIS_ERROR, 'Failed to initialize Redis client', error);
        }
    }

    async onModuleDestroy() {
        if (this.client) {
            await this.client.quit();
            this.logger.log('Redis connection closed.');
        }
    }

    getClient(): RedisClient {
        if (!this.client) {
            // Điều này không nên xảy ra nếu onModuleInit thành công, nhưng để đề phòng
            throw new AppException(ErrorCode.REDIS_ERROR, 'Redis client is not initialized.');
        }
        return this.client;
    }

    /**
     * Lấy giá trị từ Redis.
     * @param key Khóa cần lấy.
     * @returns Promise<string | null> Giá trị dạng string hoặc null nếu không tìm thấy.
     */
    async get(key: string): Promise<string | null> {
        try {
            return await this.getClient().get(key);
        } catch (error) {
            this.logger.error(`Error getting key ${key} from Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error getting key ${key} from Redis`, error);
        }
    }

    /**
     * Thiết lập giá trị trong Redis.
     * @param key Khóa cần đặt.
     * @param value Giá trị (sẽ được chuyển thành string).
     * @returns Promise<'OK'>
     */
    async set(key: string, value: string | number | Buffer): Promise<'OK'> {
        try {
            return await this.getClient().set(key, value);
        } catch (error) {
            this.logger.error(`Error setting key ${key} in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error setting key ${key} in Redis`, error);
        }
    }

    /**
     * Thiết lập giá trị trong Redis với thời gian hết hạn.
     * @param key Khóa cần đặt.
     * @param value Giá trị.
     * @param expiryInSeconds Thời gian hết hạn tính bằng giây.
     * @returns Promise<'OK'>
     */
    async setWithExpiry(
        key: string,
        value: string | number | Buffer,
        expiryInSeconds: number,
    ): Promise<'OK'> {
        if (expiryInSeconds <= 0) {
            this.logger.error('Expiry time must be positive for setWithExpiry.');
            throw new AppException(ErrorCode.VALIDATION_ERROR, 'Expiry time must be positive.');
        }
        try {
            return await this.getClient().setex(key, expiryInSeconds, value);
        } catch (error) {
            this.logger.error(`Error setting key ${key} with expiry in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error setting key ${key} with expiry in Redis`, error);
        }
    }

    /**
     * Xóa một hoặc nhiều khóa khỏi Redis.
     * @param keys Khóa hoặc danh sách các khóa cần xóa.
     * @returns Promise<number> Số lượng khóa đã bị xóa.
     */
    async del(keys: string | string[]): Promise<number> {
        try {
            let result: number;
            if (Array.isArray(keys)) {
                // Nếu là mảng, đảm bảo không rỗng và dùng spread operator
                if (keys.length === 0) {
                    return 0;
                }
                result = await this.getClient().del(...keys);
            } else {
                // Nếu là một chuỗi đơn
                result = await this.getClient().del(keys);
            }
            return result;
        } catch (error) {
            this.logger.error(`Error deleting keys ${JSON.stringify(keys)} from Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error deleting keys from Redis`, error);
        }
    }

    /**
     * Kiểm tra xem một khóa có tồn tại không.
     * @param key Khóa cần kiểm tra.
     * @returns Promise<boolean> True nếu khóa tồn tại, false nếu không.
     */
    async exists(key: string): Promise<boolean> {
        try {
            const result = await this.getClient().exists(key);
            return result === 1;
        } catch (error) {
            this.logger.error(`Error checking existence of key ${key} in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error checking existence of key in Redis`, error);
        }
    }

    /**
     * Tăng giá trị của một khóa lên 1 (nếu là số).
     * @param key Khóa cần tăng.
     * @returns Promise<number> Giá trị mới của khóa sau khi tăng.
     */
    async incr(key: string): Promise<number> {
        try {
            return await this.getClient().incr(key);
        } catch (error) {
            this.logger.error(`Error incrementing key ${key} in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error incrementing key in Redis`, error);
        }
    }

     /**
     * Giảm giá trị của một khóa xuống 1 (nếu là số).
     * @param key Khóa cần giảm.
     * @returns Promise<number> Giá trị mới của khóa sau khi giảm.
     */
    async decr(key: string): Promise<number> {
        try {
            return await this.getClient().decr(key);
        } catch (error) {
            this.logger.error(`Error decrementing key ${key} in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error decrementing key in Redis`, error);
        }
    }

    /**
     * Tìm kiếm các khóa theo mẫu.
     * @param pattern Mẫu tìm kiếm (ví dụ: "user:*").
     * @returns Promise<string[]> Danh sách các khóa phù hợp với mẫu.
     */
    async keys(pattern: string): Promise<string[]> {
        try {
            return await this.getClient().keys(pattern);
        } catch (error) {
            this.logger.error(`Error getting keys with pattern ${pattern} from Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error getting keys with pattern from Redis`, error);
        }
    }

    /**
     * Đặt thời gian hết hạn cho một khóa.
     * @param key Khóa cần đặt thời gian hết hạn.
     * @param seconds Thời gian hết hạn tính bằng giây.
     * @returns Promise<number> 1 nếu thành công, 0 nếu khóa không tồn tại.
     */
    async expire(key: string, seconds: number): Promise<number> {
        try {
            return await this.getClient().expire(key, seconds);
        } catch (error) {
            this.logger.error(`Error setting expiration for key ${key} in Redis:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error setting expiration for key in Redis`, error);
        }
    }

    // ==================== REDIS STREAMS METHODS ====================

    /**
     * Create a consumer group for Redis Streams
     * @param streamKey Stream key (e.g., 'agent_stream:threadId')
     * @param groupName Consumer group name
     * @param startId Starting ID for the group ('0' for beginning, '$' for new messages)
     * @returns Promise<'OK'> Success confirmation
     */
    async createConsumerGroup(streamKey: string, groupName: string, startId: string = '0-0'): Promise<'OK' | null> {
        try {
            // Create consumer group, ignore if already exists
            return await this.getClient().xgroup('CREATE', streamKey, groupName, startId, 'MKSTREAM') as 'OK';
        } catch (error: any) {
            // Ignore BUSYGROUP error (group already exists)
            if (error.message && error.message.includes('BUSYGROUP')) {
                this.logger.debug(`Consumer group ${groupName} already exists for stream ${streamKey}`);
                return 'OK';
            }
            this.logger.error(`Error creating consumer group ${groupName} for stream ${streamKey}:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error creating consumer group for stream`, error);
        }
    }

    /**
     * Read messages from Redis Stream using consumer group
     * @param streamKey Stream key to read from
     * @param groupName Consumer group name
     * @param consumerId Consumer ID within the group
     * @param startId Starting ID ('>' for new messages, '0' for pending messages)
     * @param count Maximum number of messages to read
     * @param blockMs Milliseconds to block waiting for messages (0 = no blocking)
     * @returns Promise<any> Stream messages or null if no messages
     */
    async readStreamGroup(
        streamKey: string,
        groupName: string,
        consumerId: string,
        startId: string = '>',
        count: number = 10,
        blockMs: number = 1000
    ): Promise<any> {
        try {
            const args: string[] = ['GROUP', groupName, consumerId];

            if (count > 0) {
                args.push('COUNT', count.toString());
            }

            if (blockMs > 0) {
                args.push('BLOCK', blockMs.toString());
            }

            args.push('STREAMS', streamKey, startId);

            // TypeScript-safe way to call xreadgroup with dynamic arguments
            return await this.getClient().call('XREADGROUP', ...args);
        } catch (error) {
            this.logger.error(`Error reading from stream ${streamKey} with group ${groupName}:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error reading from stream with consumer group`, error);
        }
    }

    /**
     * Delete a consumer group
     * @param streamKey Stream key
     * @param groupName Consumer group name to delete
     * @returns Promise<number> Number of groups deleted
     */
    async deleteConsumerGroup(streamKey: string, groupName: string): Promise<number> {
        try {
            return await this.getClient().xgroup('DESTROY', streamKey, groupName) as number;
        } catch (error) {
            this.logger.error(`Error deleting consumer group ${groupName} for stream ${streamKey}:`, error);
            throw new AppException(ErrorCode.REDIS_ERROR, `Error deleting consumer group`, error);
        }
    }

    /**
     * Parse Redis Stream fields into a JavaScript object
     * @param fields Array of field-value pairs from Redis Stream
     * @returns Parsed object
     */
    parseStreamFields(fields: string[]): Record<string, any> {
        const result: Record<string, any> = {};

        for (let i = 0; i < fields.length; i += 2) {
            const key = fields[i];
            const value = fields[i + 1];

            // Try to parse JSON values, fallback to string
            try {
                result[key] = JSON.parse(value);
            } catch {
                result[key] = value;
            }
        }

        return result;
    }

    /**
     * Get a duplicate Redis client for separate connections
     * Useful for pub/sub or long-running operations
     * @returns Redis client instance
     */
    getDuplicateClient(): RedisClient {
        try {
            return this.getClient().duplicate();
        } catch (error) {
            this.logger.error('Error creating duplicate Redis client:', error);
            throw new AppException(ErrorCode.REDIS_ERROR, 'Error creating duplicate Redis client', error);
        }
    }
}