import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Storage, Bucket, GetSignedUrlConfig } from '@google-cloud/storage';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class GoogleStorageService {
  private storage: Storage;
  private bucket: Bucket;
  private readonly logger = new Logger(GoogleStorageService.name);

  constructor(private readonly configService: ConfigService) {
    try {
      // Lấy thông tin xác thực từ biến môi trường hoặc file
      const keyFilename = this.configService.get<string>('GOOGLE_APPLICATION_CREDENTIALS');
      const bucketName = this.configService.get<string>('GOOGLE_CLOUD_STORAGE_BUCKET');

      if (!keyFilename || !bucketName) {
        this.logger.error('Missing Google Cloud Storage configuration');
        return;
      }

      // Khởi tạo Storage client
      this.storage = new Storage({
        keyFilename,
      });

      // Lấy bucket
      this.bucket = this.storage.bucket(bucketName);
      this.logger.log(`Google Cloud Storage initialized with bucket: ${bucketName}`);
    } catch (error) {
      this.logger.error(`Failed to initialize Google Cloud Storage: ${error.message}`);
    }
  }

  /**
   * Tải file lên Google Cloud Storage
   * @param filePath Đường dẫn đến file cần tải lên
   * @param destination Đường dẫn đích trên Google Cloud Storage
   * @returns URL của file đã tải lên
   */
  async uploadFile(filePath: string, destination: string): Promise<string> {
    try {
      // Kiểm tra xem file có tồn tại không
      if (!fs.existsSync(filePath)) {
        throw new Error(`File not found: ${filePath}`);
      }

      // Tải file lên
      const [file] = await this.bucket.upload(filePath, {
        destination,
        metadata: {
          cacheControl: 'public, max-age=31536000',
        },
      });

      // Trả về URL công khai
      return `https://storage.googleapis.com/${this.bucket.name}/${file.name}`;
    } catch (error) {
      this.logger.error(`Failed to upload file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tải buffer lên Google Cloud Storage
   * @param buffer Buffer cần tải lên
   * @param destination Đường dẫn đích trên Google Cloud Storage
   * @param contentType Loại nội dung của file
   * @returns URL của file đã tải lên
   */
  async uploadBuffer(buffer: Buffer, destination: string, contentType: string): Promise<string> {
    try {
      // Tạo file trên Google Cloud Storage
      const file = this.bucket.file(destination);

      // Tải buffer lên
      await file.save(buffer, {
        contentType,
        metadata: {
          cacheControl: 'public, max-age=31536000',
        },
      });

      // Trả về URL công khai
      return `https://storage.googleapis.com/${this.bucket.name}/${file.name}`;
    } catch (error) {
      this.logger.error(`Failed to upload buffer: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo URL có chữ ký để tải file lên
   * @param destination Đường dẫn đích trên Google Cloud Storage
   * @param contentType Loại nội dung của file
   * @param expiresIn Thời gian hết hạn (tính bằng giây)
   * @returns URL có chữ ký
   */
  async getSignedUploadUrl(destination: string, contentType: string, expiresIn = 900): Promise<string> {
    try {
      const file = this.bucket.file(destination);
      const options: GetSignedUrlConfig = {
        version: 'v4',
        action: 'write',
        expires: Date.now() + expiresIn * 1000,
        contentType,
      };

      const [url] = await file.getSignedUrl(options);
      return url;
    } catch (error) {
      this.logger.error(`Failed to get signed upload URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Tạo URL có chữ ký để tải file xuống
   * @param filename Tên file trên Google Cloud Storage
   * @param expiresIn Thời gian hết hạn (tính bằng giây)
   * @returns URL có chữ ký
   */
  async getSignedDownloadUrl(filename: string, expiresIn = 3600): Promise<string> {
    try {
      const file = this.bucket.file(filename);
      const options: GetSignedUrlConfig = {
        version: 'v4',
        action: 'read',
        expires: Date.now() + expiresIn * 1000,
      };

      const [url] = await file.getSignedUrl(options);
      return url;
    } catch (error) {
      this.logger.error(`Failed to get signed download URL: ${error.message}`);
      throw error;
    }
  }

  /**
   * Xóa file trên Google Cloud Storage
   * @param filename Tên file cần xóa
   * @returns true nếu xóa thành công
   */
  async deleteFile(filename: string): Promise<boolean> {
    try {
      const file = this.bucket.file(filename);
      await file.delete();
      return true;
    } catch (error) {
      this.logger.error(`Failed to delete file: ${error.message}`);
      throw error;
    }
  }

  /**
   * Kiểm tra xem file có tồn tại không
   * @param filename Tên file cần kiểm tra
   * @returns true nếu file tồn tại
   */
  async fileExists(filename: string): Promise<boolean> {
    try {
      const file = this.bucket.file(filename);
      const [exists] = await file.exists();
      return exists;
    } catch (error) {
      this.logger.error(`Failed to check if file exists: ${error.message}`);
      throw error;
    }
  }
}
