import { Body, Controller, Delete, Get, Param, Post, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserRoleService } from '../../user/service/user-role.service';
import { AddUserRoleDto } from '../../dto/add-user-role.dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { JwtEmployeeGuard } from '@/modules/auth/guards';

@ApiTags(SWAGGER_API_TAGS.ADMIN_USER)
@Controller('user-roles')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class UserRoleController {
  constructor(private readonly userRoleService: UserRoleService) {}

  /**
   * Thêm vai trò USER cho người dùng
   */
  @ApiOperation({ summary: 'Thêm vai trò USER cho người dùng' })
  @ApiResponse({ status: 201, description: 'Vai trò đã được thêm thành công' })
  @ApiResponse({ status: 400, description: 'Dữ liệu đầu vào không hợp lệ' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người dùng hoặc vai trò' })
  @ApiResponse({ status: 409, description: 'Người dùng đã có vai trò này' })
  @Post('add-user-role')
  async addUserRole(@Body() addUserRoleDto: AddUserRoleDto) {
    return this.userRoleService.addUserRole(addUserRoleDto.userId);
  }

  /**
   * Lấy tất cả vai trò của người dùng
   */
  @ApiOperation({ summary: 'Lấy tất cả vai trò của người dùng' })
  @ApiParam({ name: 'userId', description: 'ID của người dùng' })
  @ApiResponse({ status: 200, description: 'Danh sách vai trò của người dùng' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người dùng' })
  @Get('user/:userId')
  async getUserRoles(@Param('userId') userId: number) {
    return this.userRoleService.getUserRoles(userId);
  }

  /**
   * Xóa vai trò của người dùng
   */
  @ApiOperation({ summary: 'Xóa vai trò của người dùng' })
  @ApiParam({ name: 'userId', description: 'ID của người dùng' })
  @ApiParam({ name: 'roleType', description: 'Loại vai trò' })
  @ApiResponse({ status: 200, description: 'Vai trò đã được xóa thành công' })
  @ApiResponse({ status: 404, description: 'Không tìm thấy người dùng hoặc vai trò' })
  @Delete('user/:userId/role/:roleType')
  async removeUserRole(
    @Param('userId') userId: number,
    @Param('roleType') roleType: string,
  ) {
    return this.userRoleService.removeUserRole(userId, roleType);
  }

  /**
   * Kiểm tra người dùng có vai trò không
   */
  @ApiOperation({ summary: 'Kiểm tra người dùng có vai trò không' })
  @ApiParam({ name: 'userId', description: 'ID của người dùng' })
  @ApiParam({ name: 'roleType', description: 'Loại vai trò' })
  @ApiResponse({ status: 200, description: 'Kết quả kiểm tra' })
  @Get('user/:userId/has-role/:roleType')
  async hasRole(
    @Param('userId') userId: number,
    @Param('roleType') roleType: string,
  ) {
    const hasRole = await this.userRoleService.hasRole(userId, roleType);
    return { hasRole };
  }
}
