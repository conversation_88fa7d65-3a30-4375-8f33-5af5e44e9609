import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto } from '@common/dto';
import { UserTypeEnum } from '@modules/user/enums';

/**
 * Enum định nghĩa các trường có thể sắp xếp
 */
export enum UserSortBy {
  ID = 'id',
  FULL_NAME = 'fullName',
  EMAIL = 'email',
  PHONE_NUMBER = 'phoneNumber',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  POINTS_BALANCE = 'pointsBalance'
}

/**
 * DTO cho các tham số query khi lấy danh sách người dùng
 */
export class UserQueryDto extends QueryDto {
  @ApiProperty({
    description: 'Lọc theo trạng thái hoạt động',
    required: false,
    example: true
  })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({
    description: 'Lọc theo loại tài khoản',
    enum: UserTypeEnum,
    required: false,
    example: UserTypeEnum.INDIVIDUAL
  })
  @IsOptional()
  @IsEnum(UserTypeEnum)
  type?: UserTypeEnum;

  @ApiProperty({
    description: 'Trường cần sắp xếp',
    enum: UserSortBy,
    default: UserSortBy.CREATED_AT,
    required: false
  })
  @IsOptional()
  @IsEnum(UserSortBy)
  sortBy?: UserSortBy = UserSortBy.CREATED_AT;

  @ApiProperty({
    description: 'Lọc theo email',
    required: false,
    example: '<EMAIL>'
  })
  @IsOptional()
  @IsString()
  email?: string;

  @ApiProperty({
    description: 'Lọc theo số điện thoại',
    required: false,
    example: '0123456789'
  })
  @IsOptional()
  @IsString()
  phoneNumber?: string;
}
