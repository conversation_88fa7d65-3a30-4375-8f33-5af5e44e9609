import { Injectable, BadRequestException, NotFoundException, UnauthorizedException, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '../entities/user.entity';
import { Transactional } from 'typeorm-transactional';
import * as bcrypt from 'bcrypt';

/**
 * Service xử lý logic liên quan đến mật khẩu
 */
@Injectable()
export class PasswordService {
  private readonly logger = new Logger(PasswordService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
  ) {}

  /**
   * Đổi mật khẩu cho người dùng
   * @param userId ID của người dùng
   * @param currentPassword Mật khẩu hiện tại
   * @param newPassword Mật khẩu mới
   * @param confirmPassword Xác nhận mật khẩu mới
   * @returns Thông báo kết quả
   */
  @Transactional()
  async changePassword(
    userId: number,
    currentPassword: string,
    newPassword: string,
    confirmPassword: string,
  ): Promise<{ message: string }> {
    // Kiểm tra mật khẩu mới và xác nhận mật khẩu mới có khớp nhau không
    if (newPassword !== confirmPassword) {
      throw new BadRequestException('Mật khẩu mới và xác nhận mật khẩu mới không khớp');
    }

    // Kiểm tra độ mạnh của mật khẩu
    this.validatePasswordStrength(newPassword);

    // Tìm người dùng theo ID
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`Người dùng với ID "${userId}" không tồn tại`);
    }

    // Kiểm tra mật khẩu hiện tại có chính xác không
    const isPasswordValid = await this.comparePasswords(currentPassword, user.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Mật khẩu hiện tại không chính xác');
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = await this.hashPassword(newPassword);

    // Cập nhật mật khẩu mới và thời gian cập nhật
    user.password = hashedPassword;
    user.updatedAt = Date.now();

    // Lưu thay đổi vào cơ sở dữ liệu
    await this.userRepository.save(user);

    this.logger.log(`Người dùng có ID ${userId} đã đổi mật khẩu thành công`);
    return { message: 'Đổi mật khẩu thành công' };
  }



  /**
   * Kiểm tra độ mạnh của mật khẩu
   * @param password Mật khẩu cần kiểm tra
   * @returns Thông tin về độ mạnh của mật khẩu
   */
  checkPasswordStrength(password: string): { score: number; feedback: string } {
    let score = 0;
    const feedback: string[] = [];

    // Kiểm tra độ dài
    if (password.length < 8) {
      feedback.push('Mật khẩu quá ngắn (tối thiểu 8 ký tự)');
    } else {
      score += 1;
    }

    // Kiểm tra chữ hoa
    if (!/[A-Z]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 chữ hoa');
    } else {
      score += 1;
    }

    // Kiểm tra chữ thường
    if (!/[a-z]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 chữ thường');
    } else {
      score += 1;
    }

    // Kiểm tra số
    if (!/[0-9]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 số');
    } else {
      score += 1;
    }

    // Kiểm tra ký tự đặc biệt
    if (!/[^A-Za-z0-9]/.test(password)) {
      feedback.push('Mật khẩu nên chứa ít nhất 1 ký tự đặc biệt');
    } else {
      score += 1;
    }

    // Đánh giá độ mạnh
    let strengthFeedback = '';
    if (score < 2) {
      strengthFeedback = 'Mật khẩu yếu';
    } else if (score < 4) {
      strengthFeedback = 'Mật khẩu trung bình';
    } else {
      strengthFeedback = 'Mật khẩu mạnh';
    }

    return {
      score,
      feedback: feedback.length > 0 ? `${strengthFeedback}. ${feedback.join('. ')}` : strengthFeedback,
    };
  }

  /**
   * Đổi mật khẩu mà không cần mật khẩu cũ
   * @param userId ID của người dùng
   * @param newPassword Mật khẩu mới
   * @returns Thông báo kết quả
   */
  @Transactional()
  async setPassword(userId: number, newPassword: string): Promise<{ message: string }> {
    // Kiểm tra độ mạnh của mật khẩu
    this.validatePasswordStrength(newPassword);

    // Tìm người dùng theo ID
    const user = await this.userRepository.findOne({ where: { id: userId } });
    if (!user) {
      throw new NotFoundException(`Người dùng với ID "${userId}" không tồn tại`);
    }

    // Mã hóa mật khẩu mới
    const hashedPassword = await this.hashPassword(newPassword);

    // Cập nhật mật khẩu mới và thời gian cập nhật
    user.password = hashedPassword;
    user.updatedAt = Date.now();

    // Lưu thay đổi vào cơ sở dữ liệu
    await this.userRepository.save(user);

    this.logger.log(`Đã đổi mật khẩu cho người dùng có ID ${userId}`);
    return { message: 'Đổi mật khẩu thành công' };
  }

  /**
   * Kiểm tra và validate độ mạnh của mật khẩu
   * @param password Mật khẩu cần kiểm tra
   * @throws BadRequestException nếu mật khẩu không đủ mạnh
   */
  private validatePasswordStrength(password: string): void {
    const { score, feedback } = this.checkPasswordStrength(password);
    if (score < 3) {
      throw new BadRequestException(`Mật khẩu không đủ mạnh. ${feedback}`);
    }
  }



  /**
   * Mã hóa mật khẩu
   * @param password Mật khẩu cần mã hóa
   * @returns Mật khẩu đã mã hóa
   */
  private async hashPassword(password: string): Promise<string> {
    const salt = await bcrypt.genSalt();
    return bcrypt.hash(password, salt);
  }

  /**
   * So sánh mật khẩu
   * @param plainPassword Mật khẩu gốc
   * @param hashedPassword Mật khẩu đã mã hóa
   * @returns true nếu mật khẩu khớp, ngược lại false
   */
  private async comparePasswords(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }
}
