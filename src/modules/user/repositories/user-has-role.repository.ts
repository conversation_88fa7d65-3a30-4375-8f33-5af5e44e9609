import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserHasRole } from '../entities/user-has-role.entity';

/**
 * Repository cho UserHasRole
 */
@Injectable()
export class UserHasRoleRepository {
  constructor(
    @InjectRepository(UserHasRole)
    private readonly repository: Repository<UserHasRole>,
  ) {}

  /**
   * Thêm vai trò cho người dùng
   * @param userId ID của người dùng
   * @param roleId ID của vai trò
   * @returns UserHasRole đã được tạo
   */
  async addRoleToUser(userId: number, roleId: number): Promise<UserHasRole> {
    const userHasRole = this.repository.create({
      userId,
      roleId,
    });
    return this.repository.save(userHasRole);
  }

  /**
   * Kiểm tra người dùng đã có vai trò chưa
   * @param userId ID của người dùng
   * @param roleId ID của vai trò
   * @returns true nếu người dùng đã có vai trò, ngược lại false
   */
  async hasRole(userId: number, roleId: number): Promise<boolean> {
    const count = await this.repository.count({
      where: {
        userId,
        roleId,
      },
    });
    return count > 0;
  }

  /**
   * Lấy tất cả vai trò của người dùng
   * @param userId ID của người dùng
   * @returns Danh sách các ID vai trò của người dùng
   */
  async findRolesByUserId(userId: number): Promise<number[]> {
    const userRoles = await this.repository.find({
      where: { userId },
    });
    return userRoles.map(ur => ur.roleId);
  }

  /**
   * Xóa vai trò của người dùng
   * @param userId ID của người dùng
   * @param roleId ID của vai trò
   * @returns true nếu xóa thành công, ngược lại false
   */
  async removeRoleFromUser(userId: number, roleId: number): Promise<boolean> {
    const result = await this.repository.delete({
      userId,
      roleId,
    });
    return result.affected !== null && result.affected !== undefined && result.affected > 0;
  }
}
