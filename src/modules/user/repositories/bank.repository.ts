import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { FindManyOptions, FindOptionsWhere, Repository } from 'typeorm';
import { Bank } from '../entities/bank.entity';

/**
 * Repository xử lý truy vấn dữ liệu cho bảng banks
 */
@Injectable()
export class BankRepository {
  constructor(
    @InjectRepository(Bank)
    public readonly repository: Repository<Bank>,
  ) {}

  /**
   * Tìm tất cả ngân hàng
   * @returns Danh sách ngân hàng
   */
  async findAll(): Promise<Bank[]> {
    return this.repository.find();
  }

  /**
   * Tìm kiếm nhiều ngân hàng
   * @param options Tùy chọn tìm kiếm
   * @returns Danh sách ngân hàng
   */
  async find(options?: FindManyOptions<Bank>): Promise<Bank[]> {
    return this.repository.find(options);
  }

  /**
   * Đếm số lượng ngân hàng
   * @param options Tùy chọn tìm kiếm
   * @returns Số lượng ngân hàng
   */
  async count(options?: FindManyOptions<Bank>): Promise<number> {
    return this.repository.count(options);
  }

  /**
   * Tìm ngân hàng theo mã
   * @param bankCode Mã ngân hàng
   * @returns Ngân hàng hoặc null nếu không tìm thấy
   */
  async findByCode(bankCode: string): Promise<Bank | null> {
    return this.repository.findOne({
      where: { bankCode },
    });
  }
}
