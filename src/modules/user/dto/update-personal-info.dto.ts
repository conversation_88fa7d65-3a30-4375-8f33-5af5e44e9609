import { ApiProperty } from '@nestjs/swagger';
import { IsDateString, IsEnum, IsOptional, IsString, Matches } from 'class-validator';
import { GenderEnum } from '../enums';

/**
 * DTO cho việc cập nhật thông tin cá nhân của người dùng
 */
export class UpdatePersonalInfoDto {
  /**
   * Tên đầy đủ của người dùng
   */
  @ApiProperty({
    description: 'Tên đầy đủ của người dùng',
    example: 'Nguyễn Văn A',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên đầy đủ phải là chuỗi' })
  fullName?: string;

  /**
   * Giới tính của người dùng
   */
  @ApiProperty({
    description: 'Giới tính của người dùng',
    enum: GenderEnum,
    example: GenderEnum.MALE,
    required: false,
  })
  @IsOptional()
  @IsEnum(GenderEnum, { message: 'Giớ<PERSON> tính không hợp lệ' })
  gender?: GenderEnum;

  /**
   * Ngày sinh của người dùng
   */
  @ApiProperty({
    description: 'Ngày sinh của người dùng (định dạng YYYY-MM-DD)',
    example: '1990-01-01',
    required: false,
  })
  @IsOptional()
  @IsDateString({}, { message: 'Ngày sinh không hợp lệ, phải theo định dạng YYYY-MM-DD' })
  dateOfBirth?: string;

  /**
   * Địa chỉ của người dùng
   */
  @ApiProperty({
    description: 'Địa chỉ của người dùng',
    example: 'Số 123, Đường ABC, Quận XYZ, TP. Hồ Chí Minh',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Địa chỉ phải là chuỗi' })
  address?: string;

  /**
   * Số điện thoại của người dùng
   */
  @ApiProperty({
    description: 'Số điện thoại của người dùng (hỗ trợ định dạng quốc tế)',
    example: '+84912345678',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Số điện thoại phải là chuỗi' })
  @Matches(/^[\+]?[1-9][\d]{0,15}$/, {
    message: 'Số điện thoại không hợp lệ, phải là số điện thoại quốc tế hợp lệ',
  })
  phoneNumber?: string;
}
