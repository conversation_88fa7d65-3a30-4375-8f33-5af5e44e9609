import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsString, Length } from 'class-validator';

/**
 * DTO cho việc bật/tắt xác thực hai lớp qua SMS
 */
export class ToggleSmsAuthDto {
  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua SMS',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;
}

/**
 * DTO cho việc bật/tắt xác thực hai lớp qua email
 */
export class ToggleEmailAuthDto {
  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua email',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;
}

/**
 * DTO cho việc bật/tắt xác thực hai lớp qua Google Authenticator
 */
export class ToggleGoogleAuthDto {
  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua Google Authenticator',
    example: true,
  })
  @IsBoolean()
  enabled: boolean;
}

/**
 * DTO cho việc xác nhận cài đặt Google Authenticator
 */
export class VerifyGoogleAuthDto {
  @ApiProperty({
    description: 'Mã xác thực từ Google Authenticator',
    example: '123456',
  })
  @IsString()
  @IsNotEmpty()
  @Length(6, 6)
  token: string;
}

/**
 * DTO cho phản hồi trạng thái xác thực hai lớp
 */
export class TwoFactorAuthStatusDto {
  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua SMS',
    example: false,
  })
  otpSmsEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua email',
    example: true,
  })
  otpEmailEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái bật/tắt xác thực hai lớp qua Google Authenticator',
    example: false,
  })
  googleAuthenticatorEnabled: boolean;

  @ApiProperty({
    description: 'Trạng thái xác nhận cài đặt Google Authenticator',
    example: false,
  })
  isGoogleAuthenticatorConfirmed: boolean;
}

/**
 * DTO cho phản hồi thiết lập Google Authenticator
 */
export class GoogleAuthSetupDto {
  @ApiProperty({
    description: 'Secret key cho Google Authenticator',
    example: 'ABCDEFGHIJKLMNOPQRSTUVWXYZ234567',
  })
  secretKey: string;

  @ApiProperty({
    description: 'URL QR code cho Google Authenticator',
    example: 'data:image/png;base64,...',
  })
  qrCodeUrl: string;
}
