import { Column, Entity, ManyToMany, PrimaryGeneratedColumn } from 'typeorm';
import { User } from './user.entity';

/**
 * Entity đại diện cho bảng user_roles trong cơ sở dữ liệu
 * Bảng lưu các vai trò của người dùng
 */
@Entity('user_roles')
export class UserRole {
  /**
   * ID tự tăng
   */
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  /**
   * Loại vai trò
   */
  @Column({ name: 'type', length: 255, unique: true })
  type: string;

  /**
   * Tên vai trò
   */
  @Column({ name: 'name', length: 255, nullable: true })
  name: string;

  /**
   * Thời gian tạo
   */
  @Column({ name: 'created_at', type: 'bigint', nullable: true })
  createdAt: number;

  /**
   * Thời gian cập nhật
   */
  @Column({ name: 'updated_at', type: 'bigint', nullable: true })
  updatedAt: number;
}
