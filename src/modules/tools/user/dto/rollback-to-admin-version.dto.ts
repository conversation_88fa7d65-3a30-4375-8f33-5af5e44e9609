import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, IsUUID } from 'class-validator';

/**
 * DTO cho việc khôi phục về phiên bản gốc từ admin
 */
export class RollbackToAdminVersionDto {
  @ApiProperty({
    description: 'ID của tool người dùng cần khôi phục',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @IsString()
  @IsUUID()
  @IsNotEmpty()
  userToolId: string;
}
