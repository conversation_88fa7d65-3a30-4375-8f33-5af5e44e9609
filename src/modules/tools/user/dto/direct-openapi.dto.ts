import { ApiProperty } from '@nestjs/swagger';
import { IsOpenApiSpec } from '../validators/openapi-spec.validator';

/**
 * DTO cho việc tích hợp trực tiếp từ đặc tả OpenAPI
 * Lưu ý: Class này chỉ dùng cho Swagger documentation, không dùng cho validation
 */
export class DirectOpenApiDto {
  /**
   * Phiên bản OpenAPI
   */
  @ApiProperty({
    description: 'Phiên bản OpenAPI',
    example: '3.0.0'
  })
  openapi: string;

  /**
   * Thông tin về API
   */
  @ApiProperty({
    description: 'Thông tin về API',
    example: {
      title: 'My API',
      description: 'API mô tả các chức năng cơ bản',
      version: '1.0.0'
    }
  })
  info: {
    title: string;
    description?: string;
    version: string;
  };

  /**
   * Danh sách server
   */
  @ApiProperty({
    description: 'Danh sách server',
    example: [
      {
        url: 'http://localhost:8080/api',
        description: 'Local server'
      }
    ]
  })
  servers?: {
    url: string;
    description?: string;
  }[];

  /**
   * Đường dẫn API
   */
  @ApiProperty({
    description: 'Đường dẫn API',
    example: {
      '/users': {
        get: {
          summary: 'Lấy danh sách người dùng',
          responses: {
            '200': {
              description: 'Thành công'
            }
          }
        },
        post: {
          summary: 'Tạo người dùng mới',
          responses: {
            '201': {
              description: 'Tạo thành công'
            }
          }
        }
      }
    }
  })
  paths: Record<string, any>;

  /**
   * Các thành phần
   */
  @ApiProperty({
    description: 'Các thành phần',
    example: {
      schemas: {
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1
            },
            username: {
              type: 'string',
              example: 'johndoe'
            }
          }
        }
      }
    }
  })
  components?: Record<string, any>;
}
