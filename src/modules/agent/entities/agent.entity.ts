import {
  BeforeUpdate,
  Column,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';

/**
 * Entity đại diện cho bảng agents trong cơ sở dữ liệu
 * Bảng lưu thông tin chung của tất cả agent trong hệ thống
 */
@Entity('agents')
export class Agent {
  /**
   * UUID định danh duy nhất cho mỗi agent
   */
  @PrimaryGeneratedColumn('uuid')
  id: string;

  /**
   * Tên hiển thị của agent
   */
  @Column({ length: 255 })
  name: string;

  /**
   * Lưu key s3 của avatar agent
   */
  @Column({ length: 255, type: 'varchar', nullable: true })
  avatar: string | null;

  /**
   * Cấu hình AI model dạng JSONB (ví dụ: {"temperature": 0.7, "max_tokens": 1000})
   */
  @Column({ name: 'model_config', type: 'jsonb', default: '{}' })
  modelConfig: ModelConfig;

  /**
   * H<PERSON>ớng dẫn hoặc system prompt cho agent
   */
  @Column({ type: 'text', nullable: true })
  instruction: string | null;

  /**
   * ID của kho vector sử dụng bởi agent
   */
  @Column({
    name: 'vector_store_id',
    type: 'varchar',
    length: 100,
    nullable: true,
  })
  vectorStoreId: string | null;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @CreateDateColumn({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }

  /**
   * Thời điểm xóa mềm (timestamp millis)
   */
  @Column({ name: 'deleted_at', type: 'bigint', nullable: true })
  deletedAt: number | null;
}
