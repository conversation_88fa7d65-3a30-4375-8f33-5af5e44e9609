import { BeforeUpdate, Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { RankStrategyStatus } from '@modules/agent/constants';

/**
 * Entity đại diện cho bảng rank_strategy trong cơ sở dữ liệu
 * Quản lý các chiến lược xếp hạng
 */
@Entity('rank_strategy')
export class RankStrategy {
  /**
   * ID định danh duy nhất cho mỗi rank strategy
   */
  @PrimaryGeneratedColumn({ type: 'integer' })
  id: number;

  /**
   * Đường dẫn hình ảnh
   */
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Đường dẫn hình ảnh'
  })
  image: string;

  /**
   * Tên của rank strategy
   */
  @Column({
    type: 'varchar',
    length: 255,
    comment: 'Tên của rank strategy'
  })
  name: string;

  /**
   * Mô tả về rank strategy
   */
  @Column({
    type: 'text',
    nullable: true,
    comment: 'Mô tả về rank strategy'
  })
  description?: string;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời điểm tạo, tính bằng mili giây'
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật gần nhất (timestamp millis)
   */
  @Column({
    name: 'updated_at',
    type: 'bigint',
    default: () => '(EXTRACT(epoch FROM now()) * 1000)::bigint',
    comment: 'Thời điểm cập nhật gần nhất, tính bằng mili giây'
  })
  updatedAt: number;

  @BeforeUpdate()
  updateTimestamp() {
    this.updatedAt = Date.now();
  }

  /**
   * ID nhân viên tạo
   */
  @Column({ name: 'created_by', type: 'integer', nullable: true })
  createdBy: number | null;

  /**
   * ID nhân viên cập nhật
   */
  @Column({ name: 'updated_by', type: 'integer', nullable: true })
  updatedBy: number | null;

  /**
   * Trạng thái của rank strategy
   */
  @Column({
    type: 'enum',
    enum: RankStrategyStatus,
    default: RankStrategyStatus.APPROVED,
    comment: 'Trạng thái của rank strategy'
  })
  status: RankStrategyStatus;
}
