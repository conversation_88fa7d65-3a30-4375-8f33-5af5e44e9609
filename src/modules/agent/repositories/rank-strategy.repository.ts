import { Injectable, Logger } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { RankStrategy } from '@modules/agent/entities';
import { RankStrategyStatus } from '@modules/agent/constants';
import { PaginatedResult } from '@/common/response';

/**
 * Repository cho RankStrategy
 * Xử lý các thao tác CRUD và truy vấn nâng cao liên quan đến rank strategy
 */
@Injectable()
export class RankStrategyRepository extends Repository<RankStrategy> {
  private readonly logger = new Logger(RankStrategyRepository.name);

  constructor(private dataSource: DataSource) {
    super(RankStrategy, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder cơ bản cho RankStrategy
   * @returns SelectQueryBuilder cho RankStrategy
   */
  private createBaseQuery(): SelectQueryBuilder<RankStrategy> {
    return this.createQueryBuilder('rankStrategy');
  }

  /**
   * Tìm rank strategy theo ID
   * @param id ID của rank strategy
   * @returns RankStrategy nếu tìm thấy, null nếu không tìm thấy
   */
  async findById(id: number): Promise<RankStrategy | null> {
    return this.createBaseQuery()
      .where('rankStrategy.id = :id', { id })
      .getOne();
  }

  /**
   * Tìm rank strategy theo tên
   * @param name Tên của rank strategy
   * @returns RankStrategy nếu tìm thấy, null nếu không tìm thấy
   */
  async findByName(name: string): Promise<RankStrategy | null> {
    return this.createBaseQuery()
      .where('rankStrategy.name = :name', { name })
      .getOne();
  }

  /**
   * Lấy danh sách rank strategies với phân trang
   * @param page Số trang
   * @param limit Số lượng item trên một trang
   * @param search Từ khóa tìm kiếm (tùy chọn)
   * @param status Trạng thái rank strategy (tùy chọn)
   * @param sortBy Trường sắp xếp
   * @param sortDirection Hướng sắp xếp
   * @returns Danh sách rank strategies với phân trang
   */
  async findPaginated(
    page: number,
    limit: number,
    search?: string,
    status?: RankStrategyStatus,
    sortBy: string = 'createdAt',
    sortDirection: 'ASC' | 'DESC' = 'DESC',
  ): Promise<PaginatedResult<RankStrategy>> {
    const qb = this.createBaseQuery();

    // Thêm điều kiện tìm kiếm nếu có
    if (search) {
      qb.andWhere('(rankStrategy.name ILIKE :search OR rankStrategy.description ILIKE :search)',
        { search: `%${search}%` });
    }

    // Thêm điều kiện lọc theo status nếu có
    if (status) {
      qb.andWhere('rankStrategy.status = :status', { status });
    }

    // Thêm phân trang và sắp xếp
    qb.skip((page - 1) * limit)
      .take(limit)
      .orderBy(`rankStrategy.${sortBy}`, sortDirection);

    const [items, total] = await qb.getManyAndCount();

    return {
      items,
      meta: {
        totalItems: total,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(total / limit),
        currentPage: page
      }
    };
  }

  /**
   * Lấy danh sách tất cả rank strategies đã được approved
   * @returns Danh sách rank strategies
   */
  async findAllApproved(): Promise<RankStrategy[]> {
    return this.createBaseQuery()
      .where('rankStrategy.status = :status', { status: RankStrategyStatus.APPROVED })
      .orderBy('rankStrategy.name', 'ASC')
      .getMany();
  }

  /**
   * Lấy danh sách rank strategies theo status
   * @param status Trạng thái cần lọc
   * @returns Danh sách rank strategies
   */
  async findByStatus(status: RankStrategyStatus): Promise<RankStrategy[]> {
    return this.createBaseQuery()
      .where('rankStrategy.status = :status', { status })
      .orderBy('rankStrategy.createdAt', 'DESC')
      .getMany();
  }

  /**
   * Kiểm tra tên rank strategy có tồn tại không (trừ ID hiện tại)
   * @param name Tên cần kiểm tra
   * @param excludeId ID cần loại trừ khỏi kiểm tra
   * @returns true nếu tồn tại, false nếu không
   */
  async isNameExists(name: string, excludeId?: number): Promise<boolean> {
    const qb = this.createBaseQuery()
      .where('rankStrategy.name = :name', { name });

    if (excludeId) {
      qb.andWhere('rankStrategy.id != :excludeId', { excludeId });
    }

    const count = await qb.getCount();
    return count > 0;
  }

  /**
   * Cập nhật status của rank strategy
   * @param id ID của rank strategy
   * @param status Status mới
   * @param updatedBy ID của người cập nhật
   * @returns true nếu cập nhật thành công
   */
  async updateStatus(id: number, status: RankStrategyStatus, updatedBy?: number): Promise<boolean> {
    try {
      const updateData: any = {
        status,
        updatedAt: Date.now(),
      };

      if (updatedBy) {
        updateData.updatedBy = updatedBy;
      }

      const result = await this.createQueryBuilder()
        .update(RankStrategy)
        .set(updateData)
        .where('id = :id', { id })
        .execute();

      return result.affected !== null && result.affected !== undefined && result.affected > 0;
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật status rank strategy ${id}: ${error.message}`);
      return false;
    }
  }

  /**
   * Đếm số lượng rank strategies theo status
   * @param status Trạng thái cần đếm
   * @returns Số lượng rank strategies
   */
  async countByStatus(status: RankStrategyStatus): Promise<number> {
    try {
      const result = await this.createBaseQuery()
        .where('rankStrategy.status = :status', { status })
        .getCount();

      return result;
    } catch (error) {
      this.logger.error(`Lỗi khi đếm rank strategies theo status ${status}: ${error.message}`);
      return 0;
    }
  }
}
