# Model Resolution Examples

## Tổng Quan

Tài liệu này cung cấp các ví dụ cụ thể về cách Basic Info API resolve model information từ database.

## Logic Resolution

### **Rule 1: keyLlmId chỉ có khi userModelId tồn tại**
### **Rule 2: Priority: userModelId → systemModelId → modelFineTuneId**

## Ví Dụ Cụ Thể

### **Scenario 1: User Model (có keyLlm)**

**Database State:**
```sql
-- agents_user table
user_model_id: "user-model-123"
system_model_id: null
model_fine_tune_id: null

-- user_models table
id: "user-model-123"
model_id: "gpt-4o"
key_llm_id: "key-llm-456"
```

**Query Executed:**
```sql
SELECT um.model_id, um.key_llm_id 
FROM user_models um 
WHERE um.id = 'user-model-123'
```

**Result:**
```json
{
  "modelId": "gpt-4o",
  "provider": "USER_KEY",
  "keyLlm": "key-llm-456"
}
```

### **Scenario 2: System Model (không có keyLlm)**

**Database State:**
```sql
-- agents_user table
user_model_id: null
system_model_id: "system-model-789"
model_fine_tune_id: null

-- system_models table
id: "system-model-789"
model_id: "claude-3-sonnet"
```

**Query Executed:**
```sql
SELECT sm.model_id 
FROM system_models sm 
WHERE sm.id = 'system-model-789'
```

**Result:**
```json
{
  "modelId": "claude-3-sonnet",
  "provider": "SYSTEM_KEY",
  "keyLlm": null
}
```

### **Scenario 3: Fine-tune Model (không có keyLlm)**

**Database State:**
```sql
-- agents_user table
user_model_id: null
system_model_id: null
model_fine_tune_id: "fine-tune-101"

-- user_model_fine_tune table
id: "fine-tune-101"
model_id: "ft:gpt-4o:custom:20241201"
```

**Query Executed:**
```sql
SELECT umft.model_id 
FROM user_model_fine_tune umft 
WHERE umft.id = 'fine-tune-101'
```

**Result:**
```json
{
  "modelId": "ft:gpt-4o:custom:20241201",
  "provider": "FINE_TUNED",
  "keyLlm": null
}
```

### **Scenario 4: No Model Configured**

**Database State:**
```sql
-- agents_user table
user_model_id: null
system_model_id: null
model_fine_tune_id: null
```

**Query Executed:**
```
No queries executed
```

**Result:**
```json
{
  "modelId": "",
  "provider": "unknown",
  "keyLlm": null
}
```

### **Scenario 5: Priority Test (userModelId wins)**

**Database State:**
```sql
-- agents_user table
user_model_id: "user-model-123"      ← Priority 1 (WINS)
system_model_id: "system-model-789"  ← Priority 2 (ignored)
model_fine_tune_id: "fine-tune-101"  ← Priority 3 (ignored)
```

**Query Executed:**
```sql
-- Only user_models query is executed
SELECT um.model_id, um.key_llm_id 
FROM user_models um 
WHERE um.id = 'user-model-123'
```

**Result:**
```json
{
  "modelId": "gpt-4o",
  "provider": "USER_KEY",
  "keyLlm": "key-llm-456"
}
```

## Service Implementation

### **Code Flow:**
```typescript
// Priority 1: User Model
if (agentUser.userModelId) {
  const userModel = await this.dataSource
    .createQueryBuilder()
    .select(['um.model_id', 'um.key_llm_id'])
    .from('user_models', 'um')
    .where('um.id = :userModelId', { userModelId: agentUser.userModelId })
    .getRawOne();

  if (userModel) {
    resolvedModelId = userModel.model_id || '';
    resolvedProvider = 'USER_KEY';
    keyLlmId = userModel.key_llm_id || null; // ✅ CHỈ CÓ Ở ĐÂY
  }
}
// Priority 2: System Model
else if (agentUser.systemModelId) {
  const systemModel = await this.dataSource
    .createQueryBuilder()
    .select(['sm.model_id'])
    .from('system_models', 'sm')
    .where('sm.id = :systemModelId', { systemModelId: agentUser.systemModelId })
    .getRawOne();

  if (systemModel) {
    resolvedModelId = systemModel.model_id || '';
    resolvedProvider = 'SYSTEM_KEY';
    // keyLlmId = null ❌ KHÔNG CÓ
  }
}
// Priority 3: Fine-tune Model
else if (agentUser.modelFineTuneId) {
  const fineTuneModel = await this.dataSource
    .createQueryBuilder()
    .select(['umft.model_id'])
    .from('user_model_fine_tune', 'umft')
    .where('umft.id = :modelFineTuneId', { modelFineTuneId: agentUser.modelFineTuneId })
    .getRawOne();

  if (fineTuneModel) {
    resolvedModelId = fineTuneModel.model_id || '';
    resolvedProvider = 'FINE_TUNED';
    // keyLlmId = null ❌ KHÔNG CÓ
  }
}
```

## API Response Examples

### **User Model Response:**
```json
{
  "success": true,
  "data": {
    "avatar": "https://cdn.example.com/avatars/agent.jpg",
    "name": "AI Assistant",
    "provider": "USER_KEY",
    "keyLlm": "key-llm-456",
    "modelId": "gpt-4o",
    "modelConfig": { "temperature": 0.7 },
    "instruction": "You are an AI assistant",
    "vectorId": "vector-123",
    "updatedAt": 1672531200000
  }
}
```

### **System Model Response:**
```json
{
  "success": true,
  "data": {
    "avatar": "https://cdn.example.com/avatars/agent.jpg",
    "name": "AI Assistant",
    "provider": "SYSTEM_KEY",
    "keyLlm": null,
    "modelId": "claude-3-sonnet",
    "modelConfig": { "temperature": 0.7 },
    "instruction": "You are an AI assistant",
    "vectorId": "vector-123",
    "updatedAt": 1672531200000
  }
}
```

## Error Handling

### **Database Query Failures:**
```typescript
try {
  const userModel = await this.dataSource.createQueryBuilder()...
} catch (error) {
  this.logger.warn(`Không thể resolve user model ${agentUser.userModelId}: ${error.message}`);
  // Continue với default values
}
```

### **Missing Model Data:**
```typescript
if (userModel) {
  resolvedModelId = userModel.model_id || '';  // Fallback to empty string
  keyLlmId = userModel.key_llm_id || null;     // Fallback to null
}
```

## Testing Scenarios

### **Unit Tests:**
1. ✅ userModelId có → keyLlm có giá trị
2. ✅ systemModelId có → keyLlm = null
3. ✅ modelFineTuneId có → keyLlm = null
4. ✅ Không có model nào → keyLlm = null
5. ✅ Priority test: userModelId wins over others
6. ✅ Database query failures handled gracefully
7. ✅ Missing model data handled gracefully

### **Integration Tests:**
1. ✅ End-to-end API calls với different model configurations
2. ✅ Database state verification
3. ✅ Response structure validation
4. ✅ Error scenarios testing
