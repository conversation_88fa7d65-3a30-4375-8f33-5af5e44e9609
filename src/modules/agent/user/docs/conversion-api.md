# Agent Conversion APIs

## Tổng Quan

Conversion APIs cho phép người dùng quản lý cấu hình chuyển đổi (conversion config) của agent. Conversion config định nghĩa schema JSON cho dữ liệu khách hàng mà agent sẽ thu thập và xử lý.

## C<PERSON>u Trúc Database

Conversion config được lưu trữ dưới dạng JSONB trong bảng `agents_user`:

```sql
-- Trường convert_config trong bảng agents_user
convert_config JSONB DEFAULT '[]'
```

## API Endpoints

### 1. L<PERSON>y Thông Tin Conversion Config

**GET** `/user/agents/{id}/conversion`

Lấy thông tin conversion config hiện tại của agent.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Response:**
```json
{
  "success": true,
  "data": {
    "convertConfig": [
      {
        "name": "customer_name",
        "type": "string",
        "description": "Tên đầy đủ của khách hàng",
        "required": true,
        "defaultValue": ""
      },
      {
        "name": "customer_email",
        "type": "string",
        "description": "Email của khách hàng",
        "required": true,
        "defaultValue": ""
      },
      {
        "name": "order_amount",
        "type": "number",
        "description": "Số tiền đơn hàng",
        "required": true,
        "defaultValue": 0
      }
    ],
    "totalFields": 3,
    "requiredFields": 3,
    "updatedAt": 1672531200000
  },
  "message": "Lấy thông tin conversion config thành công"
}
```

### 2. Cập Nhật Conversion Config

**PUT** `/user/agents/{id}/conversion`

Cập nhật conversion config của agent.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Request Body:**
```json
{
  "convertConfig": [
    {
      "name": "customer_name",
      "type": "string",
      "description": "Tên đầy đủ của khách hàng",
      "required": true,
      "defaultValue": ""
    },
    {
      "name": "customer_email",
      "type": "string",
      "description": "Email của khách hàng",
      "required": true,
      "defaultValue": ""
    },
    {
      "name": "customer_phone",
      "type": "string",
      "description": "Số điện thoại của khách hàng",
      "required": false,
      "defaultValue": ""
    },
    {
      "name": "order_amount",
      "type": "number",
      "description": "Số tiền đơn hàng",
      "required": true,
      "defaultValue": 0
    },
    {
      "name": "is_vip_customer",
      "type": "boolean",
      "description": "Khách hàng VIP",
      "required": false,
      "defaultValue": false
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "convertConfig": [
      // Updated conversion config
    ],
    "totalFields": 5,
    "requiredFields": 3,
    "updatedAt": 1672531300000
  },
  "message": "Cập nhật conversion config thành công"
}
```

### 3. Reset Conversion Config

**POST** `/user/agents/{id}/conversion/reset`

Reset conversion config về cấu hình mặc định.

**Parameters:**
- `id` (path): ID của agent (UUID)

**Response:**
```json
{
  "success": true,
  "data": {
    "convertConfig": [
      {
        "name": "customer_name",
        "type": "string",
        "description": "Tên đầy đủ của khách hàng",
        "required": true,
        "defaultValue": ""
      },
      {
        "name": "customer_email",
        "type": "string",
        "description": "Email của khách hàng",
        "required": true,
        "defaultValue": ""
      },
      {
        "name": "customer_phone",
        "type": "string",
        "description": "Số điện thoại của khách hàng",
        "required": false,
        "defaultValue": ""
      }
    ],
    "totalFields": 3,
    "requiredFields": 2,
    "updatedAt": 1672531400000
  },
  "message": "Reset conversion config thành công"
}
```

## Validation Rules

### **Field Name (name)**
- **Type**: String
- **Max Length**: 100 ký tự
- **Required**: Có
- **Unique**: Không được trùng lặp trong cùng config

### **Field Type (type)**
- **Type**: Enum
- **Values**: string, number, boolean, array_number, array_string, enum
- **Required**: Có

### **Description**
- **Type**: String
- **Max Length**: 500 ký tự
- **Required**: Có

### **Required**
- **Type**: Boolean
- **Required**: Có

### **Default Value**
- **Type**: Any (phù hợp với field type)
- **Optional**: Có

### **Array Limits**
- **Max Items**: 50 fields
- **Unique Names**: Không được có tên field trùng lặp

## Field Types

### **string**
- Dữ liệu văn bản
- Example: "Nguyễn Văn A", "<EMAIL>"

### **number**
- Dữ liệu số
- Example: 1000000, 25, 3.14

### **boolean**
- Dữ liệu true/false
- Example: true, false

### **array_number**
- Mảng số
- Example: [1, 2, 3], [100, 200, 300]

### **array_string**
- Mảng chuỗi
- Example: ["item1", "item2"], ["tag1", "tag2"]

### **enum**
- Giá trị enum/lựa chọn
- Example: "ACTIVE", "INACTIVE", "PENDING"

## Error Codes

- `40101`: Agent không tồn tại hoặc không thuộc về user
- `40135`: Cấu hình conversion không hợp lệ
- `40120`: Lỗi khi thao tác với tài nguyên agent

## Security

- Tất cả APIs yêu cầu JWT authentication
- User chỉ có thể thao tác với agents thuộc về mình
- Validation nghiêm ngặt cho tất cả input parameters

## Business Logic

### **Default Configuration**
- Khi agent mới được tạo, sẽ có 3 fields mặc định:
  - customer_name (string, required)
  - customer_email (string, required)
  - customer_phone (string, not required)

### **Data Persistence**
- Config được lưu dưới dạng JSONB trong PostgreSQL
- Hỗ trợ query và index trên các trường JSONB
- Validation đầy đủ trước khi lưu

### **Type Safety**
- Sử dụng TypeScript interfaces cho type safety
- Validation đầy đủ với class-validator
- Mapper pattern để chuyển đổi giữa DTO và Entity
