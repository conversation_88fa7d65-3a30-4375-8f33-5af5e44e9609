# Agent Management APIs

## Tổng Quan

Agent Management APIs cung cấp các chức năng quản lý cơ bản cho agents của người dùng:

1. **Delete Agent**: <PERSON><PERSON><PERSON> mềm agent
2. **Toggle Agent Active**: Bật/tắt trạng thái hoạt động của agent

## API Endpoints

### 1. Xóa Agent

**DELETE** `/user/agents/{id}`

Thực hiện soft delete agent. Agent sẽ không bị xóa vĩnh viễn mà chỉ được đánh dấu là đã xóa.

**Parameters:**
- `id` (path): ID của agent cần xóa (UUID)

**Response:**
```json
{
  "success": true,
  "data": null,
  "message": "Xóa agent thành công"
}
```

**Business Logic:**
- Kiểm tra agent có tồn tại và thuộc về user không
- Thực hiện soft delete bằng cách set `deletedAt = Date.now()`
- Agent sẽ không hiển thị trong danh sách sau khi xóa
- <PERSON><PERSON> thể restore lại trong tương lai (nếu c<PERSON> chức năng restore)

### 2. Bật/Tắt Agent

**PATCH** `/user/agents/{id}/active`

Đảo ngược trạng thái hoạt động của agent (toggle active status).

**Parameters:**
- `id` (path): ID của agent cần thay đổi trạng thái (UUID)

**Response:**
```json
{
  "success": true,
  "data": {
    "active": true
  },
  "message": "Cập nhật trạng thái agent thành công"
}
```

**Business Logic:**
- Kiểm tra agent có tồn tại và thuộc về user không
- Đảo ngược trạng thái `active` hiện tại
- Cập nhật trong bảng `agents_user`
- Trả về trạng thái mới

## Request/Response Details

### **Delete Agent**

#### Request
```http
DELETE /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890
Authorization: Bearer <jwt-token>
```

#### Success Response (200)
```json
{
  "success": true,
  "data": null,
  "message": "Xóa agent thành công"
}
```

#### Error Responses
```json
// Agent không tồn tại hoặc không thuộc về user
{
  "success": false,
  "error": {
    "code": "40101",
    "message": "Agent không tồn tại hoặc không thuộc về bạn"
  }
}

// Lỗi khi xóa
{
  "success": false,
  "error": {
    "code": "40121",
    "message": "Không thể xóa agent"
  }
}
```

### **Toggle Agent Active**

#### Request
```http
PATCH /user/agents/a1b2c3d4-e5f6-7890-abcd-ef1234567890/active
Authorization: Bearer <jwt-token>
```

#### Success Response (200)
```json
{
  "success": true,
  "data": {
    "active": false
  },
  "message": "Cập nhật trạng thái agent thành công"
}
```

#### Error Responses
```json
// Agent không tồn tại hoặc không thuộc về user
{
  "success": false,
  "error": {
    "code": "40101",
    "message": "Agent không tồn tại hoặc không thuộc về bạn"
  }
}

// Lỗi khi cập nhật
{
  "success": false,
  "error": {
    "code": "40120",
    "message": "Không thể cập nhật trạng thái agent"
  }
}
```

## Error Codes

| Code | Description | HTTP Status |
|------|-------------|-------------|
| `40101` | Agent không tồn tại hoặc không thuộc về user | 404 |
| `40120` | Lỗi khi thao tác với tài nguyên agent | 400 |
| `40121` | Lỗi khi xóa agent | 400 |
| `50001` | Lỗi server nội bộ | 500 |

## Security

### **Authentication**
- Tất cả APIs yêu cầu JWT authentication
- Token phải hợp lệ và chưa hết hạn

### **Authorization**
- User chỉ có thể thao tác với agents thuộc về mình
- Kiểm tra ownership thông qua `agents_user.user_id`

### **Input Validation**
- Agent ID phải là UUID hợp lệ
- Kiểm tra agent tồn tại trước khi thao tác

## Database Operations

### **Delete Agent Flow**
```sql
-- 1. Kiểm tra ownership
SELECT agent.*, agentUser.* 
FROM agents agent
INNER JOIN agents_user agentUser ON agentUser.id = agent.id
WHERE agent.id = ? AND agentUser.user_id = ?

-- 2. Soft delete
UPDATE agents 
SET deleted_at = ? 
WHERE id = ?
```

### **Toggle Active Flow**
```sql
-- 1. Kiểm tra ownership và lấy trạng thái hiện tại
SELECT agent.*, agentUser.* 
FROM agents agent
INNER JOIN agents_user agentUser ON agentUser.id = agent.id
WHERE agent.id = ? AND agentUser.user_id = ?

-- 2. Toggle active status
UPDATE agents_user 
SET active = NOT active 
WHERE id = ? AND user_id = ?
```

## Performance Considerations

### **Database Optimization**
- Sử dụng indexes trên `agents_user.user_id` và `agents.id`
- Transactions để đảm bảo data consistency
- Efficient ownership checks

### **Logging**
- Log tất cả delete operations
- Log active status changes
- Include user ID và agent ID trong logs

## Business Rules

### **Delete Agent**
- Chỉ soft delete, không hard delete
- Agent đã xóa không hiển thị trong lists
- Có thể restore trong tương lai
- Không ảnh hưởng đến related data

### **Toggle Active**
- Active agents có thể được sử dụng
- Inactive agents bị disable
- Không ảnh hưởng đến agent data
- Có thể toggle nhiều lần

## Integration Points

### **Frontend Integration**
```typescript
// Delete agent
const deleteAgent = async (agentId: string) => {
  const response = await fetch(`/user/agents/${agentId}`, {
    method: 'DELETE',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};

// Toggle active
const toggleAgentActive = async (agentId: string) => {
  const response = await fetch(`/user/agents/${agentId}/active`, {
    method: 'PATCH',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  });
  return response.json();
};
```

### **Mobile Integration**
- Confirm dialogs cho delete operations
- Visual feedback cho active/inactive states
- Optimistic updates với rollback

## Testing Scenarios

### **Delete Agent Tests**
- ✅ Delete own agent successfully
- ✅ Cannot delete other user's agent
- ✅ Cannot delete non-existent agent
- ✅ Agent disappears from lists after delete

### **Toggle Active Tests**
- ✅ Toggle active status successfully
- ✅ Cannot toggle other user's agent
- ✅ Cannot toggle non-existent agent
- ✅ Status changes correctly in database

## Future Enhancements

### **Bulk Operations**
- Bulk delete multiple agents
- Bulk toggle active status
- Batch processing for performance

### **Restore Functionality**
- Restore deleted agents
- List deleted agents
- Permanent delete after retention period

### **Audit Trail**
- Track who deleted/modified agents
- Timestamp all operations
- Detailed operation logs
