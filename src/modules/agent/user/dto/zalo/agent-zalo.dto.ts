import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber } from 'class-validator';

/**
 * DTO cho việc thêm nhiều Zalo Official Accounts vào agent
 */
export class AddZaloOfficialAccountsDto {
  /**
   * Danh sách ID của Zalo Official Accounts cần thêm
   */
  @ApiProperty({
    description: 'Danh sách ID của Zalo Official Accounts cần thêm',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  zaloOfficialAccountIds: number[];
}

/**
 * DTO cho việc gỡ nhiều Zalo Official Accounts khỏi agent
 */
export class RemoveZaloOfficialAccountsDto {
  /**
   * Danh sách ID của Zalo Official Accounts cần gỡ
   */
  @ApiProperty({
    description: 'Danh sách ID của Zalo Official Accounts cần gỡ',
    example: [1, 2, 3],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  @IsNotEmpty()
  zaloOfficialAccountIds: number[];
}

/**
 * DTO response cho thông tin Zalo Official Account trong agent
 */
export class AgentZaloOfficialAccountResponseDto {
  /**
   * ID của Official Account trong hệ thống
   */
  @ApiProperty({
    description: 'ID của Official Account trong hệ thống',
    example: 1,
  })
  id: number;

  /**
   * ID của Official Account trên Zalo
   */
  @ApiProperty({
    description: 'ID của Official Account trên Zalo',
    example: '*********',
  })
  oaId: string;

  /**
   * Tên của Official Account
   */
  @ApiProperty({
    description: 'Tên của Official Account',
    example: 'RedAI Official',
  })
  name: string;

  /**
   * Mô tả của Official Account
   */
  @ApiPropertyOptional({
    description: 'Mô tả của Official Account',
    example: 'Kênh chính thức của RedAI',
  })
  description?: string;

  /**
   * URL avatar của Official Account
   */
  @ApiPropertyOptional({
    description: 'URL avatar của Official Account',
    example: 'https://zalo.me/avatar/*********.jpg',
  })
  avatarUrl?: string;

  /**
   * Trạng thái kết nối
   */
  @ApiProperty({
    description: 'Trạng thái kết nối',
    example: 'active',
  })
  status: string;

  /**
   * Thời điểm tạo (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: *************,
  })
  createdAt: number;

  /**
   * Thời điểm cập nhật (Unix timestamp)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: *************,
  })
  updatedAt: number;
}

/**
 * DTO response cho kết quả thêm/gỡ Zalo Official Accounts
 */
export class ZaloOfficialAccountOperationResultDto {
  /**
   * Số lượng đã thêm/gỡ thành công
   */
  @ApiProperty({
    description: 'Số lượng đã thêm/gỡ thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số lượng bị bỏ qua (đã tồn tại hoặc không hợp lệ)
   */
  @ApiProperty({
    description: 'Số lượng bị bỏ qua (đã tồn tại hoặc không hợp lệ)',
    example: 1,
  })
  skippedCount: number;

  /**
   * Danh sách ID đã xử lý thành công
   */
  @ApiProperty({
    description: 'Danh sách ID đã xử lý thành công',
    example: [1, 2],
    type: [Number],
  })
  successIds: number[];

  /**
   * Danh sách ID bị bỏ qua với lý do
   */
  @ApiProperty({
    description: 'Danh sách ID bị bỏ qua với lý do',
    example: [{ id: 3, reason: 'Không tìm thấy hoặc không thuộc về user' }],
  })
  skippedItems: Array<{
    id: number;
    reason: string;
  }>;
}
