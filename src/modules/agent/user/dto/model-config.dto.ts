import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsOptional, 
  IsN<PERSON><PERSON>, 
  Min, 
  Max 
} from 'class-validator';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';

/**
 * DTO cho cấu hình model của agent
 */
export class ModelConfigDto implements ModelConfig {
  /**
   * Giá trị temperature cho model (0-2)
   */
  @ApiPropertyOptional({
    description: 'Giá trị temperature cho model (0-2)',
    example: 0.7,
    minimum: 0,
    maximum: 2,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Temperature phải là số' })
  @Min(0, { message: 'Temperature phải >= 0' })
  @Max(2, { message: 'Temperature phải <= 2' })
  @Type(() => Number)
  temperature?: number;

  /**
   * Giá trị top_p cho model (0-1)
   */
  @ApiPropertyOptional({
    description: '<PERSON>i<PERSON> trị top_p cho model (0-1)',
    example: 0.9,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Top_p phải là số' })
  @Min(0, { message: 'Top_p phải >= 0' })
  @Max(1, { message: 'Top_p phải <= 1' })
  @Type(() => Number)
  top_p?: number;

  /**
   * Giá trị top_k cho model
   */
  @ApiPropertyOptional({
    description: 'Giá trị top_k cho model',
    example: 40,
    minimum: 0,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Top_k phải là số' })
  @Min(0, { message: 'Top_k phải >= 0' })
  @Type(() => Number)
  top_k?: number;

  /**
   * Số token tối đa cho kết quả
   */
  @ApiPropertyOptional({
    description: 'Số token tối đa cho kết quả',
    example: 1000,
    minimum: 1,
  })
  @IsOptional()
  @IsNumber({}, { message: 'Max_tokens phải là số' })
  @Min(1, { message: 'Max_tokens phải >= 1' })
  @Type(() => Number)
  max_tokens?: number;
}
