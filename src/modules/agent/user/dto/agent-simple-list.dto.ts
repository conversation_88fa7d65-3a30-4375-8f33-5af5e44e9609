import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { QueryDto } from '@common/dto/query.dto';
import { IsOptional, IsEnum } from 'class-validator';

/**
 * Enum cho các trường sắp xếp của agent simple list
 */
export enum AgentSimpleSortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách agent đơn giản
 */
export class AgentSimpleQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentSimpleSortBy,
    default: AgentSimpleSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentSimpleSortBy)
  sortBy?: AgentSimpleSortBy = AgentSimpleSortBy.CREATED_AT;
}

/**
 * DTO đơn giản cho danh sách agent
 */
export class AgentSimpleListDto {
  /**
   * ID của agent
   */
  @ApiProperty({
    description: 'ID của agent',
    example: 'uuid-string',
  })
  id: string;

  /**
   * Avatar của agent (đã gán CDN URL)
   */
  @ApiProperty({
    description: 'Avatar của agent với CDN URL',
    example: 'https://cdn.example.com/avatars/agent-avatar.jpg',
    required: false,
  })
  avatar?: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'Agent AI Assistant',
  })
  name: string;
}
