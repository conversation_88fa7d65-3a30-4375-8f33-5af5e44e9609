import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsEnum, IsString } from 'class-validator';
import { Type } from 'class-transformer';
import { QueryDto, SortDirection } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của website trong agent
 */
export enum AgentWebsiteSortBy {
  WEBSITE_NAME = 'websiteName',
  HOST = 'host',
  CREATED_AT = 'createdAt',
  VERIFY = 'verify',
}

/**
 * DTO cho query parameters khi lấy danh sách website trong agent
 */
export class AgentWebsiteQueryDto extends QueryDto {
  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentWebsiteSortBy,
    default: AgentWebsiteSortBy.CREATED_AT,
  })
  @IsOptional()
  @IsEnum(AgentWebsiteSortBy)
  sortBy?: AgentWebsiteSortBy = AgentWebsiteSortBy.CREATED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;
}
