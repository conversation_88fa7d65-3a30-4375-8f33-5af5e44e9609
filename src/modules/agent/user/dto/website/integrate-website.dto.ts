import { ApiProperty } from '@nestjs/swagger';
import { IsUUID, IsArray, ArrayMaxSize, ArrayMinSize } from 'class-validator';

/**
 * DTO cho việc tích hợp danh sách Website với Agent
 */
export class IntegrateWebsiteDto {
  /**
   * <PERSON><PERSON> sách UUID của các Website trong hệ thống cần tích hợp
   */
  @ApiProperty({
    description: 'Danh sách UUID của các Website trong hệ thống cần tích hợp',
    example: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      'b2c3d4e5-f6g7-8901-bcde-f23456789012'
    ],
    type: [String],
    isArray: true
  })
  @IsArray({
    message: 'Website IDs phải là array'
  })
  @ArrayMinSize(1, {
    message: '<PERSON><PERSON><PERSON> có ít nhất 1 website ID'
  })
  @ArrayMaxSize(10, {
    message: '<PERSON><PERSON><PERSON><PERSON> đượ<PERSON> v<PERSON> quá 10 website IDs'
  })
  @IsUUID('4', {
    each: true,
    message: 'Mỗi website ID phải là UUID hợp lệ'
  })
  websiteIds: string[];
}
