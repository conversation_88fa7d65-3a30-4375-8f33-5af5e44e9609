import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { TypeAgentConfig } from '@modules/agent/interfaces/type-agent-config.interface';
import { TypeAgentToolDto } from './type-agent-tool.dto';

/**
 * DTO cho thông tin cơ bản của loại agent trong danh sách
 */
export class TypeAgentListItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * <PERSON><PERSON><PERSON> hình mặc định cho loại agent
   */
  @ApiProperty({
    description: 'Cấu hình mặc định cho loại agent - Tự động đồng bộ với TypeAgentConfig interface',
    example: {
      enableAgentProfileCustomization: true,
      enableOutputToMessenger: true,
      enableOutputToWebsiteLiveChat: true,
      enableTaskConversionTracking: false,
      enableResourceUsage: true,
      enableDynamicStrategyExecution: true,
      enableMultiAgentCollaboration: false,
      enableOutputToZaloOA: true,
    },
  })
  config: TypeAgentConfig;

  /**
   * Số lượng tool của loại agent
   */
  @ApiProperty({
    description: 'Số lượng tool của loại agent',
    example: 5,
  })
  countTool: number;

  /**
   * Thời điểm tạo (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm tạo (timestamp millis)',
    example: 1672531200000,
  })
  createdAt: number;
}



/**
 * DTO cho thông tin chi tiết của loại agent
 */
export class TypeAgentDetailDto extends TypeAgentListItemDto {
  /**
     * Thời điểm cập nhật (timestamp millis)
     */
  @ApiProperty({
    description: 'Thời điểm cập nhật (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;

  /**
   * Danh sách tool của loại agent
   */
  @ApiProperty({
    description: 'Danh sách tool của loại agent',
    type: [TypeAgentToolDto],
  })
  tools: TypeAgentToolDto[];
}
