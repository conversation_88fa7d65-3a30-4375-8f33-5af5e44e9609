import { ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsOptional, 
  IsString, 
  IsArray, 
  IsNumber, 
  IsEnum,
  ArrayMaxSize,
  Min,
  Max
} from 'class-validator';
import { GenderEnum } from '@modules/agent/constants/gender.enum';

/**
 * DTO cho việc cập nhật profile của agent
 */
export class UpdateProfileDto {
  /**
   * Giới tính
   */
  @ApiPropertyOptional({
    description: 'Giới tính của agent',
    example: 'MALE',
    enum: GenderEnum,
  })
  @IsOptional()
  @IsEnum(GenderEnum, { message: 'Giới tính phải là một trong các giá trị hợp lệ' })
  gender?: string;

  /**
   * <PERSON><PERSON><PERSON> sinh (timestamp millis)
   */
  @ApiPropertyOptional({
    description: '<PERSON><PERSON><PERSON> sinh (timestamp millis)',
    example: 946684800000,
    minimum: 0,
    maximum: Date.now(),
  })
  @IsOptional()
  @IsNumber({}, { message: '<PERSON><PERSON><PERSON> sinh phải là số' })
  @Type(() => Number)
  @Min(0, { message: '<PERSON><PERSON><PERSON> sinh không hợp lệ' })
  @Max(Date.now(), { message: 'Ngày sinh không thể trong tương lai' })
  dateOfBirth?: number;

  /**
   * Vị trí/Chức vụ
   */
  @ApiPropertyOptional({
    description: 'Vị trí hoặc chức vụ của agent',
    example: 'Trợ lý AI chuyên về marketing',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Vị trí phải là chuỗi' })
  position?: string;

  /**
   * Trình độ học vấn
   */
  @ApiPropertyOptional({
    description: 'Trình độ học vấn của agent',
    example: 'Thạc sĩ Khoa học Máy tính',
    maxLength: 255,
  })
  @IsOptional()
  @IsString({ message: 'Học vấn phải là chuỗi' })
  education?: string;

  /**
   * Danh sách kỹ năng
   */
  @ApiPropertyOptional({
    description: 'Danh sách kỹ năng của agent (tối đa 20 kỹ năng)',
    example: ['Trả lời câu hỏi', 'Tìm kiếm thông tin', 'Phân tích dữ liệu'],
    type: [String],
    maxItems: 20,
  })
  @IsOptional()
  @IsArray({ message: 'Kỹ năng phải là mảng' })
  @IsString({ each: true, message: 'Mỗi kỹ năng phải là chuỗi' })
  @ArrayMaxSize(20, { message: 'Không được vượt quá 20 kỹ năng' })
  skills?: string[];

  /**
   * Danh sách tính cách
   */
  @ApiPropertyOptional({
    description: 'Danh sách tính cách của agent (tối đa 15 tính cách)',
    example: ['Thân thiện', 'Kiên nhẫn', 'Sáng tạo'],
    type: [String],
    maxItems: 15,
  })
  @IsOptional()
  @IsArray({ message: 'Tính cách phải là mảng' })
  @IsString({ each: true, message: 'Mỗi tính cách phải là chuỗi' })
  @ArrayMaxSize(15, { message: 'Không được vượt quá 15 tính cách' })
  personality?: string[];

  /**
   * Danh sách ngôn ngữ
   */
  @ApiPropertyOptional({
    description: 'Danh sách ngôn ngữ mà agent có thể sử dụng (tối đa 10 ngôn ngữ)',
    example: ['Tiếng Việt', 'Tiếng Anh', 'Tiếng Nhật'],
    type: [String],
    maxItems: 10,
  })
  @IsOptional()
  @IsArray({ message: 'Ngôn ngữ phải là mảng' })
  @IsString({ each: true, message: 'Mỗi ngôn ngữ phải là chuỗi' })
  @ArrayMaxSize(10, { message: 'Không được vượt quá 10 ngôn ngữ' })
  languages?: string[];

  /**
   * Quốc gia/Khu vực
   */
  @ApiPropertyOptional({
    description: 'Quốc gia hoặc khu vực mà agent phục vụ',
    example: 'Việt Nam',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Quốc gia phải là chuỗi' })
  nations?: string;
}
