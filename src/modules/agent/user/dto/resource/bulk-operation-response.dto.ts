import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO cho kết quả bulk operation với URL
 */
export class BulkUrlOperationResponseDto {
  /**
   * Danh sách ID URL thành công
   */
  @ApiProperty({
    description: 'Danh sách ID URL thành công',
    example: ['url-1', 'url-2'],
    type: [String],
  })
  idSuccess: string[];

  /**
   * Danh sách ID URL thất bại
   */
  @ApiProperty({
    description: 'Danh sách ID URL thất bại',
    example: ['url-3'],
    type: [String],
  })
  idFailed: string[];

  /**
   * Chi tiết lỗi cho từng ID thất bại
   */
  @ApiProperty({
    description: 'Chi tiết lỗi cho từng ID thất bại',
    example: { 'url-3': 'URL không tồn tại hoặc không thuộc về user' },
    type: 'object',
    additionalProperties: { type: 'string' },
  })
  errors: Record<string, string>;

  /**
   * Tổng số items được xử lý
   */
  @ApiProperty({
    description: 'Tổng số items được xử lý',
    example: 3,
  })
  totalProcessed: number;

  /**
   * Số items thành công
   */
  @ApiProperty({
    description: 'Số items thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số items thất bại
   */
  @ApiProperty({
    description: 'Số items thất bại',
    example: 1,
  })
  failedCount: number;
}

/**
 * DTO cho kết quả bulk operation với Media
 */
export class BulkMediaOperationResponseDto {
  /**
   * Danh sách ID Media thành công
   */
  @ApiProperty({
    description: 'Danh sách ID Media thành công',
    example: ['media-1', 'media-2'],
    type: [String],
  })
  idSuccess: string[];

  /**
   * Danh sách ID Media thất bại
   */
  @ApiProperty({
    description: 'Danh sách ID Media thất bại',
    example: ['media-3'],
    type: [String],
  })
  idFailed: string[];

  /**
   * Chi tiết lỗi cho từng ID thất bại
   */
  @ApiProperty({
    description: 'Chi tiết lỗi cho từng ID thất bại',
    example: { 'media-3': 'Media không tồn tại hoặc không thuộc về user' },
    type: 'object',
    additionalProperties: { type: 'string' },
  })
  errors: Record<string, string>;

  /**
   * Tổng số items được xử lý
   */
  @ApiProperty({
    description: 'Tổng số items được xử lý',
    example: 3,
  })
  totalProcessed: number;

  /**
   * Số items thành công
   */
  @ApiProperty({
    description: 'Số items thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số items thất bại
   */
  @ApiProperty({
    description: 'Số items thất bại',
    example: 1,
  })
  failedCount: number;
}

/**
 * DTO cho kết quả bulk operation với Product
 */
export class BulkProductOperationResponseDto {
  /**
   * Danh sách ID Product thành công
   */
  @ApiProperty({
    description: 'Danh sách ID Product thành công',
    example: [1, 2],
    type: [Number],
  })
  idSuccess: number[];

  /**
   * Danh sách ID Product thất bại
   */
  @ApiProperty({
    description: 'Danh sách ID Product thất bại',
    example: [3],
    type: [Number],
  })
  idFailed: number[];

  /**
   * Chi tiết lỗi cho từng ID thất bại
   */
  @ApiProperty({
    description: 'Chi tiết lỗi cho từng ID thất bại',
    example: { '3': 'Product không tồn tại hoặc không thuộc về user' },
    type: 'object',
    additionalProperties: { type: 'string' },
  })
  errors: Record<string, string>;

  /**
   * Tổng số items được xử lý
   */
  @ApiProperty({
    description: 'Tổng số items được xử lý',
    example: 3,
  })
  totalProcessed: number;

  /**
   * Số items thành công
   */
  @ApiProperty({
    description: 'Số items thành công',
    example: 2,
  })
  successCount: number;

  /**
   * Số items thất bại
   */
  @ApiProperty({
    description: 'Số items thất bại',
    example: 1,
  })
  failedCount: number;
}
