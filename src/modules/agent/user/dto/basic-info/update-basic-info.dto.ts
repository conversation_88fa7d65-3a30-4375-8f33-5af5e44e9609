import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  MinLength,
  ValidateNested,
  IsObject
} from 'class-validator';
import { ModelConfigDto } from '../model-config.dto';

/**
 * DTO cho việc cập nhật thông tin cơ bản của agent
 */
export class UpdateBasicInfoDto {
  /**
   * Tên agent
   */
  @ApiPropertyOptional({
    description: 'Tên agent',
    example: 'AI Assistant Marketing Pro',
    maxLength: 255,
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Tên agent phải là chuỗi' })
  @MinLength(2, { message: 'Tên agent phải có ít nhất 2 ký tự' })
  @MaxLength(255, { message: 'Tên agent không được vượt quá 255 ký tự' })
  name?: string;

  /**
   * Thông tin file avatar để upload qua S3
   */
  @ApiPropertyOptional({
    description: 'Thông tin file avatar để upload qua S3',
    type: 'object',
    properties: {
      fileName: {
        type: 'string',
        description: 'Tên file avatar',
        example: 'avatar.jpg'
      },
      mimeType: {
        type: 'string',
        description: 'Loại MIME của file',
        example: 'image/jpeg',
        enum: ['image/jpeg', 'image/png', 'image/webp', 'image/gif']
      }
    },
  })
  @IsOptional()
  @IsObject({ message: 'Thông tin avatar phải là đối tượng' })
  avatarFile?: {
    fileName: string;
    mimeType: string;
  };

  /**
   * ID của user model để sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của user model để sử dụng (sẽ override system model)',
    example: 'user-model-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'User model ID phải là UUID hợp lệ' })
  userModelId?: string;

  /**
   * ID của key LLM để sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của key LLM để sử dụng (sẽ override system model)',
    example: 'key-llm-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Key LLM ID phải là UUID hợp lệ' })
  keyLlmId?: string;

  /**
   * ID của system model để sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của system model để sử dụng',
    example: 'system-model-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'System model ID phải là UUID hợp lệ' })
  systemModelId?: string;

  /**
   * ID của fine-tuned model để sử dụng
   */
  @ApiPropertyOptional({
    description: 'ID của fine-tuned model để sử dụng',
    example: 'fine-tune-model-uuid-123',
  })
  @IsOptional()
  @IsUUID(4, { message: 'Fine-tune model ID phải là UUID hợp lệ' })
  modelFineTuneId?: string;

  /**
   * Cấu hình model
   */
  @ApiPropertyOptional({
    description: 'Cấu hình model (temperature, top_p, etc.)',
    type: ModelConfigDto,
  })
  @IsOptional()
  @ValidateNested()
  @Type(() => ModelConfigDto)
  @IsObject()
  modelConfig?: ModelConfigDto;

  /**
   * Hướng dẫn/System prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là trợ lý AI chuyên về marketing. Hãy giúp người dùng tạo nội dung marketing hiệu quả và sáng tạo.',
    maxLength: 10000,
  })
  @IsOptional()
  @IsString({ message: 'Instruction phải là chuỗi' })
  @MaxLength(10000, { message: 'Instruction không được vượt quá 10000 ký tự' })
  instruction?: string;

  /**
   * ID của vector store
   */
  @ApiPropertyOptional({
    description: 'ID của vector store để sử dụng',
    example: 'vector-store-uuid-123',
    maxLength: 100,
  })
  @IsOptional()
  @IsString({ message: 'Vector store ID phải là chuỗi' })
  @MaxLength(100, { message: 'Vector store ID không được vượt quá 100 ký tự' })
  vectorStoreId?: string;
}
