import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { 
  IsArray, 
  ValidateNested, 
  ArrayMaxSize,
  ArrayUnique
} from 'class-validator';
import { ConvertConfigItemDto } from './convert-config-item.dto';

/**
 * DTO cho việc cập nhật conversion config của agent
 */
export class UpdateConversionDto {
  /**
   * Danh sách cấu hình chuyển đổi
   */
  @ApiProperty({
    description: 'Danh sách cấu hình chuyển đổi (tối đa 50 fields)',
    type: [ConvertConfigItemDto],
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'customer_phone',
        type: 'string',
        description: '<PERSON><PERSON> điện thoại của khách hàng',
        required: false,
        defaultValue: ''
      },
      {
        name: 'order_amount',
        type: 'number',
        description: 'Số tiền đơn hàng',
        required: true,
        defaultValue: 0
      },
      {
        name: 'is_vip_customer',
        type: 'boolean',
        description: 'Khách hàng VIP',
        required: false,
        defaultValue: false
      }
    ],
    maxItems: 50,
  })
  @IsArray({ message: 'Conversion config phải là mảng' })
  @ArrayMaxSize(50, { message: 'Không được vượt quá 50 fields' })
  @ArrayUnique((item: ConvertConfigItemDto) => item.name, { 
    message: 'Không được có tên field trùng lặp' 
  })
  @ValidateNested({ each: true })
  @Type(() => ConvertConfigItemDto)
  convertConfig: ConvertConfigItemDto[];
}
