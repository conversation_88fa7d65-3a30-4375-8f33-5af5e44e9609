import { ApiProperty } from '@nestjs/swagger';
import { ConvertConfigItemDto } from './convert-config-item.dto';

/**
 * DTO cho response thông tin conversion config của agent
 */
export class ConversionResponseDto {
  /**
   * <PERSON>h sách cấu hình chuyển đổi
   */
  @ApiProperty({
    description: 'Danh sách cấu hình chuyển đổi',
    type: [ConvertConfigItemDto],
    example: [
      {
        name: 'customer_name',
        type: 'string',
        description: 'Tên đầy đủ của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'customer_email',
        type: 'string',
        description: 'Email của khách hàng',
        required: true,
        defaultValue: ''
      },
      {
        name: 'order_amount',
        type: 'number',
        description: 'Số tiền đơn hàng',
        required: true,
        defaultValue: 0
      }
    ],
  })
  convertConfig: ConvertConfigItemDto[];

  /**
   * <PERSON><PERSON> lượng fields trong conversion config
   */
  @ApiProperty({
    description: '<PERSON><PERSON> lượng fields trong conversion config',
    example: 5,
  })
  totalFields: number;

  /**
   * <PERSON><PERSON> lượng fields bắt buộc
   */
  @ApiProperty({
    description: 'Số lượng fields bắt buộc',
    example: 3,
  })
  requiredFields: number;

  /**
   * Thời điểm cập nhật conversion config gần nhất (timestamp millis)
   */
  @ApiProperty({
    description: 'Thời điểm cập nhật conversion config gần nhất (timestamp millis)',
    example: 1672531200000,
  })
  updatedAt: number;
}
