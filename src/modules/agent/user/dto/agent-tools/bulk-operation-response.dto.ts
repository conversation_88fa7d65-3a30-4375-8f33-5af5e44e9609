import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO response cho các thao tác bulk (thêm/gỡ nhiều tools)
 */
export class BulkOperationResponseDto {
  /**
   * Số lượng tools đã được xử lý thành công
   */
  @ApiProperty({
    description: 'Số lượng tools đã được xử lý thành công',
    example: 3,
  })
  processedCount: number;

  /**
   * Số lượng tools bị bỏ qua (đã tồn tại hoặc không tồn tại)
   */
  @ApiProperty({
    description: 'Số lượng tools bị bỏ qua',
    example: 1,
  })
  skippedCount: number;

  /**
   * Danh sách ID của các tools bị bỏ qua
   */
  @ApiProperty({
    description: 'Danh sách ID của các tools bị bỏ qua',
    type: [String],
    example: ['550e8400-e29b-41d4-a716-446655440001'],
  })
  skippedIds: string[];

  /**
   * Tổng số tools được yêu cầu xử lý
   */
  @ApiProperty({
    description: 'Tổng số tools được yêu cầu xử lý',
    example: 4,
  })
  totalRequested: number;
}
