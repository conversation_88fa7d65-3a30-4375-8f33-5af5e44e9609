import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';

/**
 * DTO cho phản hồi thông tin agent strategy user
 */
export class AgentStrategyUserResponseDto {
  /**
   * UUID duy nhất định danh bản ghi agents_strategy_user
   */
  @ApiProperty({
    description: 'UUID duy nhất định danh bản ghi agents_strategy_user',
    example: 'strategy-user-uuid-123',
  })
  id: string;

  /**
   * Thời điểm người dùng sở hữu strategy, tính theo epoch time (milliseconds)
   */
  @ApiProperty({
    description: 'Thời điểm người dùng sở hữu strategy, tính theo epoch time (milliseconds)',
    example: 1640995200000,
  })
  ownedAt: number;

  /**
   * Tên của strategy (từ bảng agents)
   */
  @ApiPropertyOptional({
    description: 'Tên của strategy (từ bảng agents)',
    example: 'Customer Support Strategy',
  })
  strategyName?: string;
}

export class AgentStrategyUserDetailResponseDto extends AgentStrategyUserResponseDto {
  /**
   * Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent
   */
  @ApiProperty({
    description: 'Danh sách ví dụ (example) tuỳ chỉnh theo người dùng cho strategy agent',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ tùy chỉnh: Khi người dùng hỏi về sản phẩm' },
      { stepOrder: 2, content: 'Ví dụ tùy chỉnh: Khi người dùng cần hỗ trợ thanh toán' }
    ]
  })
  example: IStrategyContentStep[];
}