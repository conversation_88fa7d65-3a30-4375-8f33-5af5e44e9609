import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional, IsString, IsUUID } from 'class-validator';
import { QueryDto } from '@common/dto';

/**
 * DTO cho việc truy vấn danh sách agent strategy user
 */
export class QueryAgentStrategyUserDto extends QueryDto {
  /**
   * Lọc theo agents strategy ID
   */
  @ApiPropertyOptional({
    description: 'Lọc theo agents strategy ID',
    example: 'agent-strategy-uuid-123',
  })
  @IsString()
  @IsOptional()
  agentsStrategyId?: string;

  /**
   * Lọc theo user model ID
   */
  @ApiPropertyOptional({
    description: 'Lọc theo user model ID',
    example: 'user-model-uuid-123',
  })
  @IsUUID()
  @IsOptional()
  userModelId?: string;

  /**
   * Lọc theo key LLM ID
   */
  @ApiPropertyOptional({
    description: 'Lọc theo key LLM ID',
    example: 'key-llm-uuid-123',
  })
  @IsUUID()
  @IsOptional()
  keyLlmId?: string;
}
