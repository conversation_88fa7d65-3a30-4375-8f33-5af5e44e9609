import {
  Controller,
  Get,
  Put,
  Post,
  Param,
  Body,
  Query,
  UseGuards,
  Logger,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
} from '@nestjs/swagger';

import { JwtUserGuard } from '@modules/auth/guards';
import { CurrentUser } from '@modules/auth/decorators';
import { JWTPayload } from '@modules/auth/interfaces';
import { ApiResponseDto, PaginatedResult } from '@common/response';

import {
  QueryAgentStrategyUserDto,
  UpdateAgentStrategyUserDto,
  SetDefaultAgentStrategyUserDto,
  AgentStrategyUserResponseDto,
  AgentStrategyUserDetailResponseDto,
} from '../dto/agent-strategy-user';
import { AgentStrategyUserService } from '../services/agent-strategy-user.service';
import { SWAGGER_API_TAGS } from '@/common/swagger';
import { QueryDto } from '@common/dto';

/**
 * Controller xử lý các API liên quan đến agent strategy user
 */
@ApiTags(SWAGGER_API_TAGS.USER_AGENT_STRATEGY)
@Controller('user/agent-strategy')
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class AgentStrategyController {
  private readonly logger = new Logger(AgentStrategyController.name);

  constructor(
    private readonly agentStrategyUserService: AgentStrategyUserService,
  ) {}

  /**
   * Lấy danh sách strategy user theo userId
   */
  @Get()
  @ApiOperation({
    summary: 'Lấy danh sách strategy user',
    description: 'Lấy danh sách các strategy mà người dùng đang sở hữu',
  })
  @ApiResponse({
    status: 200,
    description: 'Lấy danh sách strategy user thành công',
    schema: ApiResponseDto.getPaginatedSchema(AgentStrategyUserResponseDto),
  })
  async getAgentStrategyUsers(
    @CurrentUser() user: JWTPayload,
    @Query() query: QueryDto,
  ): Promise<ApiResponseDto<PaginatedResult<AgentStrategyUserResponseDto>>> {
    try {
      const userId = typeof user.id === 'number' ? user.id : parseInt(user.id.toString());
      const result = await this.agentStrategyUserService.getAgentStrategyUsers(userId, query);

      return ApiResponseDto.paginated(
        result,
        'Lấy danh sách strategy user thành công',
      );
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách strategy user: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin strategy user
   */
  @Put(':id')
  @ApiOperation({
    summary: 'Cập nhật strategy user',
    description: 'Cập nhật thông tin của strategy user',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của strategy user',
    example: 'strategy-user-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Cập nhật strategy user thành công',
    schema: ApiResponseDto.getSchema(AgentStrategyUserDetailResponseDto),
  })
  async updateAgentStrategyUser(
    @CurrentUser() user: JWTPayload,
    @Param('id') id: string,
    @Body() updateDto: UpdateAgentStrategyUserDto,
  ): Promise<ApiResponseDto<AgentStrategyUserDetailResponseDto>> {
    try {
      this.logger.log(`Cập nhật strategy user ${id} cho user ${user.id}`);

      const userId = typeof user.id === 'number' ? user.id : parseInt(user.id.toString());
      const result = await this.agentStrategyUserService.updateAgentStrategyUser(id, userId, updateDto);

      return ApiResponseDto.success(
        result,
        'Cập nhật strategy user thành công',
      );
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật strategy user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set dữ liệu mặc định cho strategy user
   */
  @Post(':id/set-default')
  @ApiOperation({
    summary: 'Set dữ liệu mặc định cho strategy user',
    description: 'Thiết lập dữ liệu mặc định cho strategy user từ strategy gốc',
  })
  @ApiParam({
    name: 'id',
    description: 'ID của strategy user',
    example: 'strategy-user-uuid-123',
  })
  @ApiResponse({
    status: 200,
    description: 'Set dữ liệu mặc định thành công',
    schema: ApiResponseDto.getSchema(AgentStrategyUserDetailResponseDto),
  })
  async setDefaultAgentStrategyUser(
    @CurrentUser() user: JWTPayload,
    @Param('id') id: string,
    @Body() setDefaultDto: SetDefaultAgentStrategyUserDto,
  ): Promise<ApiResponseDto<AgentStrategyUserDetailResponseDto>> {
    try {
      this.logger.log(`Set default cho strategy user ${id} của user ${user.id}`);

      const userId = typeof user.id === 'number' ? user.id : parseInt(user.id.toString());
      const result = await this.agentStrategyUserService.setDefaultAgentStrategyUser(id, userId, setDefaultDto);

      return ApiResponseDto.success(
        result,
        'Set dữ liệu mặc định thành công',
      );
    } catch (error) {
      this.logger.error(`Lỗi khi set default cho strategy user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}