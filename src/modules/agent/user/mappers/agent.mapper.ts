import { Logger } from '@nestjs/common';


/**
 * Mapper cho các đối tượng Agent
 */
export class AgentMapper {
  private static readonly logger = new Logger(AgentMapper.name);

  /**
   * Chuyển đổi từ entity sang DTO danh sách
   * @param item Dữ liệu agent từ repository
   * @param cdnService Service để tạo URL CDN
   * @param agentRank Thông tin về rank của agent (nếu có)
   * @returns AgentListItemDto
   */
  // static toListItemDto(
  //   item: any,
  //   cdnService: CdnService,
  //   agentRank?: any
  // ): AgentListItemDto {
  //   try {
  //     // Tạo URL CDN cho avatar nếu có
  //     let avatarUrl = item.avatar || null;
  //     if (item.avatar) {
  //       try {
  //         avatarUrl = cdnService.generateUrlView(item.avatar, TimeIntervalEnum.ONE_DAY);
  //       } catch (error) {
  //         this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
  //       }
  //     }

  //     // Tạo URL CDN cho badge nếu có
  //     let badgeUrl = '';
  //     if (agentRank && agentRank.badge) {
  //       try {
  //         badgeUrl = cdnService.generateUrlView(agentRank.badge, TimeIntervalEnum.ONE_DAY) || '';
  //       } catch (error) {
  //         this.logger.warn(`Không thể tạo URL CDN cho badge: ${error.message}`);
  //       }
  //     }

  //     return {
  //       id: item.id,
  //       name: item.name,
  //       avatar: avatarUrl,
  //       typeId: item.typeId,
  //       typeName: item.typeName || '',
  //       active: item.active || false,
  //       createdAt: item.createdAt,
  //       updatedAt: item.updatedAt,
  //       // Thêm các trường mới
  //       exp: item.exp || 0,
  //       expMax: agentRank ? agentRank.maxExp : 100,
  //       level: agentRank ? agentRank.id : 1,
  //       badge_url: badgeUrl,
  //       model_id: item.resolvedModelId || '',
  //       provider_type: item.resolvedProvider || '',
  //     };
  //   } catch (error) {
  //     this.logger.error(`Lỗi khi xử lý thông tin agent ${item.id}: ${error.message}`);

  //     // Trả về thông tin cơ bản nếu có lỗi
  //     return {
  //       id: item.id,
  //       name: item.name,
  //       avatar: item.avatar || null,
  //       typeId: item.typeId || 0,
  //       typeName: item.typeName || '',
  //       active: item.active || false,
  //       createdAt: item.createdAt,
  //       updatedAt: item.updatedAt,
  //       // Thêm các trường mới với giá trị mặc định
  //       exp: 0,
  //       expMax: 100,
  //       level: 1,
  //       badge_url: '',
  //       model_id: '',
  //       provider_type: '',
  //     };
  //   }
  // }

  // /**
  //  * Chuyển đổi từ entity sang DTO chi tiết
  //  * @param agent Entity Agent
  //  * @param agentUser Entity AgentUser
  //  * @param cdnService Service để tạo URL CDN
  //  * @param providerType Loại provider
  //  * @returns AgentDetailDto
  //  */
  // static toDetailDto(
  //   agent: Agent,
  //   agentUser: AgentUser,
  //   cdnService: CdnService
  // ): AgentDetailDto {
  //   // Chuyển đổi ModelConfig sang ModelConfigDto
  //   const modelConfigDto = ModelConfigMapper.toDto(agent.modelConfig);

  //   // Chuyển đổi ProfileAgent sang ProfileDto
  //   const profileDto = ProfileMapper.toDto(agentUser.profile);

  //   // Tạo URL CDN cho avatar nếu có
  //   let avatarUrl = agent.avatar || null;
  //   if (agent.avatar) {
  //     try {
  //       avatarUrl = cdnService.generateUrlView(agent.avatar, TimeIntervalEnum.ONE_DAY);
  //     } catch (error) {
  //       this.logger.warn(`Không thể tạo URL CDN cho avatar: ${error.message}`);
  //     }
  //   }

  //   // Lấy tên của loại agent nếu có
  //   let typeName = '';
  //   if (agentUser.typeAgent && agentUser.typeAgent.name) {
  //     typeName = agentUser.typeAgent.name;
  //   }

  //   // Tạo vector store
  //   const vectorStores = agent.vectorStoreId ? {
  //     id: agent.vectorStoreId,
  //     name: agent.vectorStoreId // Using ID as name since we don't have the actual name
  //   } : undefined;

  //   return {
  //     id: agent.id,
  //     name: agent.name,
  //     avatar: avatarUrl,
  //     typeId: agentUser.typeId,
  //     typeName: typeName,
  //     modelConfig: modelConfigDto,
  //     instruction: agent.instruction || '',
  //     profile: profileDto,
  //     active: agentUser.active || false,
  //     vectorStores: vectorStores,
  //     createdAt: agent.createdAt,
  //     updatedAt: agent.updatedAt,
  //     // Thêm các trường mới
  //     exp: agentUser.exp || 0,
  //     expMax: 100, // Giá trị mặc định
  //     level: 1, // Giá trị mặc định
  //     badge_url: '', // Giá trị mặc định
  //     model_id: '',
  //   };
  // }
}
