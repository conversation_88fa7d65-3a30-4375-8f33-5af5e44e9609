import { GenderUtils } from '@modules/agent/constants/gender.enum';
import { ProfileAgent } from '@modules/agent/interfaces/profile-agent.interface';
import { ProfileDto } from '../dto/agent';

/**
 * Mapper cho các đối tượng Profile
 */
export class ProfileMapper {
  /**
   * <PERSON>y<PERSON>n đổi từ ProfileAgent sang ProfileDto
   * @param profile Thông tin profile của agent
   * @returns ProfileDto
   */
  static toDto(profile?: ProfileAgent): ProfileDto {
    return {
      gender: profile?.gender?.toString() || undefined,
      dateOfBirth: profile?.dateOfBirth instanceof Date
        ? profile.dateOfBirth.getTime()
        : (typeof profile?.dateOfBirth === 'string'
          ? new Date(profile.dateOfBirth).getTime()
          : undefined),
      position: profile?.position || undefined,
      education: profile?.education || undefined,
      skills: profile?.skills || undefined,
      personality: profile?.personality || undefined,
      languages: profile?.languages || undefined,
      nations: profile?.nations || undefined,
    };
  }

  /**
   * <PERSON>y<PERSON><PERSON> đổi từ ProfileDto sang ProfileAgent
   * @param profileDto DTO profile
   * @param currentProfile Profile hiện tại (nếu có)
   * @returns ProfileAgent
   */
  static fromDto(profileDto: ProfileDto, currentProfile?: ProfileAgent): ProfileAgent {
    return {
      ...currentProfile,
      gender: profileDto.gender ? GenderUtils.getGender(profileDto.gender) : undefined,
      dateOfBirth: profileDto.dateOfBirth ? new Date(profileDto.dateOfBirth) : undefined,
      position: profileDto.position,
      education: profileDto.education,
      skills: profileDto.skills,
      personality: profileDto.personality,
      languages: profileDto.languages,
      nations: profileDto.nations,
    };
  }
}
