import { UserMultiAgent } from '@modules/agent/entities';
import { Agent } from '@modules/agent/entities';
import { CdnService } from '@shared/services/cdn.service';
import { TimeIntervalEnum } from '@shared/utils';
import { MultiAgentResponseDto } from '../dto/multi-agent';

/**
 * Mapper cho Multi-Agent operations
 */
export class MultiAgentMapper {
  /**
   * Chuyển đổi UserMultiAgent entity với Agent info sang MultiAgentResponseDto
   * @param userMultiAgent UserMultiAgent entity
   * @param childAgent Agent entity của agent con
   * @param cdnService CDN service để tạo URL
   * @returns MultiAgentResponseDto
   */
  static toMultiAgentResponseDto(
    userMultiAgent: UserMultiAgent, 
    childAgent: Agent,
    cdnService: CdnService
  ): MultiAgentResponseDto {
    // Tạo URL CDN cho avatar
    let avatar = '';
    if (childAgent.avatar) {
      try {
        const generatedUrl = cdnService.generateUrlView(childAgent.avatar, TimeIntervalEnum.ONE_DAY);
        avatar = generatedUrl || '';
      } catch (error) {
        console.warn(`Không thể tạo URL CDN cho avatar agent ${childAgent.id}: ${error.message}`);
        avatar = '';
      }
    }

    return {
      childAgentId: userMultiAgent.childAgentId,
      name: childAgent.name,
      avatar,
      prompt: userMultiAgent.prompt || '',
    };
  }

  /**
   * Chuyển đổi danh sách UserMultiAgent entities sang danh sách MultiAgentResponseDto
   * @param userMultiAgents Danh sách UserMultiAgent entities
   * @param childAgents Map của child agents (key: agentId, value: Agent)
   * @param cdnService CDN service để tạo URL
   * @returns Danh sách MultiAgentResponseDto
   */
  static toMultiAgentResponseDtos(
    userMultiAgents: UserMultiAgent[], 
    childAgents: Map<string, Agent>,
    cdnService: CdnService
  ): MultiAgentResponseDto[] {
    return userMultiAgents.map(userMultiAgent => {
      const childAgent = childAgents.get(userMultiAgent.childAgentId);
      if (!childAgent) {
        // Fallback nếu không tìm thấy agent info
        return {
          childAgentId: userMultiAgent.childAgentId,
          name: 'Unknown Agent',
          avatar: '',
          prompt: userMultiAgent.prompt || '',
        };
      }
      return this.toMultiAgentResponseDto(userMultiAgent, childAgent, cdnService);
    });
  }
}
