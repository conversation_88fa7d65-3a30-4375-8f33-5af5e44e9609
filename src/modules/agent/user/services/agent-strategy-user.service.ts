import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';

import { AgentStrategyUserRepository } from '@modules/agent/repositories/agent-strategy-user.repository';
import { AppException } from '@common/exceptions';
import { STRATEGY_ERROR_CODES } from '@modules/agent/exceptions/strategy-error.code';
import { PaginatedResult } from '@common/response';
import {
  QueryAgentStrategyUserDto,
  UpdateAgentStrategyUserDto,
  SetDefaultAgentStrategyUserDto,
  AgentStrategyUserResponseDto,
  AgentStrategyUserDetailResponseDto,
} from '../dto/agent-strategy-user';
import { QueryDto } from '@common/dto';

/**
 * Service xử lý logic nghiệp vụ cho AgentStrategyUser
 */
@Injectable()
export class AgentStrategyUserService {
  private readonly logger = new Logger(AgentStrategyUserService.name);

  constructor(
    private readonly agentStrategyUserRepository: AgentStrategyUserRepository,
  ) {}

  /**
   * <PERSON><PERSON><PERSON> danh sách strategy user theo userId với phân trang
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách strategy user có phân trang
   */
  async getAgentStrategyUsers(
    userId: number,
    queryDto: QueryDto,
  ): Promise<PaginatedResult<AgentStrategyUserResponseDto>> {
    try {
      this.logger.log(`Lấy danh sách strategy user cho user ${userId}`);

      const result = await this.agentStrategyUserRepository.findPaginatedByUserId(userId, queryDto);

      // Transform dữ liệu sang DTO
      const items: AgentStrategyUserResponseDto[] = result.items.map(item => ({
        id: item.id,
        ownedAt: item.ownedAt,
        strategyName: item.strategyName,
      }));

      return {
        items,
        meta: result.meta,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi lấy danh sách strategy user cho user ${userId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Cập nhật thông tin strategy user
   * @param id ID của strategy user
   * @param userId ID của người dùng
   * @param updateDto Dữ liệu cập nhật
   * @returns Strategy user đã cập nhật
   */
  @Transactional()
  async updateAgentStrategyUser(
    id: string,
    userId: number,
    updateDto: UpdateAgentStrategyUserDto,
  ): Promise<AgentStrategyUserDetailResponseDto> {
    try {
      this.logger.log(`Cập nhật strategy user ${id} cho user ${userId}`);

      // Kiểm tra strategy user có tồn tại và thuộc về user không
      const existingStrategyUser = await this.agentStrategyUserRepository.findByIdAndUserId(id, userId);
      if (!existingStrategyUser) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Chuẩn bị dữ liệu cập nhật
      const updateData: Partial<any> = {};

      if (updateDto.example !== undefined) {
        updateData.example = updateDto.example;
      }

      // Thực hiện cập nhật
      await this.agentStrategyUserRepository.updateByIdAndUserId(id, userId, updateData);

      // Lấy lại thông tin đầy đủ sau khi cập nhật
      const updatedStrategyUser = await this.agentStrategyUserRepository.findDetailByIdAndUserId(id, userId);
      
      if (!updatedStrategyUser) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      this.logger.log(`Cập nhật strategy user ${id} thành công`);

      return {
        id: updatedStrategyUser.id,
        ownedAt: updatedStrategyUser.ownedAt,
        strategyName: updatedStrategyUser.strategyName,
        example: updatedStrategyUser.example,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi cập nhật strategy user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * Set dữ liệu mặc định cho strategy user
   * @param id ID của strategy user
   * @param userId ID của người dùng
   * @param setDefaultDto Dữ liệu mặc định
   * @returns Strategy user đã được set default
   */
  @Transactional()
  async setDefaultAgentStrategyUser(
    id: string,
    userId: number,
    setDefaultDto: SetDefaultAgentStrategyUserDto,
  ): Promise<AgentStrategyUserDetailResponseDto> {
    try {
      this.logger.log(`Set default cho strategy user ${id} của user ${userId}`);

      // Kiểm tra strategy user có tồn tại và thuộc về user không
      const existingStrategyUser = await this.agentStrategyUserRepository.findByIdAndUserId(id, userId);
      if (!existingStrategyUser) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      // Cập nhật example với dữ liệu mặc định
      const updateData = {
        example: setDefaultDto.example,
      };

      await this.agentStrategyUserRepository.updateByIdAndUserId(id, userId, updateData);

      // Lấy lại thông tin đầy đủ sau khi cập nhật
      const updatedStrategyUser = await this.agentStrategyUserRepository.findDetailByIdAndUserId(id, userId);
      
      if (!updatedStrategyUser) {
        throw new AppException(STRATEGY_ERROR_CODES.STRATEGY_NOT_FOUND);
      }

      this.logger.log(`Set default cho strategy user ${id} thành công`);

      return {
        id: updatedStrategyUser.id,
        ownedAt: updatedStrategyUser.ownedAt,
        strategyName: updatedStrategyUser.strategyName,
        example: updatedStrategyUser.example,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi set default cho strategy user ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }
}
