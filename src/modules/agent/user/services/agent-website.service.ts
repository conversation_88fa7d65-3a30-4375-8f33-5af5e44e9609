import { Injectable, Logger } from '@nestjs/common';
import { Transactional } from 'typeorm-transactional';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentUserRepository } from '@modules/agent/repositories';
import { UserWebsiteRepository } from '@modules/integration/repositories';
import {
  IntegrateWebsiteDto,
  IntegrateWebsitesResponseDto,
  AgentWebsiteDto,
  AgentWebsiteQueryDto
} from '../dto/website';
import { PaginatedResult } from '@common/response';

/**
 * Service xử lý tích hợp Website với Agent
 */
@Injectable()
export class AgentWebsiteService {
  private readonly logger = new Logger(AgentWebsiteService.name);

  constructor(
    private readonly agentUserRepository: AgentUserRepository,
    private readonly userWebsiteRepository: UserWebsiteRepository,
  ) {}

  /**
   * Tích hợp danh sách Website vào Agent
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param dto Danh sách Website cần tích hợp (UUID trong hệ thống)
   */
  @Transactional()
  async integrateWebsites(
    agentId: string,
    userId: number,
    dto: IntegrateWebsiteDto,
  ): Promise<IntegrateWebsitesResponseDto> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      const results: Array<{
        websiteId: string;
        status: 'integrated' | 'skipped' | 'error';
        error?: string;
      }> = [];

      let integratedCount = 0;
      let skippedCount = 0;

      // Xử lý từng website trong danh sách
      for (const websiteId of dto.websiteIds) {
        try {
          // Kiểm tra Website có tồn tại và thuộc về user không
          const website = await this.userWebsiteRepository.findByIdAndUserId(
            websiteId,
            userId
          );

          if (!website) {
            results.push({
              websiteId,
              status: 'error',
              error: 'Website không tồn tại hoặc không thuộc về người dùng'
            });
            continue;
          }

          // Kiểm tra Website đã được tích hợp với agent này chưa
          if (website.agentId === agentId) {
            results.push({
              websiteId,
              status: 'skipped'
            });
            skippedCount++;
            continue;
          }

          // Kiểm tra Website đã được tích hợp với agent khác chưa
          if (website.agentId && website.agentId !== agentId) {
            results.push({
              websiteId,
              status: 'error',
              error: 'Website đã được tích hợp với agent khác'
            });
            continue;
          }

          // Tích hợp Website với Agent (cập nhật agentId)
          try {
            await this.userWebsiteRepository.updateAgentId(website.id, agentId);
            results.push({
              websiteId,
              status: 'integrated'
            });
            integratedCount++;
          } catch (updateError) {
            const errorMessage = 'Không thể tích hợp website với agent';
            this.logger.error(`${errorMessage} ${websiteId}: ${updateError.message}`, updateError.stack);
            results.push({
              websiteId,
              status: 'error',
              error: errorMessage
            });
            // Throw error để dừng quá trình tích hợp
            throw new AppException(
              AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED,
              `${errorMessage}: ${updateError.message}`
            );
          }

        } catch (error) {
          this.logger.error(`Lỗi khi tích hợp website ${websiteId}: ${error.message}`);
          results.push({
            websiteId,
            status: 'error',
            error: error.message || 'Lỗi không xác định'
          });
          // Re-throw AppException để dừng quá trình
          if (error instanceof AppException) {
            throw error;
          }
        }
      }

      return {
        message: 'Tích hợp danh sách Website thành công',
        integratedCount,
        skippedCount,
        details: results
      };

    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi tích hợp danh sách Website với Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_INTEGRATION_FAILED);
    }
  }

  /**
   * Lấy danh sách Website trong Agent với phân trang
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   * @param queryDto Tham số truy vấn và phân trang
   * @returns Danh sách Website với phân trang
   */
  async getWebsites(
    agentId: string,
    userId: number,
    queryDto: AgentWebsiteQueryDto
  ): Promise<PaginatedResult<AgentWebsiteDto>> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      const { page, limit, search, sortBy, sortDirection } = queryDto;

      // Lấy danh sách Website đã tích hợp với Agent với phân trang
      const result = await this.userWebsiteRepository.findByAgentIdWithPagination(
        agentId,
        page,
        limit,
        search,
        sortBy,
        sortDirection
      );

      // Chuyển đổi sang DTO
      const websiteDtos: AgentWebsiteDto[] = result.items.map(website => ({
        id: website.id, // UUID trong hệ thống
        websiteName: website.websiteName,
        host: website.host,
        verify: website.verify,
        isActive: website.agentId !== null // Có agentId thì active
      }));

      return {
        items: websiteDtos,
        meta: result.meta
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi lấy danh sách Website: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_LIST_FAILED);
    }
  }



  /**
   * Gỡ Website khỏi Agent
   * @param agentId ID của Agent
   * @param websiteId UUID của Website trong hệ thống
   * @param userId ID của người dùng
   */
  @Transactional()
  async removeWebsite(
    agentId: string,
    websiteId: string,
    userId: number,
  ): Promise<void> {
    try {
      // Kiểm tra Agent có tồn tại và thuộc về user không
      await this.checkAgentOwnership(agentId, userId);

      // Kiểm tra Website có tồn tại và đã tích hợp với Agent không (sử dụng UUID)
      const website = await this.userWebsiteRepository.findByIdAndUserId(websiteId, userId);

      if (!website || website.agentId !== agentId) {
        throw new AppException(AGENT_ERROR_CODES.WEBSITE_NOT_INTEGRATED);
      }

      // Gỡ bỏ agentId khỏi Website
      await this.userWebsiteRepository.updateAgentId(website.id, null);
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }
      this.logger.error(`Lỗi khi gỡ Website khỏi Agent: ${error.message}`, error.stack);
      throw new AppException(AGENT_ERROR_CODES.WEBSITE_REMOVE_FAILED);
    }
  }

  /**
   * Kiểm tra Agent có tồn tại và thuộc về user không
   * @param agentId ID của Agent
   * @param userId ID của người dùng
   */
  private async checkAgentOwnership(agentId: string, userId: number): Promise<void> {
    const agentUser = await this.agentUserRepository.findAgentByIdAndUserId(agentId, userId);

    if (!agentUser) {
      throw new AppException(AGENT_ERROR_CODES.AGENT_NOT_FOUND);
    }
  }
}
