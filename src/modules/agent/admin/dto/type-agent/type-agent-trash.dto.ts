import { EmployeeInfoSimpleDto } from '@/modules/employee/dto/employee-info-simple.dto';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNumber } from 'class-validator';

/**
 * DTO cho thông tin type agent đã bị xóa mềm
 */
export class TypeAgentTrashItemDto {
  /**
   * ID của loại agent
   */
  @ApiProperty({
    description: 'ID của loại agent',
    example: 1,
  })
  id: number;

  /**
   * Tên loại agent
   */
  @ApiProperty({
    description: 'Tên loại agent',
    example: 'Chatbot Agent',
  })
  name: string;

  /**
   * <PERSON>ô tả chi tiết về loại agent
   */
  @ApiPropertyOptional({
    description: 'Mô tả chi tiết về loại agent',
    example: 'Loại agent hỗ trợ chat với người dùng',
  })
  description: string | null;

  /**
   * Thời gian tạo
   */
  @ApiProperty({
    description: 'Thời gian tạo (timestamp millis)',
    example: 1682506892000,
  })
  createdAt: number;

  /**
   * Thời gian xóa
   */
  @ApiProperty({
    description: 'Thời gian xóa (timestamp millis)',
    example: 1682506892000,
  })
  deletedAt: number;

  /**
   * Thông tin người xóa
   */
  @ApiProperty({
    description: 'Thông tin người xóa',
    type: EmployeeInfoSimpleDto,
  })
  deleted: EmployeeInfoSimpleDto;
}

/**
 * DTO cho việc xóa type agent với migration
 */
export class DeleteTypeAgentDto {
  /**
   * ID của type agent mới để chuyển các agents hiện tại
   */
  @ApiProperty({
    description: 'ID của type agent mới để chuyển các agents hiện tại',
    example: 2,
  })
  @IsNumber()
  newTypeAgentId: number;
}


/**
 * DTO cho việc khôi phục nhiều type agents
 */
export class BulkRestoreTypeAgentDto {
  /**
   * Danh sách ID của các type agents cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các type agents cần khôi phục',
    example: [1, 3, 5],
    type: [Number],
  })
  @IsArray()
  @IsNumber({}, { each: true })
  ids: number[];
}
