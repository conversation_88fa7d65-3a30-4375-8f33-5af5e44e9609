import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsNumber, Allow } from 'class-validator';

/**
 * DTO cho việc khôi phục nhiều type agent
 */
export class RestoreTypeAgentDto {
  /**
   * <PERSON>h sách ID của các type agent cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các type agent cần khôi phục',
    type: [Number],
    example: [1, 2, 3],
    isArray: true,
    items: {
      type: 'number'
    }
  })
  @Allow()
  @IsArray({ message: 'IDs phải là một mảng' })
  @IsNumber({}, { each: true, message: 'Mỗi ID phải là số hợp lệ' })
  @ArrayMinSize(1, { message: '<PERSON><PERSON>i có ít nhất 1 ID để khôi phục' })
  @IsNotEmpty({ message: '<PERSON><PERSON> sách IDs không được rỗng' })
  ids: number[];
}
