import { ApiProperty } from '@nestjs/swagger';
import { Allow, IsArray } from 'class-validator';

/**
 * DTO cho việc khôi phục agent system
 */
export class RestoreAgentSystemDto {
  /**
   * Danh sách ID của các agent system cần khôi phục
   */
  @ApiProperty({
    description: 'Danh sách ID của các agent system cần khôi phục',
    example: ['550e8400-e29b-41d4-a716-446655440000', '550e8400-e29b-41d4-a716-446655440001'],
  })
  @Allow()
  @IsArray({ message: 'IDs phải là một mảng' })
  ids: string[];
}
