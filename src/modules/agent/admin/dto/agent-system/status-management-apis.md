# Agent System Status Management APIs

## Overview
Hai APIs mới để quản lý trạng thái của agent systems:
1. **Toggle Active Status**: Tự động đảo ngược trạng thái active
2. **Set Supervisor**: Set agent làm supervisor (chỉ 1 supervisor duy nhất)

## API Specifications

### 1. PATCH /admin/agent-systems/:id/toggle-active

#### **Description**
Tự động đảo ngược trạng thái `active` của agent system (true ↔ false)

#### **Request**
```http
PATCH /admin/agent-systems/550e8400-e29b-41d4-a716-************/toggle-active
Authorization: Bearer <JWT_TOKEN>
```

#### **Response Success (200)**
```json
{
  "success": true,
  "message": "Trạng thái active đã được bật",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "active": true
  }
}
```

#### **Response Error (404)**
```json
{
  "success": false,
  "message": "Agent system không tồn tại",
  "errorCode": "AGENT_SYSTEM_NOT_FOUND",
  "data": null
}
```

### 2. PATCH /admin/agent-systems/:id/set-supervisor

#### **Description**
Set agent system làm supervisor. Chỉ có 1 supervisor duy nhất trong hệ thống.

#### **Request**
```http
PATCH /admin/agent-systems/550e8400-e29b-41d4-a716-************/set-supervisor
Authorization: Bearer <JWT_TOKEN>
```

#### **Response Success (200)**
```json
{
  "success": true,
  "message": "Agent system đã được set làm supervisor thành công",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "isSupervisor": true
  }
}
```

#### **Response Error (404)**
```json
{
  "success": false,
  "message": "Agent system không tồn tại",
  "errorCode": "AGENT_SYSTEM_NOT_FOUND",
  "data": null
}
```

## Implementation Details

### Repository Layer

#### **AgentSystemRepository Methods**

**1. toggleActiveStatus()**
```typescript
async toggleActiveStatus(id: string): Promise<boolean> {
  // Lấy trạng thái hiện tại
  const agentSystem = await this.findOne({ where: { id } });
  if (!agentSystem) {
    return false;
  }

  // Đảo ngược trạng thái
  const newActiveStatus = !agentSystem.active;
  
  // Cập nhật database
  await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ active: newActiveStatus })
    .where('id = :id', { id })
    .execute();

  return newActiveStatus;
}
```

**2. setSupervisor()**
```typescript
async setSupervisor(id: string): Promise<number> {
  // Bước 1: Set tất cả agents khác thành không phải supervisor
  await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ isSupervisor: false })
    .where('deletedBy IS NULL')
    .execute();

  // Bước 2: Set agent được chọn thành supervisor
  const result = await this.createQueryBuilder()
    .update(AgentSystem)
    .set({ isSupervisor: true })
    .where('id = :id', { id })
    .andWhere('deletedBy IS NULL')
    .execute();

  return result.affected || 0;
}
```

**3. existsAndNotDeleted()**
```typescript
async existsAndNotDeleted(id: string): Promise<boolean> {
  const count = await this.createQueryBuilder('as')
    .where('as.id = :id', { id })
    .andWhere('as.deletedBy IS NULL')
    .getCount();

  return count > 0;
}
```

### Service Layer

#### **AdminAgentSystemService Methods**

**1. toggleActiveStatus()**
```typescript
async toggleActiveStatus(id: string): Promise<{ id: string; active: boolean }> {
  // Validation
  const exists = await this.agentSystemRepository.existsAndNotDeleted(id);
  if (!exists) {
    throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  }

  // Business logic
  const newActiveStatus = await this.agentSystemRepository.toggleActiveStatus(id);

  return { id, active: newActiveStatus };
}
```

**2. setSupervisor()**
```typescript
async setSupervisor(id: string): Promise<{ id: string; isSupervisor: boolean }> {
  // Validation
  const exists = await this.agentSystemRepository.existsAndNotDeleted(id);
  if (!exists) {
    throw new AppException(AGENT_ERROR_CODES.AGENT_SYSTEM_NOT_FOUND);
  }

  // Business logic
  const affectedRows = await this.agentSystemRepository.setSupervisor(id);
  if (affectedRows === 0) {
    throw new AppException(AGENT_ERROR_CODES.AGENT_UPDATE_FAILED);
  }

  return { id, isSupervisor: true };
}
```

## Business Logic

### Toggle Active Status
1. **Validation**: Kiểm tra agent system tồn tại và chưa bị xóa
2. **Get Current State**: Lấy trạng thái `active` hiện tại
3. **Toggle**: Đảo ngược trạng thái (true → false, false → true)
4. **Update**: Cập nhật database
5. **Response**: Trả về ID và trạng thái mới

### Set Supervisor
1. **Validation**: Kiểm tra agent system tồn tại và chưa bị xóa
2. **Clear All**: Set `isSupervisor = false` cho tất cả agent systems
3. **Set New**: Set `isSupervisor = true` cho agent được chọn
4. **Verify**: Kiểm tra cập nhật thành công
5. **Response**: Trả về ID và trạng thái supervisor

## Database Operations

### Toggle Active Status
```sql
-- Step 1: Get current status
SELECT active FROM agents_system WHERE id = ? AND deleted_by IS NULL;

-- Step 2: Update with opposite status
UPDATE agents_system 
SET active = NOT active 
WHERE id = ? AND deleted_by IS NULL;
```

### Set Supervisor
```sql
-- Step 1: Clear all supervisors
UPDATE agents_system 
SET is_supervisor = false 
WHERE deleted_by IS NULL;

-- Step 2: Set new supervisor
UPDATE agents_system 
SET is_supervisor = true 
WHERE id = ? AND deleted_by IS NULL;
```

## Test Cases

### Toggle Active Status

#### **Test Case 1: Toggle from false to true**
- **Input**: Agent system với `active = false`
- **Expected**: `active = true`
- **Validation**: Database được cập nhật đúng

#### **Test Case 2: Toggle from true to false**
- **Input**: Agent system với `active = true`
- **Expected**: `active = false`
- **Validation**: Database được cập nhật đúng

#### **Test Case 3: Agent system không tồn tại**
- **Input**: Invalid agent system ID
- **Expected**: Error AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

#### **Test Case 4: Agent system đã bị xóa**
- **Input**: Deleted agent system ID
- **Expected**: Error AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

### Set Supervisor

#### **Test Case 1: Set supervisor thành công**
- **Input**: Valid agent system ID
- **Expected**: Agent được set làm supervisor, các agent khác không phải supervisor
- **Validation**: 
  - Target agent: `isSupervisor = true`
  - Other agents: `isSupervisor = false`

#### **Test Case 2: Thay đổi supervisor**
- **Setup**: Agent A đang là supervisor
- **Input**: Set Agent B làm supervisor
- **Expected**: Agent B thành supervisor, Agent A không còn là supervisor
- **Validation**: Chỉ có Agent B là supervisor

#### **Test Case 3: Agent system không tồn tại**
- **Input**: Invalid agent system ID
- **Expected**: Error AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

#### **Test Case 4: Agent system đã bị xóa**
- **Input**: Deleted agent system ID
- **Expected**: Error AGENT_SYSTEM_NOT_FOUND
- **Validation**: Status code 404

## Error Handling

### Common Errors
- `AGENT_SYSTEM_NOT_FOUND`: Agent system không tồn tại hoặc đã bị xóa
- `AGENT_UPDATE_FAILED`: Lỗi khi cập nhật database
- `INTERNAL_SERVER_ERROR`: Lỗi hệ thống không mong muốn

### Error Response Format
```json
{
  "success": false,
  "message": "Error message in Vietnamese",
  "errorCode": "ERROR_CODE",
  "data": null
}
```

## Security Considerations

1. **Authentication**: Yêu cầu JWT token hợp lệ
2. **Authorization**: Chỉ admin mới có quyền thực hiện
3. **Validation**: Kiểm tra UUID format cho ID parameter
4. **Soft Delete**: Chỉ thao tác trên records chưa bị xóa

## Performance Considerations

1. **Database Queries**: 
   - Toggle Active: 2 queries (SELECT + UPDATE)
   - Set Supervisor: 2 queries (UPDATE ALL + UPDATE ONE)
2. **Indexing**: Đảm bảo có index trên `id` và `deletedBy` columns
3. **Transaction**: Sử dụng transaction cho set supervisor để đảm bảo consistency

## Usage Examples

### Toggle Active Status
```bash
# Bật agent system
curl -X PATCH "http://localhost:3000/admin/agent-systems/550e8400-e29b-41d4-a716-************/toggle-active" \
  -H "Authorization: Bearer <JWT_TOKEN>"

# Response: {"success": true, "data": {"id": "...", "active": true}}
```

### Set Supervisor
```bash
# Set agent làm supervisor
curl -X PATCH "http://localhost:3000/admin/agent-systems/550e8400-e29b-41d4-a716-************/set-supervisor" \
  -H "Authorization: Bearer <JWT_TOKEN>"

# Response: {"success": true, "data": {"id": "...", "isSupervisor": true}}
```
