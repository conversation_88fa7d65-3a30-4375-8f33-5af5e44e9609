import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsEnum, IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { QueryDto } from '@common/dto/query.dto';

/**
 * Enum cho các trường sắp xếp của agent user
 */
export enum UserAgentSortBy {
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
  NAME = 'name',
  STATUS = 'status',
}

/**
 * DTO cho việc truy vấn danh sách agent của user
 */
export class UserAgentQueryDto extends QueryDto {
  /**
   * Lọc theo trạng thái
   */
  @ApiPropertyOptional({
    description: 'Lọc theo trạng thái',
    enum: AgentStatusEnum,
    example: AgentStatusEnum.PENDING,
  })
  @IsEnum(AgentStatusEnum, {
    message: 'status phải là một giá trị hợp lệ',
  })
  @IsOptional()
  status?: AgentStatusEnum;

  /**
   * Lọc theo ID người dùng
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID người dùng',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  userId?: number;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: UserAgentSortBy,
    example: UserAgentSortBy.CREATED_AT,
    default: UserAgentSortBy.CREATED_AT,
  })
  @IsEnum(UserAgentSortBy, {
    message: 'sortBy phải là một giá trị hợp lệ',
  })
  @IsOptional()
  sortBy: UserAgentSortBy = UserAgentSortBy.CREATED_AT;
}
