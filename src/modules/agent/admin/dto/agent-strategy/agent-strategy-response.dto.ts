import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IStrategyContentStep } from '@modules/agent/interfaces/strategy-content-step.interface';
import { ModelConfig } from '@modules/agent/interfaces/model-config.interface';
import { EmployeeInfoDto } from '../common';

export class AgentStrategyResponseDto {
  /**
   * ID của agent (khóa chính)
   */
  @ApiProperty({
    description: 'ID của agent (khóa chính)',
    example: 'd47ba20d-7c97-4ac0-9eb7-e5e7f5d74c4f',
  })
  id: string;

  /**
   * Tên của agent
   */
  @ApiProperty({
    description: 'Tên của agent',
    example: 'AI Assistant Strategy',
  })
  name: string;

  /**
   * Avatar URL của agent (CDN URL)
   */
  @ApiPropertyOptional({
    description: 'Avatar URL của agent (CDN URL)',
    example: 'https://cdn.redai.vn/agents/avatars/agent-avatar.jpg?expires=1750152880&signature=nGxQfY3rtfZAOrxl4MMkxMPLaBY',
    nullable: true,
  })
  avatar?: string | null;

  /**
   * Model ID được lấy từ system_models
   */
  @ApiProperty({
    description: 'Model ID được lấy từ system_models',
    example: 'gpt4o',
  })
  modelId: string;
}

/**
 * DTO cho phản hồi thông tin agent và chiến lược agent
 */
export class AgentStrategyDetailResponseDto extends AgentStrategyResponseDto {
  /**
   * Cấu hình AI model dạng JSONB
   */
  @ApiProperty({
    description: 'Cấu hình AI model dạng JSONB',
    example: { temperature: 0.7, max_tokens: 1000 },
  })
  modelConfig: ModelConfig;

  /**
   * Hướng dẫn hoặc system prompt cho agent
   */
  @ApiPropertyOptional({
    description: 'Hướng dẫn hoặc system prompt cho agent',
    example: 'Bạn là một trợ lý AI chuyên về chiến lược kinh doanh',
  })
  instruction?: string;

  /**
   * ID của kho vector sử dụng bởi agent
   */
  @ApiPropertyOptional({
    description: 'ID của kho vector sử dụng bởi agent',
    example: 'vector-store-123',
  })
  vectorStoreId?: string;

  /**
   * ID của system model được sử dụng
   */
  @ApiProperty({
    description: 'ID của system model được sử dụng',
    example: 'system-model-123',
  })
  modelSystemId: string;

  // ===== THÔNG TIN STRATEGY =====

  /**
   * Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON
   */
  @ApiProperty({
    description: 'Nội dung chiến lược của agent (cấu hình, rule...) dưới dạng JSON',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Bước đầu tiên: Phân tích yêu cầu' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Bước đầu tiên: Phân tích yêu cầu' },
      { stepOrder: 2, content: 'Bước hai: Xử lý thông tin' }
    ]
  })
  content: IStrategyContentStep[];

  /**
   * Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược
   */
  @ApiProperty({
    description: 'Các ví dụ mẫu để minh họa hoặc hướng dẫn chiến lược',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        stepOrder: { type: 'number', example: 1 },
        content: { type: 'string', example: 'Ví dụ: Khi người dùng hỏi về thời tiết' }
      }
    },
    example: [
      { stepOrder: 1, content: 'Ví dụ: Khi người dùng hỏi về thời tiết' },
      { stepOrder: 2, content: 'Ví dụ: Khi người dùng cần hỗ trợ kỹ thuật' }
    ]
  })
  exampleDefault: IStrategyContentStep[];

  /**
   * ID nhân viên tạo chiến lược
   */
  @ApiPropertyOptional({
    description: 'ID nhân viên tạo chiến lược',
    example: 123,
  })
  createdBy?: EmployeeInfoDto;

  /**
   * ID nhân viên chỉnh sửa gần nhất
   */
  @ApiPropertyOptional({
    description: 'ID nhân viên chỉnh sửa gần nhất',
    example: 456,
  })
  updatedBy?: EmployeeInfoDto;
}
