import { ApiPropertyOptional } from '@nestjs/swagger';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { IsEnum, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

/**
 * Enum cho các trường sắp xếp của agent strategy user
 */
export enum AgentStrategyUserSortBy {
  USER_ID = 'userId',
  OWNED_AT = 'ownedAt',
  CREATED_AT = 'createdAt',
}

/**
 * DTO cho việc truy vấn danh sách agent strategy user
 */
export class QueryAgentStrategyUserDto extends QueryDto {
  /**
   * Lọc theo ID người dùng
   */
  @ApiPropertyOptional({
    description: 'Lọc theo ID người dùng',
    example: 123,
  })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  userId?: number;

  /**
   * Lọc theo agents strategy ID
   */
  @ApiPropertyOptional({
    description: 'Lọc theo agents strategy ID',
    example: 'agent-strategy-uuid-123',
  })
  @IsString()
  @IsOptional()
  agentsStrategyId?: string;

  /**
   * Lọc theo user model ID
   */
  @ApiPropertyOptional({
    description: 'Lọc theo user model ID',
    example: 'user-model-uuid-123',
  })
  @IsString()
  @IsOptional()
  userModelId?: string;

  /**
   * Sắp xếp theo trường
   */
  @ApiPropertyOptional({
    description: 'Sắp xếp theo trường',
    enum: AgentStrategyUserSortBy,
    default: AgentStrategyUserSortBy.OWNED_AT,
  })
  @IsEnum(AgentStrategyUserSortBy)
  @IsOptional()
  sortBy?: AgentStrategyUserSortBy = AgentStrategyUserSortBy.OWNED_AT;

  /**
   * Hướng sắp xếp
   */
  @ApiPropertyOptional({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
  })
  @IsEnum(SortDirection)
  @IsOptional()
  @Type(() => String)
  sortDirection?: SortDirection = SortDirection.DESC;
}
