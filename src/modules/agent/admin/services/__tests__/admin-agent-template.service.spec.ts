import { Test, TestingModule } from '@nestjs/testing';
import { AdminAgentTemplateService } from '../admin-agent-template.service';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { AgentTemplateRepository } from '@modules/agent/repositories/agent-template.repository';
import { TypeAgentRepository } from '@modules/agent/repositories/type-agent.repository';
import { S3Service } from '@shared/services/s3.service';
import { AgentTemplateStatus } from '@modules/agent/constants';
import { AppException } from '@common/exceptions';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';

describe('AdminAgentTemplateService', () => {
  let service: AdminAgentTemplateService;
  let agentRepository: AgentRepository;
  let agentTemplateRepository: AgentTemplateRepository;
  let typeAgentRepository: TypeAgentRepository;
  let s3Service: S3Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AdminAgentTemplateService,
        {
          provide: AgentRepository,
          useValue: {
            findByName: jest.fn(),
            findById: jest.fn(),
            findByIds: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            softDelete: jest.fn(),
          },
        },
        {
          provide: AgentTemplateRepository,
          useValue: {
            findById: jest.fn(),
            findPaginated: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            update: jest.fn(),
            updateStatus: jest.fn(),
          },
        },
        {
          provide: TypeAgentRepository,
          useValue: {
            findById: jest.fn(),
          },
        },
        {
          provide: S3Service,
          useValue: {
            generateS3Key: jest.fn(),
            getPresignedUrl: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<AdminAgentTemplateService>(AdminAgentTemplateService);
    agentRepository = module.get<AgentRepository>(AgentRepository);
    agentTemplateRepository = module.get<AgentTemplateRepository>(AgentTemplateRepository);
    typeAgentRepository = module.get<TypeAgentRepository>(TypeAgentRepository);
    s3Service = module.get<S3Service>(S3Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated list of agent templates', async () => {
      const queryDto = {
        page: 1,
        limit: 10,
        status: AgentTemplateStatus.ACTIVE,
      };

      const templates = [
        { id: 'template-1', typeId: 1 },
        { id: 'template-2', typeId: 2 },
      ];

      const agents = [
        { id: 'template-1', name: 'Template 1', modelConfig: { modelId: 'gpt-4' } },
        { id: 'template-2', name: 'Template 2', modelConfig: { modelId: 'gpt-3.5' } },
      ];

      const paginatedResult = {
        items: templates,
        meta: {
          totalItems: 2,
          itemCount: 2,
          itemsPerPage: 10,
          totalPages: 1,
          currentPage: 1,
        },
      };

      agentTemplateRepository.findPaginated.mockResolvedValue(paginatedResult);
      agentRepository.findByIds.mockResolvedValue(agents);

      const result = await service.findAll(queryDto as any);

      expect(agentTemplateRepository.findPaginated).toHaveBeenCalledWith(
        1,
        10,
        undefined,
        AgentTemplateStatus.ACTIVE,
        undefined,
        undefined,
      );

      expect(agentRepository.findByIds).toHaveBeenCalledWith(['template-1', 'template-2']);

      expect(result.items.length).toBe(2);
      expect(result.items[0].id).toBe('template-1');
      expect(result.items[0].name).toBe('Template 1');
      expect(result.items[0].model).toBe('gpt-4');
      expect(result.meta).toEqual(paginatedResult.meta);
    });
  });

  describe('findById', () => {
    it('should return agent template details', async () => {
      const id = 'template-1';
      const template = { id, typeId: 1 };
      const agent = { id, name: 'Template 1', modelConfig: { modelId: 'gpt-4' } };
      const typeAgent = { id: 1, name: 'Type 1' };

      agentTemplateRepository.findById.mockResolvedValue(template);
      agentRepository.findById.mockResolvedValue(agent);
      typeAgentRepository.findById.mockResolvedValue(typeAgent);

      const result = await service.findById(id);

      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
      expect(agentRepository.findById).toHaveBeenCalledWith(id);
      expect(typeAgentRepository.findById).toHaveBeenCalledWith(1);

      expect(result.id).toBe(id);
      expect(result.name).toBe('Template 1');
      expect(result.modelConfig.modelId).toBe('gpt-4');
      expect(result.type.id).toBe(1);
      expect(result.type.name).toBe('Type 1');
    });

    it('should throw exception if agent template not found', async () => {
      const id = 'template-1';

      agentTemplateRepository.findById.mockResolvedValue(null);

      await expect(service.findById(id)).rejects.toThrow(AppException);
      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
    });

    it('should throw exception if agent not found', async () => {
      const id = 'template-1';
      const template = { id, typeId: 1 };

      agentTemplateRepository.findById.mockResolvedValue(template);
      agentRepository.findById.mockResolvedValue(null);

      await expect(service.findById(id)).rejects.toThrow(AppException);
      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
      expect(agentRepository.findById).toHaveBeenCalledWith(id);
    });
  });

  describe('create', () => {
    it('should create a new agent template', async () => {
      const createDto = {
        name: 'New Template',
        typeId: 1,
        modelConfig: { modelId: 'gpt-4' },
        status: AgentTemplateStatus.DRAFT,
        avatarMimeType: 'image/jpeg',
      };

      const employeeId = 1;
      const newAgent = { id: 'new-template-id' };
      const s3Key = 'agent-avatars/new-template-id.jpg';
      const presignedUrl = 'https://s3.example.com/upload';

      agentRepository.findByName.mockResolvedValue(null);
      typeAgentRepository.findById.mockResolvedValue({ id: 1 });
      agentRepository.create.mockReturnValue({});
      agentRepository.save.mockResolvedValue(newAgent);
      agentTemplateRepository.create.mockReturnValue({});
      agentTemplateRepository.save.mockResolvedValue({});
      s3Service.generateS3Key.mockReturnValue(s3Key);
      s3Service.getPresignedUrl.mockResolvedValue(presignedUrl);

      const result = await service.create(createDto as any, employeeId);

      expect(agentRepository.findByName).toHaveBeenCalledWith('New Template');
      expect(typeAgentRepository.findById).toHaveBeenCalledWith(1);
      expect(agentRepository.create).toHaveBeenCalled();
      expect(agentRepository.save).toHaveBeenCalled();
      expect(agentTemplateRepository.create).toHaveBeenCalledWith({
        id: 'new-template-id',
        typeId: 1,
        profile: {},
        convertConfig: {},
        status: AgentTemplateStatus.DRAFT,
        createdBy: 1,
      });
      expect(agentTemplateRepository.save).toHaveBeenCalled();
      expect(s3Service.generateS3Key).toHaveBeenCalledWith('agent-avatars', 'new-template-id', 'image/jpeg');
      expect(s3Service.getPresignedUrl).toHaveBeenCalledWith('putObject', s3Key);
      expect(agentRepository.update).toHaveBeenCalledWith('new-template-id', { avatar: s3Key });

      expect(result).toEqual({
        id: 'new-template-id',
        avatarUrlUpload: presignedUrl,
      });
    });

    it('should throw exception if agent name already exists', async () => {
      const createDto = {
        name: 'Existing Template',
        typeId: 1,
      };

      const employeeId = 1;

      agentRepository.findByName.mockResolvedValue({ id: 'existing-id' });

      await expect(service.create(createDto as any, employeeId)).rejects.toThrow(AppException);
      expect(agentRepository.findByName).toHaveBeenCalledWith('Existing Template');
    });
  });

  describe('updateStatus', () => {
    it('should update agent template status', async () => {
      const id = 'template-1';
      const updateStatusDto = { status: AgentTemplateStatus.ACTIVE };
      const employeeId = 1;

      const template = { id, status: AgentTemplateStatus.DRAFT };
      const agent = { id, status: AgentTemplateStatus.DRAFT };

      agentTemplateRepository.findById.mockResolvedValue(template);
      agentRepository.findById.mockResolvedValue(agent);

      await service.updateStatus(id, updateStatusDto, employeeId);

      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
      expect(agentRepository.findById).toHaveBeenCalledWith(id);
      expect(agentRepository.update).toHaveBeenCalledWith(id, {
        status: AgentTemplateStatus.ACTIVE,
      });
      expect(agentTemplateRepository.updateStatus).toHaveBeenCalledWith(
        id,
        AgentTemplateStatus.ACTIVE,
        employeeId,
      );
    });

    it('should throw exception if agent template not found', async () => {
      const id = 'template-1';
      const updateStatusDto = { status: AgentTemplateStatus.ACTIVE };
      const employeeId = 1;

      agentTemplateRepository.findById.mockResolvedValue(null);

      await expect(service.updateStatus(id, updateStatusDto, employeeId)).rejects.toThrow(AppException);
      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
    });
  });

  describe('remove', () => {
    it('should soft delete agent template', async () => {
      const id = 'template-1';
      const employeeId = 1;

      const template = { id };
      const agent = { id };

      agentTemplateRepository.findById.mockResolvedValue(template);
      agentRepository.findById.mockResolvedValue(agent);

      await service.remove(id, employeeId);

      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
      expect(agentRepository.findById).toHaveBeenCalledWith(id);
      expect(agentTemplateRepository.update).toHaveBeenCalledWith(id, {
        deletedBy: employeeId,
      });
      expect(agentRepository.softDelete).toHaveBeenCalledWith(id);
    });

    it('should throw exception if agent template not found', async () => {
      const id = 'template-1';
      const employeeId = 1;

      agentTemplateRepository.findById.mockResolvedValue(null);

      await expect(service.remove(id, employeeId)).rejects.toThrow(AppException);
      expect(agentTemplateRepository.findById).toHaveBeenCalledWith(id);
    });
  });
});
