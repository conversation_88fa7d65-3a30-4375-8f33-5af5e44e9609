import { TimeIntervalEnum } from '@/shared/utils';
import { AppException } from '@common/exceptions';
import { PaginatedResult } from '@common/response';
import { AgentStatusEnum } from '@modules/agent/constants/agent-status.enum';
import { Agent, AgentUser, TypeAgent } from '@modules/agent/entities';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions';
import { AgentRepository } from '@modules/agent/repositories/agent.repository';
import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { CdnService } from '@shared/services/cdn.service';
import { DataSource, Repository } from 'typeorm';
import {
  UserAgentListItemDto,
  UserAgentQueryDto
} from '../dto/user-agent';

/**
 * Service xử lý các thao tác quản lý agent của user (dành cho admin)
 */
@Injectable()
export class AdminUserAgentService {
  private readonly logger = new Logger(AdminUserAgentService.name);

  constructor(
    private readonly dataSource: DataSource,
    private readonly cdnService: CdnService,
  ) { }

  /**
   * Lấy danh sách agent của user với phân trang
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent với phân trang
   */
  async findAll(
    queryDto: UserAgentQueryDto,
  ): Promise<PaginatedResult<UserAgentListItemDto>> {
    try {
      const {
        page = 1,
        limit = 10,
        search,
        status = AgentStatusEnum.PENDING,
        sortBy = 'createdAt',
        sortDirection = 'DESC',
        userId
      } = queryDto;

      // Tạo query builder
      const qb = this.dataSource
        .createQueryBuilder()
        .select([
          'a.id as id',
          'a.name as name',
          'a.avatar as avatar',
          'a.status as status',
          'a.created_at as "createdAt"',
          'a.updated_at as "updatedAt"',
          'au.user_id as "userId"',
          'u.full_name as "userName"',
          'u.avatar as "userAvatar"',
          'au.type_id as "typeId"',
          'ta.name as "typeName"',
        ])
        .from(Agent, 'a')
        .innerJoin(AgentUser, 'au', 'a.id = au.id')
        .innerJoin('users', 'u', 'au.user_id = u.id')
        .innerJoin(TypeAgent, 'ta', 'au.type_id = ta.id')
        .where('a.deleted_at IS NULL');

      // Thêm điều kiện tìm kiếm nếu có
      if (search) {
        qb.andWhere('(a.name ILIKE :search OR u.full_name ILIKE :search)', {
          search: `%${search}%`,
        });
      }

      // Thêm điều kiện lọc theo trạng thái nếu có
      if (status) {
        qb.andWhere('a.status = :status', { status });
      }

      // Thêm điều kiện lọc theo userId nếu có
      if (queryDto.userId) {
        qb.andWhere('au.user_id = :userId', { userId: queryDto.userId });
      }

      // Đếm tổng số bản ghi
      const countQb = qb.clone();
      const total = await countQb.getCount();

      // Thêm phân trang và sắp xếp
      qb.skip((page - 1) * limit)
        .take(limit);

      // Lấy kết quả
      const items = await qb.getRawMany();

      // Chuyển đổi avatar URL
      const transformedItems = items.map((item) => ({
        ...item,
        avatar: item.avatar ? this.cdnService.generateUrlView(item.avatar, TimeIntervalEnum.FIFTEEN_MINUTES) : null,
        userAvatar: item.userAvatar ? this.cdnService.generateUrlView(item.userAvatar, TimeIntervalEnum.FIFTEEN_MINUTES) : null,
      }));

      return {
        items: transformedItems,
        meta: {
          totalItems: total,
          itemCount: transformedItems.length,
          itemsPerPage: limit,
          totalPages: Math.ceil(total / limit),
          currentPage: page,
        },
      };
    } catch (error) {
      this.logger.error(
        `Lỗi khi lấy danh sách agent của user: ${error.message}`,
        error.stack,
      );
      throw new AppException(AGENT_ERROR_CODES.AGENT_LIST_FAILED);
    }
  }
}
