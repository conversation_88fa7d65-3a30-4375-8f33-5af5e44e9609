import { Body, Controller, Delete, Get, Param, Patch, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiExtraModels, ApiOperation, ApiParam, ApiResponse, ApiTags } from '@nestjs/swagger';
import { JwtEmployeeGuard } from '@modules/auth/guards';
import { CurrentEmployee } from '@modules/auth/decorators/current-employee.decorator';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { AdminAgentTemplateService } from '../services/admin-agent-template.service';
import {
  AgentTemplateDetailDto,
  AgentTemplateListItemDto,
  AgentTemplateQueryDto,
  CreateAgentTemplateDto,
  DeletedAgentTemplateQueryDto,
  RestoreAgentTemplateDto,
  UpdateAgentTemplateDto,
  UpdateAgentTemplateStatusDto,
} from '../dto/agent-template';
import { JWTPayload } from '@modules/auth/interfaces/jwt-payload.interface';
import { AGENT_ERROR_CODES } from '@modules/agent/exceptions/agent-error.code';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@/common';

/**
 * Controller xử lý các API liên quan đến Agent Template cho Admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_AGENT_TEMPLATE)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard)
@Controller('admin/agents/template')
@ApiExtraModels(
  ApiResponseDto,
  PaginatedResult,
  AgentTemplateListItemDto,
  AgentTemplateDetailDto,
)
export class AdminAgentTemplateController {
  constructor(
    private readonly adminAgentTemplateService: AdminAgentTemplateService,
  ) {}

  /**
   * Lấy danh sách agent template đã xóa
   * @param queryDto Tham số truy vấn
   * @returns Danh sách agent template đã xóa với phân trang
   */
  // @Get('trash')
  // @ApiOperation({
  //   summary: 'Lấy danh sách agent template đã xóa',
  //   description: 'Lấy danh sách agent template đã xóa với phân trang',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Danh sách agent template đã xóa',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentTemplateListItemDto),
  // })
  // @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  // async findAllDeleted(
  //   @Query() queryDto: DeletedAgentTemplateQueryDto,
  // ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateListItemDto>>> {
  //   const result = await this.adminAgentTemplateService.findAllDeleted(queryDto);
  //   return ApiResponseDto.paginated(result);
  // }

  // /**
  //  * Xem chi tiết agent template đã xóa
  //  * @param id ID của agent template
  //  * @returns Chi tiết agent template đã xóa
  //  */
  // @Get('trash/:id')
  // @ApiOperation({
  //   summary: 'Xem chi tiết agent template đã xóa',
  //   description: 'Xem chi tiết agent template đã xóa theo ID',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID của agent template',
  //   example: '550e8400-e29b-41d4-a716-446655440000',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Chi tiết agent template đã xóa',
  //   schema: ApiResponseDto.getSchema(AgentTemplateDetailDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async findDeletedById(
  //   @Param('id') id: string,
  // ): Promise<ApiResponseDto<AgentTemplateDetailDto>> {
  //   const result = await this.adminAgentTemplateService.findDeletedById(id);
  //   return ApiResponseDto.success(result);
  // }

  // /**
  //  * Lấy danh sách agent template với phân trang
  //  * @param queryDto Tham số truy vấn
  //  * @returns Danh sách agent template với phân trang
  //  */
  // @Get()
  // @ApiOperation({
  //   summary: 'Lấy danh sách agent template',
  //   description:
  //     'Lấy danh sách agent template với phân trang và lọc theo trạng thái',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  //   schema: ApiResponseDto.getPaginatedSchema(AgentTemplateListItemDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async findAll(
  //   @Query() queryDto: AgentTemplateQueryDto,
  // ): Promise<ApiResponseDto<PaginatedResult<AgentTemplateListItemDto>>> {
  //   const result = await this.adminAgentTemplateService.findAll(queryDto);
  //   return ApiResponseDto.paginated(result);
  // }

  // /**
  //  * Lấy chi tiết agent template theo ID
  //  * @param id ID của agent template
  //  * @returns Chi tiết agent template
  //  */
  // @Get(':id')
  // @ApiOperation({
  //   summary: 'Lấy chi tiết agent template',
  //   description: 'Lấy thông tin chi tiết của agent template theo ID',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID của agent template',
  //   example: '550e8400-e29b-41d4-a716-446655440000',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Thành công',
  //   schema: ApiResponseDto.getSchema(AgentTemplateDetailDto),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async findOne(
  //   @Param('id') id: string,
  // ): Promise<ApiResponseDto<AgentTemplateDetailDto>> {
  //   const result = await this.adminAgentTemplateService.findById(id);
  //   return ApiResponseDto.success(result);
  // }

  // /**
  //  * Tạo agent template mới
  //  * @param createDto Dữ liệu tạo agent template
  //  * @param employee Thông tin nhân viên
  //  * @returns URL tải lên avatar và ID của agent template mới
  //  */
  // @Post()
  // @ApiOperation({
  //   summary: 'Tạo agent template mới',
  //   description: 'Tạo agent template mới với thông tin cơ bản',
  // })
  // @ApiResponse({
  //   status: 201,
  //   description: 'Tạo agent template thành công',
  //   schema: ApiResponseDto.getSchema({
  //     id: 'string',
  //     avatarUrlUpload: 'string',
  //   }),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NAME_EXISTS,
  //   AGENT_ERROR_CODES.TYPE_AGENT_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async create(
  //   @Body() createDto: CreateAgentTemplateDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<{ avatarUrlUpload: string | null }>> {
  //   const result = await this.adminAgentTemplateService.create(
  //     createDto,
  //     employee.id,
  //   );
  //   return ApiResponseDto.created({ avatarUrlUpload: result });
  // }

  // /**
  //  * Cập nhật agent template
  //  * @param id ID của agent template
  //  * @param updateDto Dữ liệu cập nhật
  //  * @param employee Thông tin nhân viên
  //  * @returns URL tải lên avatar mới (nếu có)
  //  */
  // @Patch(':id')
  // @ApiOperation({
  //   summary: 'Cập nhật agent template',
  //   description: 'Cập nhật thông tin của agent template theo ID',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID của agent template',
  //   example: '550e8400-e29b-41d4-a716-446655440000',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật agent template thành công',
  //   schema: ApiResponseDto.getSchema({
  //     avatarUrlUpload: 'string',
  //   }),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NAME_EXISTS,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async update(
  //   @Param('id') id: string,
  //   @Body() updateDto: UpdateAgentTemplateDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<{ avatarUrlUpload?: string }>> {
  //   const result = await this.adminAgentTemplateService.update(
  //     id,
  //     updateDto,
  //     employee.id,
  //   );
  //   return ApiResponseDto.success(result);
  // }

  // /**
  //  * Cập nhật trạng thái agent template
  //  * @param id ID của agent template
  //  * @param updateStatusDto Dữ liệu cập nhật trạng thái
  //  * @param employee Thông tin nhân viên
  //  */
  // @Patch(':id/status')
  // @ApiOperation({
  //   summary: 'Cập nhật trạng thái agent template',
  //   description: 'Cập nhật trạng thái của agent template theo ID',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID của agent template',
  //   example: '550e8400-e29b-41d4-a716-446655440000',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Cập nhật trạng thái agent template thành công',
  //   schema: ApiResponseDto.getSchema(null),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_STATUS_UPDATE_FAILED,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async updateStatus(
  //   @Param('id') id: string,
  //   @Body() updateStatusDto: UpdateAgentTemplateStatusDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<null>> {
  //   await this.adminAgentTemplateService.updateStatus(
  //     id,
  //     updateStatusDto,
  //     employee.id,
  //   );
  //   return ApiResponseDto.success(
  //     null,
  //     'Cập nhật trạng thái agent template thành công',
  //   );
  // }

  // /**
  //  * Xóa agent template
  //  * @param id ID của agent template
  //  * @param employee Thông tin nhân viên
  //  */
  // @Delete(':id')
  // @ApiOperation({
  //   summary: 'Xóa agent template',
  //   description: 'Xóa agent template theo ID (soft delete)',
  // })
  // @ApiParam({
  //   name: 'id',
  //   description: 'ID của agent template',
  //   example: '550e8400-e29b-41d4-a716-446655440000',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Xóa agent template thành công',
  //   schema: ApiResponseDto.getSchema(null),
  // })
  // @ApiErrorResponse(
  //   AGENT_ERROR_CODES.AGENT_TEMPLATE_NOT_FOUND,
  //   AGENT_ERROR_CODES.AGENT_NOT_FOUND,
  //   ErrorCode.INTERNAL_SERVER_ERROR
  // )
  // async remove(
  //   @Param('id') id: string,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<null>> {
  //   await this.adminAgentTemplateService.remove(id, employee.id);
  //   return ApiResponseDto.success(null, 'Xóa agent template thành công');
  // }



  // /**
  //  * Khôi phục agent template đã xóa
  //  * @param restoreDto Dữ liệu khôi phục
  //  * @param employee Thông tin nhân viên
  //  * @returns Số lượng bản ghi đã được khôi phục
  //  */
  // @Post('restore')
  // @ApiOperation({
  //   summary: 'Khôi phục agent template đã xóa',
  //   description: 'Khôi phục một hoặc nhiều agent template đã xóa',
  // })
  // @ApiResponse({
  //   status: 200,
  //   description: 'Khôi phục agent template thành công',
  //   schema: ApiResponseDto.getSchema({
  //     restoredCount: 'number',
  //   }),
  // })
  // @ApiErrorResponse(ErrorCode.INTERNAL_SERVER_ERROR)
  // async restore(
  //   @Body() restoreDto: RestoreAgentTemplateDto,
  //   @CurrentEmployee() employee: JWTPayload,
  // ): Promise<ApiResponseDto<{ restoredCount: number }>> {
  //   const restoredCount = await this.adminAgentTemplateService.restore(
  //     restoreDto,
  //     employee.id,
  //   );
  //   return ApiResponseDto.success({ restoredCount });
  // }
}
