import { Injectable } from '@nestjs/common';
import { UserAgentRunStatus } from '../../../shared/enums';
import { ChatDatabaseService } from './database.service';

/**
 * Interface for user agent run data
 */
export interface UserAgentRun {
  id: string;
  payload: any; // JSONB data
  status: UserAgentRunStatus;
  created_at: number; // bigint timestamp
  created_by: number; // FK to users table
  thread_id: string; // FK to user_conversation_thread table
}

/**
 * Interface for creating a new user agent run
 */
export interface CreateUserAgentRunData {
  payload: any; // JSONB data (SystemAgentConfig)
  created_by: number; // FK to users table
  status?: UserAgentRunStatus; // Optional, defaults to 'created'
}

/**
 * Raw SQL queries for user_agent_runs table operations (Chat Module)
 *
 * The chat module needs to:
 * 1. Create new runs with agent configuration payload
 * 2. Query runs for management and monitoring
 * 3. Update run status
 */
@Injectable()
export class UserAgentRunsQueries {
  constructor(private readonly databaseService: ChatDatabaseService) {}

  /**
   * Create a new user agent run
   * @param data Run data to insert
   * @returns Promise<string> The ID of the created run
   */
  async createRun(data: CreateUserAgentRunData): Promise<string> {
    const query = `
      INSERT INTO user_agent_runs (payload, status, created_by) 
      VALUES ($1, $2, $3) 
      RETURNING id
    `;
    
    const result = await this.databaseService.query(query, [
      JSON.stringify(data.payload),
      data.status || UserAgentRunStatus.CREATED,
      data.created_by
    ]);
    
    return result[0].id;
  }

  /**
   * Get a user agent run by ID
   * @param id Run ID
   * @returns Promise<UserAgentRun | null>
   */
  async getRunById(id: string): Promise<UserAgentRun | null> {
    const query = `
      SELECT id, payload, status, created_at, created_by, thread_id
      FROM user_agent_runs 
      WHERE id = $1
    `;
    
    const result = await this.databaseService.query(query, [id]);
    
    if (result.length === 0) {
      return null;
    }
    
    return {
      id: result[0].id,
      payload: result[0].payload,
      status: result[0].status as UserAgentRunStatus,
      created_at: result[0].created_at,
      created_by: result[0].created_by,
      thread_id: result[0].thread_id
    };
  }

  /**
   * Update run status
   * @param id Run ID
   * @param status New status
   * @returns Promise<boolean> True if update was successful
   */
  async updateRunStatus(id: string, status: UserAgentRunStatus): Promise<boolean> {
    const query = `
      UPDATE user_agent_runs 
      SET status = $1 
      WHERE id = $2
    `;
    
    const result = await this.databaseService.query(query, [status, id]);
    return Array.isArray(result) && result.length >= 0;
  }

  /**
   * Get runs by status
   * @param status Status to filter by
   * @param limit Optional limit (default: 100)
   * @returns Promise<UserAgentRun[]>
   */
  async getRunsByStatus(status: UserAgentRunStatus, limit: number = 100): Promise<UserAgentRun[]> {
    const query = `
      SELECT id, payload, status, created_at, created_by, thread_id
      FROM user_agent_runs 
      WHERE status = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `;
    
    const result = await this.databaseService.query(query, [status, limit]);
    
    return result.map((row: any) => ({
      id: row.id,
      payload: row.payload,
      status: row.status as UserAgentRunStatus,
      created_at: row.created_at,
      created_by: row.created_by,
      thread_id: row.thread_id
    }));
  }

  /**
   * Get runs by user
   * @param userId User ID
   * @param limit Optional limit (default: 100)
   * @returns Promise<UserAgentRun[]>
   */
  async getRunsByUser(userId: number, limit: number = 100): Promise<UserAgentRun[]> {
    const query = `
      SELECT id, payload, status, created_at, created_by, thread_id
      FROM user_agent_runs 
      WHERE created_by = $1 
      ORDER BY created_at DESC 
      LIMIT $2
    `;
    
    const result = await this.databaseService.query(query, [userId, limit]);
    
    return result.map((row: any) => ({
      id: row.id,
      payload: row.payload,
      status: row.status as UserAgentRunStatus,
      created_at: row.created_at,
      created_by: row.created_by,
      thread_id: row.thread_id
    }));
  }
}
