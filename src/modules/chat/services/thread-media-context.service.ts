import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource, In } from 'typeorm';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { ThreadMediaContext } from '../entities';
import { ImageAttachmentContextDto } from '../dto/attachment-context.dto';
import { OwnerTypeEnum } from '@/modules/data/media/enums/owner-type.enum';

/**
 * Service for managing thread-media context associations
 * Handles CRUD operations for linking conversation threads with media attachments
 */
@Injectable()
export class ThreadMediaContextService {
  private readonly logger = new Logger(ThreadMediaContextService.name);

  constructor(
    @InjectRepository(ThreadMediaContext)
    private readonly threadMediaContextRepository: Repository<ThreadMediaContext>,
    private readonly dataSource: DataSource,
  ) {}

  /**
   * Add multiple media items to a thread
   * @param threadId ID of the conversation thread
   * @param mediaIds Array of media IDs to associate
   * @param userId ID of the user making the request
   * @returns Promise with operation result
   * @throws AppException if media already associated or insert fails
   */
  async addMediaToThread(
    threadId: string,
    mediaIds: string[],
    userId: number,
  ): Promise<{ success: boolean; addedCount: number; addedMediaIds: string[] }> {
    // 1. Validate thread existence and user ownership
    await this.validateThreadOwnership(threadId, userId);

    // 2. Validate media existence and user ownership
    const validMediaIds = await this.validateMediaOwnership(mediaIds, userId);

    // 3. Bulk insert all associations (fail fast on duplicates)
    try {
      const associations = validMediaIds.map(mediaId => ({
        threadId,
        mediaDataId: mediaId,
      }));

      await this.threadMediaContextRepository.insert(associations);

      this.logger.log(`Added ${validMediaIds.length} media items to thread ${threadId}`);

      return {
        success: true,
        addedCount: validMediaIds.length,
        addedMediaIds: validMediaIds,
      };
    } catch (error) {
      // Handle unique constraint violations
      if (error.code === '23505') {
        throw new AppException(
          CHAT_ERROR_CODES.THREAD_MEDIA_ASSOCIATION_EXISTS,
          `One or more media items are already associated with thread ${threadId}`,
        );
      }

      // Handle all other database/insert errors
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_CONTEXT_INSERT_FAILED,
        `Failed to associate media with thread: ${error.message}`,
      );
    }
  }

  /**
   * Get all media associated with a thread
   * @param threadId ID of the conversation thread
   * @param userId ID of the user making the request
   * @returns Promise<ImageAttachmentContextDto[]> Array of media metadata
   */
  async getThreadMedia(
    threadId: string,
    userId: number,
  ): Promise<ImageAttachmentContextDto[]> {
    // 1. Validate thread access
    await this.validateThreadOwnership(threadId, userId);

    // 2. Fetch media with optimized query (images only, user-owned)
    const query = `
      SELECT
        md.id as "fileId",
        md.name,
        md.storage_key as "storageKey",
        md.media_type as "mediaType",
        md.tags,
        md.description as "desc"
      FROM media_data md
      JOIN thread_media_context tmc ON md.id = tmc.media_data_id
      WHERE tmc.thread_id = $1
        AND md.owned_by = $2
        AND md.status NOT IN ('REJECTED', 'DELETED')
        AND md.media_type = 'IMAGE'
        AND md.owner_type = 'USER'
      ORDER BY tmc.created_at DESC
    `;

    const results = await this.dataSource.query(query, [threadId, userId]);

    // 3. Transform to internal DTO format (for worker processing)
    const mediaItems = results.map((row: any) => ({
      type: row.mediaType.toLowerCase(), // ✅ FIXED: Use database media_type (IMAGE -> image)
      fileId: row.fileId,
      name: row.name,
      path: row.storageKey, // Use storageKey as path for worker
      tags: row.tags || [],
      desc: row.desc,
    }));

    return mediaItems;
  }

  /**
   * Remove multiple media items from a thread
   * @param threadId ID of the conversation thread
   * @param mediaIds Array of media IDs to remove
   * @param userId ID of the user making the request
   * @returns Promise with operation result
   * @throws AppException if media not found or not associated with thread
   */
  async removeMediaFromThread(
    threadId: string,
    mediaIds: string[],
    userId: number,
  ): Promise<{ success: boolean; removedCount: number; removedMediaIds: string[]; threadId: string; message: string }> {
    // 1. Validate thread ownership
    await this.validateThreadOwnership(threadId, userId);

    // 2. Validate media ownership
    const validMediaIds = await this.validateMediaOwnership(mediaIds, userId);

    // 3. Bulk remove associations
    try {
      const result = await this.threadMediaContextRepository.delete({
        threadId,
        mediaDataId: In(validMediaIds),
      });

      const affectedCount = result.affected || 0;

      if (affectedCount === 0) {
        throw new AppException(
          CHAT_ERROR_CODES.THREAD_MEDIA_ASSOCIATION_NOT_FOUND,
          `None of the specified media items are associated with thread ${threadId}`,
        );
      }

      this.logger.log(`Removed ${affectedCount} media items from thread ${threadId}`);

      return {
        success: true,
        removedCount: affectedCount,
        removedMediaIds: validMediaIds,
        threadId: threadId,
        message: `Successfully removed ${affectedCount} media items from thread`
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      throw new AppException(
        CHAT_ERROR_CODES.THREAD_CONTEXT_INSERT_FAILED,
        `Failed to remove media from thread: ${error.message}`,
      );
    }
  }

  /**
   * Validate thread exists and user has ownership
   * @param threadId ID of the thread to validate
   * @param userId ID of the user
   * @throws AppException if thread not found or access denied
   */
  private async validateThreadOwnership(threadId: string, userId: number): Promise<void> {
    const query = `
      SELECT thread_id 
      FROM user_conversation_thread 
      WHERE thread_id = $1 AND user_id = $2
    `;
    
    const result = await this.dataSource.query(query, [threadId, userId]);
    
    if (!result || result.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.THREAD_ACCESS_DENIED,
        `Thread ${threadId} not found or access denied`,
      );
    }
  }

  /**
   * Validate media exists, user has ownership, and media is image type
   * @param mediaIds Array of media IDs to validate
   * @param userId ID of the user
   * @returns Promise<string[]> Array of valid media IDs
   * @throws AppException if any media not found or access denied
   */
  private async validateMediaOwnership(mediaIds: string[], userId: number): Promise<string[]> {
    const query = `
      SELECT id
      FROM media_data
      WHERE id = ANY($1)
        AND owned_by = $2
        AND status NOT IN ('REJECTED', 'DELETED')
        AND media_type = 'IMAGE'
        AND owner_type = 'USER'
    `;
    
    const results = await this.dataSource.query(query, [mediaIds, userId]);
    const validMediaIds = results.map((row: any) => row.id);
    
    if (validMediaIds.length !== mediaIds.length) {
      const invalidIds = mediaIds.filter(id => !validMediaIds.includes(id));
      throw new AppException(
        CHAT_ERROR_CODES.MEDIA_ACCESS_DENIED,
        `Media not found or access denied: ${invalidIds.join(', ')}`,
      );
    }
    
    return validMediaIds;
  }
}
