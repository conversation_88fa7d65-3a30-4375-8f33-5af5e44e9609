import { Injectable, Logger } from '@nestjs/common';
import { ChatDatabaseService } from '../database';
import { OwnerTypeEnum } from '../../data/media/enums/owner-type.enum';
import { MediaStatusEnum } from '../../data/media/enums/media-status.enum';

export interface MediaData {
  id: string;
  name: string;
  storageKey: string;
  description: string;
  tags: string[];
}
@Injectable()
export class ImageService {
  private readonly logger = new Logger(ImageService.name);
  constructor(private readonly database: ChatDatabaseService) {}

  /**
   * Get media by id
   */
  async getMediaById(id: string, ownerType: OwnerTypeEnum, userId: number) {
    const sql = `SELECT name, description, tags, storage_key FROM media_data 
    WHERE id = $1 
    AND status != $2 
    AND owner_type = $3 AND owned_by = $4`;
    const result = await this.database.queryOne(sql, [
      id,
      MediaStatusEnum.DELETED,
      ownerType,
      userId,
    ]);
    return result;
  }

  async getMediaByIds(
    ids: string[],
    ownerType: OwnerTypeEnum,
    userId: number,
  ): Promise<MediaData[]> {
    const sql = `SELECT id, name, description, tags, storage_key as storageKey FROM media_data
                    WHERE id IN (${ids.map((_, index) => `$${index + 1}`)})
                    AND status != $${ids.length + 1}
                    AND owner_type = $${ids.length + 2}
                    AND owned_by = $${ids.length + 3}`;
    const result = await this.database.query(sql, [
      ...ids,
      MediaStatusEnum.DELETED,
      ownerType,
      userId,
    ]);
    this.logger.debug(`[getMediaByIds]: got ${JSON.stringify(result, null, 2)}`);
    return result;
  }

  /**
   * Get media by owned_by and owner_type
   */
  async getMediaByOwner(ownedBy: number, ownerType: OwnerTypeEnum) {
    const sql = `SELECT * FROM media_data WHERE owned_by = $1 AND owner_type = $2 AND status != $3`;
    return this.database.query(sql, [
      ownedBy,
      ownerType,
      MediaStatusEnum.DELETED,
    ]);
  }
}
