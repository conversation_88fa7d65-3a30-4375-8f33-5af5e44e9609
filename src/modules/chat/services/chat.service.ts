import { Inject, Injectable, Logger } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { AgentConfigService } from './agent-config.service';
import { ChatDatabaseService, UserAgentRunsQueries } from '../database';
import { MessageRequestDto } from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { UserAgentRunStatus } from '../../../shared/enums';
import { SystemAgentConfig } from '../interfaces/system-agent-config.interface';
import { ModifyResult } from '../interfaces/modify-result.interface';
import { REDIS_EVENTS, RunCancelEvent, RunTriggerEvent } from '../constants';
import { ConversationThreadService } from './conversation-thread.service';
import { ThreadMediaContextService } from './thread-media-context.service';
import { AppException } from '@/common';
import { CHAT_ERROR_CODES } from '../exceptions';
import { QueryRunner } from 'typeorm';
import { AttachmentContextArrayDto } from '../dto/attachment-context.dto';
import { FileContentBlockDto } from '../dto/file-content-block.dto';
import { ImageContentBlockDto } from '../dto/image-content-block.dto';
import { ImageService, MediaData } from './image.service';
import { OwnerTypeEnum } from '@/modules/data/media/enums';

/**
 * Service for handling chat functionality and run management
 */
@Injectable()
export class ChatService {
  private readonly logger = new Logger(ChatService.name);

  constructor(
    private readonly agentConfigService: AgentConfigService,
    private readonly userAgentRunsQueries: UserAgentRunsQueries,
    private readonly imageService: ImageService,
    private readonly chatDatabaseService: ChatDatabaseService,
    private readonly conversationThreadService: ConversationThreadService,
    private readonly threadMediaContextService: ThreadMediaContextService,
    @Inject('REDIS_CLIENT') private readonly redisClient: ClientProxy,
  ) {}

  /**
   * Process a chat message and create a run for agent processing
   * @param messageRequest Message request data
   * @param userId ID of the user sending the message
   * @param jwt JWT token for authenticated API calls
   * @returns Promise<MessageResponseDto> Response with run information
   */
  async processMessage(
    messageRequest: MessageRequestDto,
    userId: number,
    jwt: string = '',
  ): Promise<MessageResponseDto> {
    // ✅ UPDATED: Build attachment context from database instead of using UI-provided context
    // This ensures we always use the latest media associations from the database
    let attachmentContext: AttachmentContextArrayDto = [];
    try {
      attachmentContext = await this.threadMediaContextService.getThreadMedia(
        messageRequest.threadId,
        userId,
      );
      this.logger.debug(`Built attachment context from database: ${attachmentContext.length} items`, {
        threadId: messageRequest.threadId,
        userId,
        attachmentCount: attachmentContext.length,
      });
    } catch (error) {
      this.logger.warn(`Failed to build attachment context from database: ${error.message}`, {
        threadId: messageRequest.threadId,
        userId,
        error: error.message,
      });
      // Continue with empty attachment context if database lookup fails
      attachmentContext = [];
    }

    // Validate message content before processing
    // Validate file existence in content blocks
    const imageAndFileBlock = messageRequest.contentBlocks.filter(
      (block) => block.type === 'image' || block.type === 'file',
    );
    if (imageAndFileBlock?.length) {
      await this.validateFileExistenceInContentBlocks(
        imageAndFileBlock,
        userId,
      );
    }

    if (messageRequest.messageId) {
      await this.validateModifyLastTextBlock(messageRequest, userId);
    }

    // ✅ NEW: Validate tool_call_decision requirements and get interrupt message ID for deletion
    const processStartTime = Date.now();
    let toolCallInterruptMessageId: string | null = null;

    try {
      toolCallInterruptMessageId = await this.validateToolCallDecisionRequirement(
        messageRequest,
        userId
      );

      if (toolCallInterruptMessageId) {
        this.logger.log(`tool_call_decision validation passed: will replace interrupt message ${toolCallInterruptMessageId}`, {
          threadId: messageRequest.threadId,
          userId,
          interruptMessageId: toolCallInterruptMessageId,
          validationDuration: Date.now() - processStartTime,
          operation: 'processMessage_validation',
        });
      }
    } catch (error) {
      const validationDuration = Date.now() - processStartTime;
      this.logger.error(`tool_call_decision validation failed`, {
        threadId: messageRequest.threadId,
        userId,
        error: error.message,
        validationDuration,
        operation: 'processMessage_validation',
      });
      throw error; // Re-throw validation errors
    }

    // Start database transaction for atomic operation
    const queryRunner = await this.chatDatabaseService.startTransaction();
    try {
      const thread = await this.conversationThreadService.findOne(
        messageRequest.threadId,
        userId,
      );
      if (!thread) {
        throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
      }

      this.logger.log(`Processing message for from user ${userId}`);

      // 3. Build complete agent configuration map for multi-agent processing
      const agentConfigMap =
        await this.agentConfigService.buildAgentConfigMap();

      let primaryAgent: SystemAgentConfig | null = null;
      let supervisorCount = 0;

      for (const agentId in agentConfigMap) {
        if (supervisorCount > 1) {
          throw new AppException(CHAT_ERROR_CODES.TOO_MANY_SUPERVISORS);
        }
        if (agentConfigMap[agentId].isSupervisor) {
          ++supervisorCount;
          primaryAgent = agentConfigMap[agentId];
        }
      }

      if (!primaryAgent) {
        throw new AppException(CHAT_ERROR_CODES.AGENT_NOT_FOUND);
      }

      // 6. Persist user message to database
      let userMessageId = messageRequest.messageId;
      let modifyResults: ModifyResult | undefined = undefined;

      // ✅ NEW: Handle tool_call_decision (delete interrupt, don't save decision)
      if (toolCallInterruptMessageId) {
        // Delete tool_call_interrupt message (tool_call_decision is NOT saved to database)
        const deletionStartTime = Date.now();

        await this.deleteInterruptMessage(
          queryRunner,
          toolCallInterruptMessageId,
          messageRequest.threadId,
        );

        // ✅ BUSINESS RULE: tool_call_decision is NOT saved to database
        userMessageId = undefined;

        const deletionDuration = Date.now() - deletionStartTime;
        this.logger.log(`Completed tool_call_interrupt deletion for tool_call_decision`, {
          threadId: messageRequest.threadId,
          userId,
          deletedInterruptMessageId: toolCallInterruptMessageId,
          businessRule: 'tool_call_decision_not_saved_to_database',
          workerBehavior: 'will_generate_own_messageId_using_v4',
          deletionDuration,
          operation: 'processMessage_deletion',
        });
      } else if (userMessageId) {
        // Standard message modification flow
        modifyResults =
          await this.updateMessageText(queryRunner, messageRequest, userId);
      } else {
        // Standard new message insertion flow
        const { messageId } = await this.insertMessageText(
          queryRunner,
          messageRequest,
          userId,
        );
        userMessageId = messageId;
        messageRequest.messageId = messageId;
      }

      // 4. Create run payload with agent configuration, modification context, and database-built attachment context
      const runPayload = this.buildRunPayload(
        messageRequest,
        primaryAgent,
        agentConfigMap,
        userId,
        attachmentContext,
        modifyResults,
      );

      const threadId = messageRequest.threadId;
      if (!threadId) {
        throw new AppException(CHAT_ERROR_CODES.INVALID_THREAD_ID);
      }

      // 5. Create run in database (within transaction)
      const createRunQuery = `
        INSERT INTO user_agent_runs (payload, status, created_by, thread_id)
        VALUES ($1, $2, $3, $4)
        RETURNING id
      `;
      const runResult = await queryRunner.query(createRunQuery, [
        JSON.stringify(runPayload),
        UserAgentRunStatus.CREATED,
        userId,
        threadId,
      ]);
      const runId = runResult[0].id;

      this.logger.log(`Created run ${runId}`);

      // 9. Publish run trigger event to Redis for worker consumption
      // 🔍 DEBUG: Log trigger event threadId details
      this.logger.debug(`🔍 BACKEND TRIGGER - ThreadId details:`, {
        runId,
        threadId,
        messageRequestThreadId: messageRequest.threadId,
        threadIdType: typeof threadId,
        threadIdLength: threadId?.length,
        messageRequestKeys: Object.keys(messageRequest),
      });

      const runTriggerEvent: RunTriggerEvent = {
        eventType: REDIS_EVENTS.RUN_TRIGGER,
        runId,
        threadId, // ✅ CLEAN: Use same threadId as database
        agentId: primaryAgent.id,
        userId,
        jwt, // JWT token for authenticated API calls
        timestamp: Date.now(),
        priority: 'medium',
        publishedAt: Date.now(),
      };

      // Use emit for fire-and-forget pub/sub pattern
      this.redisClient.emit(REDIS_EVENTS.RUN_TRIGGER, runTriggerEvent);
      this.logger.log(
        `Published run trigger event for run ${runId} (thread ${threadId})`,
      );

      // 🔍 DEBUG: Log published trigger event details
      this.logger.debug(`🔍 BACKEND PUBLISHED trigger event:`, {
        eventType: REDIS_EVENTS.RUN_TRIGGER,
        publishedThreadId: threadId,
        publishedRunId: runId,
        fullEvent: runTriggerEvent,
      });

      // 10. Commit transaction - all database operations successful
      await queryRunner.commitTransaction();
      this.logger.log(`Transaction committed for run ${runId}`);

      // 11. Return response with modification details if applicable
      const responseData: any = {
        messageId: userMessageId,
        runId,
        agentName: primaryAgent.name,
        status: UserAgentRunStatus.CREATED,
        createdAt: Date.now(),
      };

      // Add modification details if modifications were made
      if (modifyResults) {
        responseData.modificationDetails = {
          modifiedMessageId: messageRequest.messageId,
          deletedMessageIds: modifyResults.deletedMessageIds,
          deletedMessagesCount: modifyResults.deletedMessagesCount,
        };
      }

      return new MessageResponseDto(responseData);
    } catch (error) {
      // Rollback transaction on any error
      await queryRunner.rollbackTransaction();
      this.logger.error(
        `Transaction rolled back: ${error.message}`,
        error.stack,
      );
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_PROCESSING_FAILED,
        `Failed to process message: ${error.message}`,
      );
    } finally {
      // Always release the query runner
      await queryRunner.release();
    }
  }

  /**
   * Cancel a run and notify worker
   * @param runId Run ID to cancel
   * @param reason Cancellation reason
   * @returns Promise<boolean> True if cancelled successfully
   */
  async cancelRun(
    runId: string,
    reason: string = 'User requested cancellation',
  ): Promise<boolean> {
    try {
      this.logger.log(`Cancelling run ${runId}: ${reason}`);

      // 1. Get run data to extract threadId
      const runData = await this.userAgentRunsQueries.getRunById(runId);
      if (!runData) {
        this.logger.error(`Run not found for cancellation: ${runId}`);
        return false;
      }
      if (runData.status !== UserAgentRunStatus.RUNNING) {
        this.logger.error(
          `Run is not in running state for cancellation: ${runId}`,
        );
        return false;
      }

      // ✅ CLEAN: Get threadId from database (single source of truth)
      const threadId = runData.thread_id;
      if (!threadId) {
        this.logger.error(`No threadId found for run ${runId}`);
        return false;
      }

      // 🔍 DEBUG: Log threadId extraction details
      this.logger.debug(`🔍 BACKEND CANCEL - ThreadId extraction:`, {
        runId,
        reason,
        extractedThreadId: threadId,
        threadIdType: typeof threadId,
        threadIdLength: threadId?.length,
        payloadKeys: Object.keys(runData.payload || {}),
        messageKeys: Object.keys(runData.payload?.message || {}),
      });

      // 3. Publish run cancel event to Redis using correct threadId
      try {
        const runCancelEvent: RunCancelEvent = {
          eventType: REDIS_EVENTS.RUN_CANCEL,
          threadId, // Use actual threadId from frontend (LangGraph thread)
          runId,
          reason,
          timestamp: Date.now(),
          publishedAt: Date.now(),
        };

        // Use emit for fire-and-forget pub/sub pattern
        this.redisClient.emit(REDIS_EVENTS.RUN_CANCEL, runCancelEvent);
        this.logger.log(
          `Published run cancel event for thread ${threadId} (run ${runId})`,
        );

        // 🔍 DEBUG: Log published event details
        this.logger.debug(`🔍 BACKEND PUBLISHED cancel event:`, {
          eventType: REDIS_EVENTS.RUN_CANCEL,
          publishedThreadId: threadId,
          publishedRunId: runId,
          publishedReason: reason,
          fullEvent: runCancelEvent,
        });
      } catch (error) {
        this.logger.warn(
          `Failed to publish run cancel event for run ${runId}: ${error.message}`,
        );
        // Run is still cancelled in database even if Redis publish fails
      }

      this.logger.log(
        `Successfully cancelled run ${runId} (thread ${threadId})`,
      );
      return true;
    } catch (error) {
      this.logger.error(
        `Failed to cancel run ${runId}: ${error.message}`,
        error.stack,
      );
      return false;
    }
  }

  /**
   * Validate modify_last_text content block
   * @param messageRequest Message Request for editing
   * @param userId User ID for validation
   */
  private async validateModifyLastTextBlock(
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<void> {
    // 1. Check if the target message exists and belongs to the user
    const targetMessageQuery = `SELECT message_id, role, content, created_by
                                FROM user_messages
                                WHERE created_by = $1
                                  AND role = $2
                                ORDER BY timestamp DESC
                                LIMIT 1`;

    const messageResult = await this.chatDatabaseService.query(
      targetMessageQuery,
      [userId, 'user'],
    );

    if (!messageResult || messageResult.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        `User have not submitted any messages`,
      );
    }

    const targetMessage = messageResult[0];
    if (targetMessage.message_id !== messageRequest.messageId) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_CASCADE_DELETE_FAILED,
        'Not last message',
      );
    }
  }

  /**
   * Get the latest message in a thread for validation purposes
   * @param threadId Thread ID to query
   * @param userId User ID for validation
   * @returns Promise<{messageId: string, role: string, content: any} | null>
   */
  private async getLatestMessageInThread(
    threadId: string,
    userId: number,
  ): Promise<{ messageId: string; role: string; content: any } | null> {
    this.logger.debug(`Retrieving latest message in thread ${threadId} for user ${userId}`, {
      threadId,
      userId,
      operation: 'getLatestMessageInThread',
    });

    const latestMessageQuery = `
      SELECT message_id, role, content
      FROM user_messages
      WHERE thread_id = $1
      ORDER BY timestamp DESC, message_id DESC
      LIMIT 1
    `;

    try {
      const result = await this.chatDatabaseService.query(latestMessageQuery, [threadId]);

      if (!result || result.length === 0) {
        this.logger.debug(`No messages found in thread ${threadId}`, {
          threadId,
          userId,
          messageCount: 0,
        });
        return null;
      }

      const latestMessage = result[0];

      return {
        messageId: latestMessage.message_id,
        role: latestMessage.role,
        content: latestMessage.content,
      };
    } catch (error) {
      if (error instanceof AppException) {
        throw error;
      }

      this.logger.error(`Database error retrieving latest message in thread ${threadId}`, {
        threadId,
        userId,
        error: error.message,
        stack: error.stack,
      });

      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_PROCESSING_FAILED,
        `Failed to retrieve latest message: ${error.message}`,
      );
    }
  }

  /**
   * Validate tool_call_decision requires preceding tool_call_interrupt message
   * @param messageRequest Message request with potential tool_call_decision
   * @param userId User ID for validation
   * @returns Promise<string | null> Returns interrupt message ID for deletion, or null if no validation needed
   * @throws AppException if validation fails
   */
  private async validateToolCallDecisionRequirement(
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<string | null> {
    this.logger.debug(`Validating tool_call_decision requirement for thread ${messageRequest.threadId}`, {
      threadId: messageRequest.threadId,
      userId,
      contentBlockCount: messageRequest.contentBlocks.length,
      operation: 'validateToolCallDecisionRequirement',
    });

    // Check if we have a tool_call_decision block
    const toolCallDecisionBlocks = messageRequest.contentBlocks.filter(
      block => block.type === 'tool_call_decision'
    );

    if (toolCallDecisionBlocks.length === 0) {
      this.logger.debug(`No tool_call_decision blocks found, skipping validation`, {
        threadId: messageRequest.threadId,
        userId,
        contentBlockTypes: messageRequest.contentBlocks.map(block => block.type),
      });
      return null; // No validation needed
    }

    this.logger.debug(`Found ${toolCallDecisionBlocks.length} tool_call_decision block(s), validating requirement`, {
      threadId: messageRequest.threadId,
      userId,
      toolCallDecisionCount: toolCallDecisionBlocks.length,
    });

    // Get the latest message in the thread
    const latestMessage = await this.getLatestMessageInThread(messageRequest.threadId, userId);

    if (!latestMessage) {
      this.logger.warn(`No previous messages found in thread for tool_call_decision validation`, {
        threadId: messageRequest.threadId,
        userId,
        validationType: 'tool_call_decision_requires_interrupt',
      });

      throw new AppException(
        CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
        'tool_call_decision requires a previous tool_call_interrupt message, but no messages found in thread',
      );
    }

    // Check if latest message has tool_call_interrupt content block
    const hasToolCallInterrupt = latestMessage.content.contentBlocks?.some(
      (block: any) => block.type === 'tool_call_interrupt'
    );

    this.logger.debug(`Latest message validation result`, {
      threadId: messageRequest.threadId,
      userId,
      latestMessageId: latestMessage.messageId,
      latestMessageRole: latestMessage.role,
      latestMessageContentBlocks: latestMessage.content.contentBlocks?.map((block: any) => block.type) || [],
      hasToolCallInterrupt,
      validationType: 'tool_call_decision_requires_interrupt',
    });

    if (!hasToolCallInterrupt) {
      this.logger.warn(`tool_call_decision validation failed: latest message does not contain tool_call_interrupt`, {
        threadId: messageRequest.threadId,
        userId,
        latestMessageId: latestMessage.messageId,
        latestMessageRole: latestMessage.role,
        latestMessageContentBlocks: latestMessage.content.contentBlocks?.map((block: any) => block.type) || [],
        expectedBlockType: 'tool_call_interrupt',
        actualBlockTypes: latestMessage.content.contentBlocks?.map((block: any) => block.type) || [],
      });

      throw new AppException(
        CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
        `tool_call_decision requires the latest message to be a tool_call_interrupt message. Latest message contains: ${latestMessage.content.contentBlocks?.map((block: any) => block.type).join(', ') || 'no content blocks'}`,
      );
    }

    this.logger.log(`tool_call_decision validation successful: found required tool_call_interrupt message`, {
      threadId: messageRequest.threadId,
      userId,
      interruptMessageId: latestMessage.messageId,
      validationType: 'tool_call_decision_requires_interrupt',
      result: 'validation_passed',
    });

    // Return interrupt message ID for deletion
    return latestMessage.messageId;
  }

  /**
   * Delete tool_call_interrupt message (tool_call_decision is NOT saved to database)
   * @param queryRunner Database transaction query runner
   * @param interruptMessageId ID of the tool_call_interrupt message to delete
   * @param threadId Thread ID for validation
   * @returns Promise<void>
   */
  private async deleteInterruptMessage(
    queryRunner: QueryRunner,
    interruptMessageId: string,
    threadId: string,
  ): Promise<void> {
    const startTime = Date.now();

    this.logger.debug(`Starting tool_call_interrupt message deletion (tool_call_decision NOT saved)`, {
      threadId,
      interruptMessageId,
      operation: 'deleteInterruptMessage',
      businessRule: 'tool_call_decision_not_saved_to_database',
      transactionStart: startTime,
    });

    try {
      // Delete the tool_call_interrupt message (tool_call_decision is NOT saved to database)
      const deleteStartTime = Date.now();
      const deleteInterruptQuery = `
        DELETE FROM user_messages
        WHERE message_id = $1 AND thread_id = $2
        RETURNING message_id, role, timestamp
      `;

      const deleteResult = await queryRunner.query(deleteInterruptQuery, [
        interruptMessageId,
        threadId
      ]);

      const deleteEndTime = Date.now();
      const deleteDuration = deleteEndTime - deleteStartTime;
      const totalDuration = deleteEndTime - startTime;

      if (!deleteResult || deleteResult.length === 0) {
        this.logger.error(`Failed to delete tool_call_interrupt message: message not found`, {
          threadId,
          interruptMessageId,
          operation: 'delete_interrupt_message',
          deleteDuration,
        });

        throw new AppException(
          CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
          `tool_call_interrupt message ${interruptMessageId} not found for deletion`,
        );
      }

      this.logger.log(`Successfully deleted tool_call_interrupt message (tool_call_decision NOT saved)`, {
        threadId,
        deletedMessageId: deleteResult[0].message_id,
        deletedRole: deleteResult[0].role,
        deletedTimestamp: deleteResult[0].timestamp,
        deleteDuration,
        totalDuration,
        operation: 'deleteInterruptMessage',
        businessRule: 'tool_call_decision_not_saved_to_database',
        result: 'success',
      });

      // ✅ BUSINESS RULE: tool_call_decision is NOT saved to database
      // Worker will generate its own messageId using v4() when payload.messageId is missing
    } catch (error) {
      const errorTime = Date.now();
      const errorDuration = errorTime - startTime;

      if (error instanceof AppException) {
        this.logger.error(`tool_call_interrupt deletion failed with AppException`, {
          threadId,
          interruptMessageId,
          errorCode: error.message,
          errorDuration,
          operation: 'deleteInterruptMessage',
          businessRule: 'tool_call_decision_not_saved_to_database',
          result: 'app_exception',
        });
        throw error;
      }

      this.logger.error(`tool_call_interrupt deletion failed with database error`, {
        threadId,
        interruptMessageId,
        error: error.message,
        errorDuration,
        operation: 'deleteInterruptMessage',
        businessRule: 'tool_call_decision_not_saved_to_database',
        result: 'database_error',
        stack: error.stack,
      });

      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_PROCESSING_FAILED,
        `Failed to delete tool_call_interrupt message: ${error.message}`,
      );
    }
  }

  private async insertMessageText(
    queryRunner: QueryRunner,
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<{ messageId: string }> {
    // Only save user message if it's not a modify-only request
    const createMessageQuery = `
      INSERT INTO user_messages (thread_id, role, content, timestamp, created_by)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING message_id
    `;

    // ✅ UPDATED: Remove threadId from message content to eliminate redundancy
    const messageContentWithoutThreadId = {
      ...messageRequest,
      threadId: undefined, // Remove threadId - it's already stored as separate column
    };

    const messageResult = await queryRunner.query(createMessageQuery, [
      messageRequest.threadId,
      'user',
      JSON.stringify(messageContentWithoutThreadId), // ✅ Save content without redundant threadId
      Date.now(),
      userId,
    ]);

    this.logger.log(
      `Persisted user message ${messageResult[0].message_id} for thread ${messageRequest.threadId} (threadId removed from content)`,
    );
    return { messageId: messageResult[0].message_id };
  }

  /**
   * Update message text content and delete all subsequent messages (cascade delete)
   * @param modifyBlock Modify text content block
   * @param userId User ID for processing
   * @returns Promise<ModifyResult> Result containing modified message ID and deleted message IDs
   */
  private async updateMessageText(
    queryRunner: QueryRunner,
    messageRequest: MessageRequestDto,
    userId: number,
  ): Promise<ModifyResult> {
    // Start transaction for atomic operation

    // 1. Get the target message details (thread_id, timestamp, content)
    const getMessageQuery = `
      SELECT message_id, thread_id, timestamp, content
      FROM user_messages
      WHERE message_id = $1
        AND created_by = $2
    `;

    const messageResult = await queryRunner.query(getMessageQuery, [
      messageRequest.messageId,
      userId,
    ]);

    if (!messageResult || messageResult.length === 0) {
      throw new AppException(
        CHAT_ERROR_CODES.MESSAGE_NOT_FOUND,
        `Message ${messageRequest.messageId} not found for update`,
      );
    }

    const targetMessage = messageResult[0];
    let { thread_id: threadId, timestamp: targetTimestamp } = targetMessage;

    // 2. Delete all messages after the target message (using timestamp + message_id for precision)
    const deleteSubsequentQuery = `
      DELETE
      FROM user_messages
      WHERE thread_id = $1
        AND (
        timestamp > $2
          OR (timestamp = $2 AND message_id != $3)
        )
      RETURNING message_id, role, timestamp
    `;

    const deletedMessages = await queryRunner.query(deleteSubsequentQuery, [
      threadId,
      targetTimestamp,
      messageRequest.messageId, // Exclude target message itself
    ]);

    this.logger.log(
      `Deleted the following messages ${JSON.stringify(deletedMessages, null, 2)}`,
    );

    // 4. Create updated message content
    const updatedContent = {
      ...messageRequest,
      threadId: undefined,
      messageId: undefined,
    };

    // 5. Update the target message in database
    const updateMessageQuery = `
      UPDATE user_messages
      SET content    = $1,
          updated_at = $2
      WHERE message_id = $3
        AND created_by = $4
    `;

    await queryRunner.query(updateMessageQuery, [
      JSON.stringify(updatedContent),
      Date.now(),
      messageRequest.messageId,
      userId,
    ]);

    this.logger.log(
      `Successfully modified message ${messageRequest.messageId} and deleted ${deletedMessages.length} subsequent messages`,
    );

    // 7. Return modification result
    return {
      modifiedMessageId: messageRequest.messageId as string,
      deletedMessageIds:
        deletedMessages[0]
          .filter((msg) => !!msg)
          .map((msg: any) => msg?.message_id) || [],
      deletedMessagesCount: deletedMessages[0].length,
    };
  }

  /**
   * Validate file existence in content blocks
   * @param contentBlocks Array of content blocks to validate
   */
  private async validateFileExistenceInContentBlocks(
    contentBlocks: (FileContentBlockDto | ImageContentBlockDto)[],
    userId: number,
  ): Promise<void> {
    const fileIds: string[] = [];
    const imageIds: string[] = [];
    for (const block of contentBlocks) {
      if (block.type === 'file') {
        fileIds.push(block.fileId);
      } else if (block.type === 'image') {
        imageIds.push(block.fileId);
      }
    }
    if (fileIds) {
      await this.validateFileExists(imageIds, 'file', userId);
    } else if (imageIds) {
      await this.validateFileExists(fileIds, 'image', userId);
    }
  }



  /**
   * Validate if a file exists and return file metadata (enhanced stub implementation)
   * @param fileId UUID of the file to validate
   * @param fileType Type of file ('file' or 'image')
   * @returns File metadata object with name, extension, and description
   */
  private async validateFileExists(
    fileIds: string[],
    fileType: 'file' | 'image',
    userId: number,
  ): Promise<MediaData[]> {
    this.logger.debug(`[STUB] Validating ${fileType} existence: ${fileIds}`);
    if (!fileIds) {
      return [];
    }
    if (fileType === 'image') {
      // TODO: Validate image
      const imageMetadata = await this.imageService.getMediaByIds(
        fileIds,
        OwnerTypeEnum.USER,
        userId,
      );
      if (imageMetadata?.length !== fileIds.length) {
        const message = `input image ids: ${fileIds}\n found: ${imageMetadata?.map((metadata) => metadata.id)}`;
        this.logger.error(`[validateFileExists]: ${message}`);
        throw new AppException(CHAT_ERROR_CODES.FILE_NOT_FOUND, message);
      }
      return imageMetadata;
    } else if (fileType === 'file') {
      // TODO: Validate file
      return [];
    } else {
      throw new AppException(CHAT_ERROR_CODES.INVALID_FILE_TYPE);
    }
  }

  /**
   * Build run payload for agent processing
   * @param messageRequest Message request data
   * @param agentConfig Primary agent configuration
   * @param agentConfigMap Complete agent configuration map
   * @param userId User ID for processing
   * @param attachmentContext Database-built attachment context
   * @param modifyResults Optional modification results for context
   * @returns Run payload object
   */
  private buildRunPayload(
    messageRequest: MessageRequestDto,
    agentConfig: SystemAgentConfig,
    agentConfigMap: { [agentId: string]: SystemAgentConfig },
    userId: number,
    attachmentContext: AttachmentContextArrayDto,
    modifyResults?: ModifyResult,
  ): any {
    // Build the run payload - pass through request with database-built attachment context
    const payload = {
      message: {
        ...messageRequest,
        threadId: undefined,
        // ✅ UPDATED: Use database-built attachment context instead of UI-provided context
        attachmentContext: attachmentContext,
      },

      // Primary agent ID (full config available in agentConfigMap)
      primaryAgentId: agentConfig.id,

      // Complete agent configuration map for multi-agent processing
      agentConfigMap,

      modificationDetails: modifyResults,

      // Processing metadata
      metadata: {
        userId, // Essential: actual user ID for processing
        requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        version: '1.0',
      },

      // Processing configuration
      processing: {
        maxRetries: 3,
        timeoutMs: 300000, // 5 minutes
        enableMultiAgent: Object.keys(agentConfigMap).length > 1,
        alwaysApproveToolCall: messageRequest.alwaysApproveToolCall || false,
      },
    };

    this.logger.debug(`Built run payload for agent ${agentConfig.id}`, {
      agentId: agentConfig.id,
      contentBlockCount: messageRequest.contentBlocks.length,
      attachmentCount: attachmentContext?.length || 0,
      multiAgentEnabled: payload.processing.enableMultiAgent,
      agentCount: Object.keys(agentConfigMap).length,
      alwaysApproveToolCall: payload.processing.alwaysApproveToolCall,
    });

    return payload;
  }
}
