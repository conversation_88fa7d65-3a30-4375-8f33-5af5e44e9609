import { JwtUserGuard } from '@modules/auth/guards/jwt-user.guard';
import {
  Controller,
  UseGuards,
  Post,
  Body,
  HttpCode,
  HttpStatus,
  Param,
  Delete,
  Get,
  Req,
} from '@nestjs/common';
import { Request } from 'express';
import {
  ApiBearerAuth,
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiExtraModels,
  ApiBody,
} from '@nestjs/swagger';
import { CurrentUser } from '@modules/auth/decorators/current-user.decorator';
import { ChatService } from '../services/chat.service';
import { ThreadMediaContextService } from '../services/thread-media-context.service';
import { CdnService } from '@/shared/services/cdn.service';
import { TimeIntervalEnum } from '@/shared/utils';
import { MessageRequestDto, MessageRequestExamples } from '../dto/message-request.dto';
import { MessageResponseDto } from '../dto/message-response.dto';
import { ApiResponseDto } from '@/common/response';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ErrorCode } from '@common/exceptions';
import { CHAT_ERROR_CODES } from '../exceptions';
import { TextContentBlockDto } from '../dto/text-content-block.dto';
import { ImageContentBlockDto } from '../dto/image-content-block.dto';
import { FileContentBlockDto } from '../dto/file-content-block.dto';
import { ToolCallDecisionDto } from '../dto/tool-call-decision.dto';
import {
  FileAttachmentContextDto,
  ImageAttachmentContextDto,
  ImageAttachmentContextResponseDto,
} from '../dto/attachment-context.dto';
import {
  AddMediaToThreadDto,
  AddMediaToThreadResponseDto,
  ThreadMediaResponseDto,
  RemoveMediaFromThreadDto,
  RemoveMediaFromThreadResponseDto,
} from '../dto/thread-media.dto';

/**
 * Controller for handling chat endpoints
 */
@ApiTags('Chat')
@Controller('/user/chat')
@ApiExtraModels(
  ApiResponseDto,
  MessageResponseDto,
  MessageRequestDto,
  TextContentBlockDto,
  ImageContentBlockDto,
  FileContentBlockDto,
  ToolCallDecisionDto,
  FileAttachmentContextDto,
  ImageAttachmentContextDto,
  AddMediaToThreadDto,
  AddMediaToThreadResponseDto,
  ThreadMediaResponseDto,
  RemoveMediaFromThreadDto,
  RemoveMediaFromThreadResponseDto,
)
@UseGuards(JwtUserGuard)
@ApiBearerAuth('JWT-auth')
export class ChatController {
  constructor(
    private readonly chatService: ChatService,
    private readonly threadMediaContextService: ThreadMediaContextService,
    private readonly cdnService: CdnService,
  ) {}

  /**
   * Send a message to an agent and create a run for processing
   * @param userId ID of the user sending the message
   * @param messageRequest Message request data
   * @returns Information about the created run
   */
  @Post('message')
  @HttpCode(HttpStatus.CREATED)
  @ApiOperation({
    summary: 'Send message to agent',
    description: `Send a message to a specific agent and create a run for processing. The agent will process the message asynchronously.

    Supports multiple content types:
    - Text messages
    - Image and file attachments
    - Reply to previous messages (using replyToMessageId field)
    - Modify last text message (with cascade delete)

    Reply Message Behavior:
    Use the replyToMessageId field at the message level to reply to previous messages. This provides a clean, flat structure instead of nested content blocks.

    Modify Message Behavior:
    When using type "text" with a "messageId", all subsequent messages in the conversation are automatically deleted to maintain consistency.`,
  })
  @ApiResponse({
    status: 201,
    description: 'Message sent successfully and run created',
    schema: ApiResponseDto.getSchema(MessageResponseDto),
    examples: {
      'Regular Message Response': {
        summary: 'Standard message response',
        value: {
          success: true,
          message: 'Message sent successfully',
          result: {
            messageId: '0280c24b-c849-492d-b5fd-e1c927186272',
            runId: 'run_123456-789-abc',
            agentName: 'Customer Support Agent',
            status: 'created',
            createdAt: 1672531200000,
            modificationDetails: {
              modifiedMessageId: '',
              deletedMessageIds: [],
              deletedMessagesCount: 0,
            },
          },
        },
      },
      'Modify-Only Response': {
        summary: 'Response for modify-only request',
        value: {
          success: true,
          message: 'Message modified successfully',
          result: {
            messageId: 'msg_123_modified',
            runId: 'run_456',
            agentName: 'Assistant',
            status: 'created',
            createdAt: 1749708424552,
            modificationDetails: {
              modifiedMessageId: 'msg_123',
              deletedMessageIds: ['msg_124', 'msg_125'],
              deletedMessagesCount: 2,
            },
          },
        },
      },
      'Mixed Request Response': {
        summary: 'Response for modify + new content request',
        value: {
          success: true,
          message: 'Message sent successfully',
          result: {
            messageId: 'msg_new_456',
            runId: 'run_789',
            agentName: 'Assistant',
            status: 'created',
            createdAt: 1749708424552,
            modificationDetails: {
              modifiedMessageId: 'msg_123',
              deletedMessageIds: ['msg_124'],
              deletedMessagesCount: 1,
            },
          },
        },
      },
    },
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.AGENT_NOT_FOUND,
    CHAT_ERROR_CODES.INVALID_AGENT_TYPE,
    CHAT_ERROR_CODES.AGENT_ACCESS_DENIED,
    CHAT_ERROR_CODES.THREAD_NOT_FOUND,
    CHAT_ERROR_CODES.THREAD_ACCESS_DENIED,
    CHAT_ERROR_CODES.THREAD_VALIDATION_FAILED,
    CHAT_ERROR_CODES.MESSAGE_PROCESSING_FAILED,
    CHAT_ERROR_CODES.MODIFY_TEXT_VALIDATION_FAILED,
    CHAT_ERROR_CODES.MESSAGE_CASCADE_DELETE_FAILED,
    CHAT_ERROR_CODES.REPLY_TARGET_NOT_FOUND,
    CHAT_ERROR_CODES.REPLY_TARGET_DIFFERENT_THREAD,
    CHAT_ERROR_CODES.INVALID_REPLY_TARGET,
    CHAT_ERROR_CODES.FILE_NOT_FOUND,
    CHAT_ERROR_CODES.IMAGE_NOT_FOUND,
    CHAT_ERROR_CODES.INVALID_INPUT,
    CHAT_ERROR_CODES.RUN_CREATION_FAILED,
    CHAT_ERROR_CODES.TOOL_CALL_DECISION_REQUIRES_INTERRUPT,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  @ApiBody({
    type: MessageRequestDto,
    examples: MessageRequestExamples,
  })
  async sendMessage(
    @CurrentUser('id') userId: number,
    @Body() messageRequest: MessageRequestDto,
    @Req() request: Request,
  ): Promise<ApiResponseDto<MessageResponseDto>> {
    // Extract JWT token from Authorization header
    const authHeader = request.headers.authorization;
    const jwt = authHeader?.startsWith('Bearer ')
      ? authHeader.substring(7)
      : '';

    return ApiResponseDto.created(
      await this.chatService.processMessage(messageRequest, userId, jwt),
    );
  }

  /**
   * Cancel a running or pending run
   * @param runId ID of the run to cancel
   * @returns Cancellation result
   */
  @Delete('runs/:runId')
  @HttpCode(HttpStatus.OK)
  @ApiParam({
    name: 'runId',
    description: 'ID of the run to cancel',
    example: 'run_123456-789-abc',
  })
  @ApiOperation({
    summary: 'Cancel a run',
    description:
      'Cancel a running or pending run. This will stop processing and mark the run as failed.',
  })
  @ApiResponse({
    status: 200,
    description: 'Run cancelled successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        message: { type: 'string', example: 'Run cancelled successfully' },
        runId: { type: 'string', example: 'run_123456-789-abc' },
      },
    },
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.INVALID_RUN_ID,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async cancelRun(@Param('runId') runId: string): Promise<object> {
    const success = await this.chatService.cancelRun(runId);

    return {
      success,
      message: success ? 'Run cancelled successfully' : 'Failed to cancel run',
      runId,
    };
  }

  /**
   * Associate media with a conversation thread
   * @param threadId ID of the conversation thread
   * @param addMediaDto Media IDs to associate with the thread
   * @param userId Current user ID
   * @returns Operation result with added media count
   */
  @Post('threads/:threadId/media')
  @HttpCode(HttpStatus.OK)
  @ApiParam({
    name: 'threadId',
    description: 'ID of the conversation thread',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiOperation({
    summary: 'Add media to thread',
    description:
      'Associate one or more media items with a conversation thread. Only image media owned by the user can be associated.',
  })
  @ApiResponse({
    status: 200,
    description: 'Media successfully associated with thread',
    type: AddMediaToThreadResponseDto,
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_ACCESS_DENIED,
    CHAT_ERROR_CODES.MEDIA_ACCESS_DENIED,
    CHAT_ERROR_CODES.THREAD_MEDIA_ASSOCIATION_EXISTS,
    CHAT_ERROR_CODES.THREAD_CONTEXT_INSERT_FAILED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async addMediaToThread(
    @Param('threadId') threadId: string,
    @Body() addMediaDto: AddMediaToThreadDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<AddMediaToThreadResponseDto>> {
    const result = await this.threadMediaContextService.addMediaToThread(
      threadId,
      addMediaDto.mediaIds,
      userId,
    );

    return ApiResponseDto.success(result);
  }

  /**
   * Get all media associated with a conversation thread
   * @param threadId ID of the conversation thread
   * @param userId Current user ID
   * @returns Array of media items with CDN URLs
   */
  @Get('threads/:threadId/media')
  @ApiParam({
    name: 'threadId',
    description: 'ID of the conversation thread',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiOperation({
    summary: 'Get thread media',
    description:
      'Retrieve all media items associated with a conversation thread. Returns media with CDN URLs ready for display.',
  })
  @ApiResponse({
    status: 200,
    description: 'Thread media retrieved successfully',
    type: ThreadMediaResponseDto,
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_ACCESS_DENIED,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async getThreadMedia(
    @Param('threadId') threadId: string,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<ThreadMediaResponseDto>> {
    const internalMedia = await this.threadMediaContextService.getThreadMedia(
      threadId,
      userId,
    );

    // Transform internal DTO (with path) to response DTO (with viewUrl)
    const media: ImageAttachmentContextResponseDto[] = internalMedia.map(item => ({
      fileId: item.fileId,
      name: item.name,
      viewUrl: this.cdnService.generateUrlView(item.path, TimeIntervalEnum.ONE_HOUR) || '',
      tags: item.tags,
      desc: item.desc,
    }));

    return ApiResponseDto.success({ media });
  }

  /**
   * Remove multiple media items from a conversation thread
   * @param threadId ID of the conversation thread
   * @param removeMediaDto Media IDs to remove from the thread
   * @param userId Current user ID
   * @returns Operation result
   */
  @Delete('threads/:threadId/media')
  @HttpCode(HttpStatus.OK)
  @ApiParam({
    name: 'threadId',
    description: 'ID of the conversation thread',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @ApiOperation({
    summary: 'Remove media from thread',
    description:
      'Remove one or more media items from a conversation thread. Only the media owner can remove their media.',
  })
  @ApiResponse({
    status: 200,
    description: 'Media successfully removed from thread',
    type: RemoveMediaFromThreadResponseDto,
  })
  @ApiErrorResponse(
    CHAT_ERROR_CODES.THREAD_ACCESS_DENIED,
    CHAT_ERROR_CODES.MEDIA_ACCESS_DENIED,
    CHAT_ERROR_CODES.THREAD_MEDIA_ASSOCIATION_NOT_FOUND,
    ErrorCode.INTERNAL_SERVER_ERROR,
  )
  async removeMediaFromThread(
    @Param('threadId') threadId: string,
    @Body() removeMediaDto: RemoveMediaFromThreadDto,
    @CurrentUser('id') userId: number,
  ): Promise<ApiResponseDto<RemoveMediaFromThreadResponseDto>> {
    const result = await this.threadMediaContextService.removeMediaFromThread(
      threadId,
      removeMediaDto.mediaIds,
      userId,
    );

    return ApiResponseDto.success(result);
  }
}
