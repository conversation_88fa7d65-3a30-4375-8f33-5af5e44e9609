export enum ModelProviderEnum {
  OPENAI = 'OPENAI',
  XAI = 'XAI',
  ANTHROPIC = 'ANTHROPIC',
  GOOGLE = 'GOOGLE',
  DEEPSEEK = 'DEEPSEEK',
}

export enum InputModality {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

export enum OutputModality {
  TEXT = 'text',
  IMAGE = 'image',
  AUDIO = 'audio',
  VIDEO = 'video',
}

export enum SamplingParameter {
  TEMPERATURE = 'temperature',
  TOP_P = 'top_p',
  TOP_K = 'top_k',
  MAX_TOKENS = 'max_tokens',
  MAX_OUTPUT_TOKENS = 'max_output_tokens',
}

export enum ModelFeature {
  TOOL_CALL = 'tool_call',
  PARALLEL_TOOL_CALL = 'parallel_tool_call',
}


export type TrimmingType = 'top_k' | 'ai' | 'token';

export interface McpSseConfig {
  serverName: string;
  config: Record<string, any>;
}

export interface SystemAgentConfig {
  id: string;
  name: string;
  description: string;
  instruction: string;
  mcpConfig: McpSseConfig[];
  vectorStoreId: string;
  isSupervisor: boolean;
  trimmingConfig: {
    type: TrimmingType; // default to `top_k` always
    threshold: number;
  }
  model: {
    name: string;
    provider: ModelProviderEnum;
    inputModalities: InputModality[];
    outputModalities: OutputModality[];
    samplingParameters: SamplingParameter[];
    features: ModelFeature[];
    parameters?: {
      temperature?: number;
      topP?: number;
      topK?: number;
      maxTokens?: number;
      maxOutputTokens?: number;
    };
    pricing: {
      inputRate: number,
      outputRate: number,
    }
    type: 'SYSTEM';
    apiKeys: string[];
  };
}


export interface SystemAgentConfigMap {
  [agentId: string]: SystemAgentConfig;
}
