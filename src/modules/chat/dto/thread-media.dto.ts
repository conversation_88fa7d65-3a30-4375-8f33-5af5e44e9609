import { ApiProperty } from '@nestjs/swagger';
import { 
  IsArray, 
  ArrayNotEmpty, 
  IsUUID, 
  ArrayMinSize, 
  ArrayMaxSize 
} from 'class-validator';
import { ImageAttachmentContextResponseDto } from './attachment-context.dto';

/**
 * DTO for adding media to a thread
 */
export class AddMediaToThreadDto {
  @ApiProperty({
    description: 'Array of media IDs to associate with the thread',
    example: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e'
    ],
    type: [String],
    minItems: 1,
    maxItems: 20,
  })
  @IsArray({ message: 'mediaIds must be an array' })
  @ArrayNotEmpty({ message: 'mediaIds array cannot be empty' })
  @ArrayMinSize(1, { message: 'At least 1 media ID is required' })
  @ArrayMaxSize(20, { message: 'Maximum 20 media IDs allowed per request' })
  @IsUUID('4', { 
    each: true, 
    message: 'Each media ID must be a valid UUID format' 
  })
  mediaIds: string[];
}

/**
 * DTO for add media to thread response
 */
export class AddMediaToThreadResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Number of media items successfully added',
    example: 2,
  })
  addedCount: number;

  @ApiProperty({
    description: 'Array of media IDs that were successfully added',
    example: [
      'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
      'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e'
    ],
    type: [String],
  })
  addedMediaIds: string[];
}

/**
 * DTO for get thread media response
 */
export class ThreadMediaResponseDto {
  @ApiProperty({
    description: 'Array of media items associated with the thread',
    type: [ImageAttachmentContextResponseDto],
    example: [
      {
        fileId: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
        name: 'Screenshot.png',
        viewUrl: 'https://cdn.example.com/media/IMAGE/2025/06/user_1/screenshot.png?expires=1672531200&signature=abc123',
        storageKey: 'media/images/user_123/2024/01/screenshot.png',
        tags: ['screenshot', 'ui'],
        desc: 'UI screenshot for reference'
      },
      {
        fileId: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
        name: 'Profile.jpg',
        viewUrl: 'https://cdn.example.com/media/IMAGE/2025/06/user_1/profile.jpg?expires=1672531200&signature=def456',
        storageKey: 'media/images/user_123/2024/01/profile.jpg',
        tags: ['profile', 'avatar'],
        desc: 'User profile picture'
      }
    ],
  })
  media: ImageAttachmentContextResponseDto[];
}

/**
 * DTO for removing media from thread request
 */
export class RemoveMediaFromThreadDto {
  @ApiProperty({
    description: 'Array of media IDs to remove from the thread',
    example: [
      'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
      'c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f',
    ],
    type: [String],
    minItems: 1,
    maxItems: 20,
  })
  @IsArray({ message: 'mediaIds must be an array' })
  @ArrayNotEmpty({ message: 'mediaIds array cannot be empty' })
  @ArrayMinSize(1, { message: 'At least 1 media ID is required' })
  @ArrayMaxSize(20, { message: 'Maximum 20 media IDs allowed per request' })
  @IsUUID('4', {
    each: true,
    message: 'Each media ID must be a valid UUID format'
  })
  mediaIds: string[];
}

/**
 * DTO for remove media from thread response
 */
export class RemoveMediaFromThreadResponseDto {
  @ApiProperty({
    description: 'Whether the operation was successful',
    example: true,
  })
  success: boolean;

  @ApiProperty({
    description: 'Number of media items successfully removed',
    example: 2,
  })
  removedCount: number;

  @ApiProperty({
    description: 'Array of media IDs that were removed from the thread',
    example: [
      'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
      'c3d4e5f6-a7b8-9c0d-1e2f-3a4b5c6d7e8f',
    ],
    type: [String],
  })
  removedMediaIds: string[];

  @ApiProperty({
    description: 'ID of the thread the media was removed from',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  threadId: string;

  @ApiProperty({
    description: 'Confirmation message',
    example: 'Media successfully removed from thread',
  })
  message: string;
}
