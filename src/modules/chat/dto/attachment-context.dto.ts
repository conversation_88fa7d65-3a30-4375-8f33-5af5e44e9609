import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsString,
  IsArray,
} from 'class-validator';

/**
 * File attachment context DTO - simplified like content blocks
 */
export class FileAttachmentContextDto {
  @ApiProperty({
    description: "The type of attachment, fixed to 'file'.",
    enum: ['file'],
    example: 'file',
  })
  @IsEnum(['file'])
  @IsNotEmpty()
  type: 'file';

  @ApiProperty({
    description: 'File ID - must be a valid UUID format',
    example: 'a9b1c1d8-e3f4-5a6b-7c8d-9e0f1a2b3c4d',
  })
  @IsUUID(4, { message: 'fileId must be a valid UUID' })
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'The display name of the file.',
    example: 'Invoice-April.pdf',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'tags of the image',
    example: ['funny', 'cute'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[] = [];

  @ApiProperty({
    description: 'Optional description of the file.',
    example: 'This is the invoice for April.',
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;
}

/**
 * Image attachment context DTO for worker processing
 * Contains S3 storage path for internal processing
 */
export class ImageAttachmentContextDto {
  @ApiProperty({
    description: 'Unique identifier for the image file',
    example: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
  })
  fileId: string;

  @ApiProperty({
    description: 'Original filename of the image',
    example: 'Screenshot 2024-01-15.png',
  })
  name: string;

  @ApiProperty({
    description: 'S3 storage path for the image file (worker processing)',
    example: 'media/images/user_123/2024/01/screenshot-2024-01-15.png',
  })
  path: string;

  @ApiProperty({
    description: 'S3 storage key for the image file',
    example: 'media/images/user_123/2024/01/screenshot-2024-01-15.png',
  })
  storageKey: string;

  @ApiProperty({
    description: 'Tags associated with the image',
    example: ['screenshot', 'ui'],
    type: [String],
  })
  tags: string[];

  @ApiProperty({
    description: 'Optional description of the image',
    example: 'Application screenshot for bug report',
    required: false,
  })
  desc?: string;
}

/**
 * Image attachment context response DTO for UI responses
 * Contains CDN URL for frontend display
 */
export class ImageAttachmentContextResponseDto {
  @ApiProperty({
    description: 'Unique identifier for the image file',
    example: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
  })
  fileId: string;

  @ApiProperty({
    description: 'Original filename of the image',
    example: 'Screenshot 2024-01-15.png',
  })
  name: string;

  @ApiProperty({
    description: 'CDN URL for viewing the image (UI responses)',
    example: 'https://cdn.example.com/media/images/screenshot-2024-01-15.png',
  })
  viewUrl: string;

  @ApiProperty({
    description: 'Tags associated with the image',
    example: ['screenshot', 'ui'],
    type: [String],
  })
  tags: string[];

  @ApiProperty({
    description: 'Optional description of the image',
    example: 'Application screenshot for bug report',
    required: false,
  })
  desc?: string;
}

/**
 * Union type for attachment context
 */
export type AttachmentContextDto =
  | FileAttachmentContextDto
  | ImageAttachmentContextDto;

/**
 * Array of attachment contexts for multiple attachments
 */
export type AttachmentContextArrayDto = AttachmentContextDto[];
