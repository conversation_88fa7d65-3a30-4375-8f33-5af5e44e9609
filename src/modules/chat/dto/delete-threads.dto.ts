import { ApiProperty } from '@nestjs/swagger';
import { IsArray, ArrayNotEmpty, IsUUID } from 'class-validator';

export class DeleteThreadsDto {
  @ApiProperty({
    description: 'An array of conversation thread UUIDs to be deleted',
    example: [
      'f47ac10b-58cc-4372-a567-0e02b2c3d479',
      'e87bc21c-43dd-4561-b567-1f02b2c3d480',
    ],
    type: [String],
  })
  @IsArray()
  @ArrayNotEmpty()
  @IsUUID('4', { each: true, message: 'Each value in threadIds must be a valid UUID' })
  threadIds: string[];
}