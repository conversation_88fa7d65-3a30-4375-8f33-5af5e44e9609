import { ApiProperty } from '@nestjs/swagger';
import {
  IsEnum,
  IsNotEmpty,
  IsUUID,
  IsOptional,
  IsString,
  IsArray,
} from 'class-validator';

/**
 * File content block DTO for Facebook Messenger-style messaging
 * Simple version with only essential fields: type and fileId
 */
export class FileContentBlockDto {
  @ApiProperty({
    description: "The type of content block, fixed to 'file'.",
    enum: ['file'],
    example: 'file',
  })
  @IsEnum(['file'])
  @IsNotEmpty()
  type: 'file';

  @ApiProperty({
    description: 'The unique ID of the uploaded file - must be a valid UUID.',
    example: 'a9b1c1d8-e3f4-5a6b-7c8d-9e0f1a2b3c4d',
  })
  @IsUUID(4, { message: 'fileId must be a valid UUID' })
  @IsNotEmpty()
  fileId: string;

  @ApiProperty({
    description: 'The display name of the file.',
    example: 'Invoice April 2025.pdf',
  })
  @IsString()
  @IsNotEmpty()
  name: string;

  @ApiProperty({
    description: 'tags of the image',
    example: ['funny', 'cute'],
  })
  @IsArray()
  @IsOptional()
  tags?: string[] = [];

  @ApiProperty({
    description: 'An optional description of the file.',
    example: 'Monthly invoice for April 2025.',
    required: false,
  })
  @IsOptional()
  @IsString()
  desc?: string;
}

// Export alias for backward compatibility
export { FileContentBlockDto as FileContentDto };
