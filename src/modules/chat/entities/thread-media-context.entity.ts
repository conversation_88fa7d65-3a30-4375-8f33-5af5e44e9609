import { Entity, PrimaryColumn, Column } from 'typeorm';

/**
 * Entity đại diện cho bảng thread_media_context trong cơ sở dữ liệu
 * Bảng liên kết giữa conversation thread và media attachments
 */
@Entity('thread_media_context')
export class ThreadMediaContext {
  /**
   * ID của conversation thread
   * Tham chiếu đến user_conversation_thread.thread_id
   */
  @PrimaryColumn({ 
    name: 'thread_id', 
    type: 'uuid',
    comment: 'ID của conversation thread, tham chiếu đến user_conversation_thread.thread_id'
  })
  threadId: string;

  /**
   * ID của media data
   * Tham chiếu đến media_data.id
   */
  @PrimaryColumn({ 
    name: 'media_data_id', 
    type: 'uuid',
    comment: 'ID của media data, tham chiếu đến media_data.id'
  })
  mediaDataId: string;

  /**
   * Thời điểm tạo liên kết (timestamp millis)
   * Sử dụng bigint để lưu timestamp dưới dạng milliseconds
   */
  @Column({
    name: 'created_at',
    type: 'bigint',
    default: () => '((EXTRACT(EPOCH FROM NOW()) * 1000)::BIGINT)',
    comment: 'Thời điểm tạo liên kết thread-media (timestamp millis)'
  })
  createdAt: number;
}
