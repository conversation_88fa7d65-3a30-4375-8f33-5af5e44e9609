import { Entity, PrimaryGeneratedColumn, Column, Index } from 'typeorm';

@Entity('user_conversation_thread')
@Index('idx_conversation_thread_user_id', ['userId'])
@Index('idx_conversation_thread_created_at', ['createdAt'])
export class UserConversationThread {
  @PrimaryGeneratedColumn('uuid', { name: 'thread_id' })
  threadId: string;

  @Column({ type: 'varchar', length: 255, nullable: false })
  name: string;

  @Column({ name: 'user_id', type: 'integer', nullable: false })
  userId: number;

  @Column({ 
    name: 'created_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  createdAt: number;

  @Column({ 
    name: 'updated_at', 
    type: 'bigint', 
    nullable: false,
    default: () => '((EXTRACT(epoch FROM now()) * (1000)::numeric))::bigint'
  })
  updatedAt: number;
}
