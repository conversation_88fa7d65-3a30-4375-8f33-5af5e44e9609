  import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, PrimaryColumn } from 'typeorm';
import { VectorStore } from './vector-store.entity';
import { KnowledgeFile } from './knowledge-file.entity';

/**
 * Entity đại diện cho bảng vector_store_files trong cơ sở dữ liệu
 * Bảng trung gian lưu cặp vector_store và file
 */
@Entity('vector_store_files')
export class VectorStoreFile {
  /**
   * ID của vector_stores
   */
  @PrimaryColumn({ name: 'vector_store_id' })
  vectorStoreId: string;

  /**
   * ID của knowledge_files
   */
  @PrimaryColumn({ name: 'file_id' })
  fileId: string;
}
