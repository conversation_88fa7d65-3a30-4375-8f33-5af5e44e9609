import { ApiProperty } from '@nestjs/swagger';
import { Expose } from 'class-transformer';
import { OwnerType } from '@shared/enums';

export class FileResponseDto {
  @ApiProperty({
    description: 'ID của file',
    example: 'a1b2c3d4-e5f6-7890-abcd-ef1234567890',
  })
  @Expose()
  id: string;

  @ApiProperty({
    description: 'Tên file',
    example: 'Tài liệu hướng dẫn.pdf',
  })
  @Expose()
  name: string;

  @ApiProperty({
    description: 'Phần mở rộng của file',
    example: 'pdf',
  })
  @Expose()
  extension: string;

  @ApiProperty({
    description: 'Dung lượng file (bytes)',
    example: 1024,
  })
  @Expose()
  storage: number;

  @ApiProperty({
    description: 'Loại chủ sở hữu file',
    example: 'user',
    enum: OwnerType,
  })
  @Expose()
  ownerType: OwnerType;

  @ApiProperty({
    description: 'ID của vector store chứa file (nếu có)',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @Expose()
  vectorStoreId?: string;

  @ApiProperty({
    description: 'Tên của vector store chứa file (nếu có)',
    example: 'Vector Store A',
    required: false,
  })
  @Expose()
  vectorStoreName?: string;

  @ApiProperty({
    description: 'URL xem file',
    example: 'https://cdn.example.com/knowledge/documents/2023/05/file-123456.pdf',
  })
  @Expose()
  viewUrl: string;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1629026400000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1629026400000,
  })
  @Expose()
  updatedAt: number;
}
