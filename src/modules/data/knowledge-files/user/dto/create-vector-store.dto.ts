import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsString, MaxLength } from 'class-validator';

export class CreateVectorStoreDto {
  @ApiProperty({
    description: 'Tên vector store',
    example: 'VectorStoreAI',
    maxLength: 255,
  })
  @IsNotEmpty({ message: 'Tên vector store không được để trống' })
  @IsString({ message: 'Tên vector store phải là chuỗi' })
  @MaxLength(255, { message: 'Tên vector store không được vượt quá 255 ký tự' })
  name: string;
}
