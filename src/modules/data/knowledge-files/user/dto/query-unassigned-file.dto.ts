import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString, IsInt, Min, Max, IsIn } from 'class-validator';
import { Transform, Type } from 'class-transformer';

/**
 * DTO cho việc truy vấn danh sách file tri thức chưa gắn với vector store
 */
export class QueryUnassignedFileDto {
  @ApiProperty({
    description: 'S<PERSON> trang (bắt đầu từ 1)',
    example: 1,
    required: false,
    minimum: 1,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: 'Số trang phải là số nguyên' })
  @Min(1, { message: 'Số trang phải lớn hơn hoặc bằng 1' })
  page?: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    example: 10,
    required: false,
    minimum: 1,
    maximum: 100,
  })
  @IsOptional()
  @Type(() => Number)
  @IsInt({ message: '<PERSON><PERSON> lượng bản ghi phải là số nguyên' })
  @Min(1, { message: '<PERSON><PERSON> lượng bản ghi phải lớn hơn hoặc bằng 1' })
  @Max(100, { message: 'Số lượng bản ghi không được vượt quá 100' })
  limit?: number = 10;

  @ApiProperty({
    description: 'Từ khóa tìm kiếm theo tên file',
    example: 'hướng dẫn',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Từ khóa tìm kiếm phải là chuỗi' })
  @Transform(({ value }) => value?.trim())
  search?: string;

  @ApiProperty({
    description: 'Danh sách phần mở rộng file cần lọc (phân cách bằng dấu phẩy)',
    example: 'pdf,docx,txt',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'Danh sách phần mở rộng phải là chuỗi' })
  @Transform(({ value }) => value?.trim())
  extensions?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    example: 'createdAt',
    required: false,
    enum: ['name', 'createdAt', 'storage'],
  })
  @IsOptional()
  @IsString({ message: 'Trường sắp xếp phải là chuỗi' })
  @IsIn(['name', 'createdAt', 'storage'], {
    message: 'Trường sắp xếp phải là một trong: name, createdAt, storage',
  })
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Hướng sắp xếp',
    example: 'DESC',
    required: false,
    enum: ['ASC', 'DESC'],
  })
  @IsOptional()
  @IsString({ message: 'Hướng sắp xếp phải là chuỗi' })
  @IsIn(['ASC', 'DESC'], {
    message: 'Hướng sắp xếp phải là ASC hoặc DESC',
  })
  sortDirection?: string = 'DESC';

  @ApiProperty({
    description: 'ID của vector store cần kiểm tra để lấy các file chưa gắn với vector store này',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000',
    required: true,
  })
  @IsString({ message: 'ID vector store phải là chuỗi' })
  @Transform(({ value }) => value?.trim())
  vectorStoreId: string;
}
