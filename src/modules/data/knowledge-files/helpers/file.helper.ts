import { KnowledgeFile } from '../entities';
import { FileResponseDto } from '../admin/dto';
import { plainToInstance } from 'class-transformer';

/**
 * Helper class cho các thao tác liên quan đến file
 */
export class FileHelper {
  /**
   * Lấy phần mở rộng của file từ tên file
   * @param fileName Tên file
   * @returns Phần mở rộng của file (không bao gồm dấu chấm) hoặc chuỗi rỗng nếu không có phần mở rộng
   */
  static getFileExtension(fileName: string): string {
    if (!fileName) return '';

    const lastDotIndex = fileName.lastIndexOf('.');
    if (lastDotIndex === -1 || lastDotIndex === fileName.length - 1) {
      return ''; // Không có phần mở rộng hoặc dấu chấm là ký tự cuối cùng
    }

    return fileName.substring(lastDotIndex + 1);
  }

  /**
   * Chuyển đổi tên trường sắp xếp thành tên thuộc tính trong entity
   * @param sortBy Tên trường sắp xếp
   * @returns Tên thuộc tính trong entity
   */
  static getSortColumn(sortBy: string): string {
    // Đảm bảo sortBy không null hoặc undefined
    const field = sortBy || 'createdAt';

    switch (field.toLowerCase()) {
      case 'name':
        return 'name';
      case 'storage':
        return 'storage';
      case 'createdat':
      case 'created_at':
      case 'createat':
      default:
        return 'createdAt';
    }
  }

  /**
   * Kiểm tra file có phần mở rộng hợp lệ không
   * @param fileName Tên file
   * @returns true nếu file có phần mở rộng hợp lệ, false nếu không
   */
  static hasValidExtension(fileName: string): boolean {
    return !!FileHelper.getFileExtension(fileName);
  }

  /**
   * Tạo key mở rộng cho file không có phần mở rộng
   * @param storageKey Key gốc của file
   * @param extension Phần mở rộng muốn thêm (mặc định: 'txt')
   * @returns Key mới với phần mở rộng
   */
  static createExtendedKey(storageKey: string, extension: string = 'txt'): string {
    return `${storageKey}.${extension}`;
  }

  /**
   * Chuyển đổi từ entity sang DTO
   * @param file Entity KnowledgeFile
   * @param downloadURL URL tải xuống file
   * @param vectorStoreId ID của vector store (nếu có)
   * @param vectorStoreName Tên của vector store (nếu có)
   * @returns FileResponseDto đã được chuyển đổi
   */
  static mapToResponseDto(
    file: KnowledgeFile,
    downloadURL: string,
    vectorStoreId?: string,
    vectorStoreName?: string
  ): FileResponseDto {
    // Lấy phần mở rộng của file từ tên file
    const extension = FileHelper.getFileExtension(file.name);

    // Đảm bảo các trường dữ liệu đúng định dạng
    const fileResponse = {
      id: file.id,
      name: file.name,
      extension: extension || 'unknown',
      storage: Number(file.storage) || 1024, // Đảm bảo storage là số, mặc định 1024 nếu không có
      ownerType: file.ownerType, // Thêm loại chủ sở hữu file
      vectorStoreId: vectorStoreId,
      vectorStoreName: vectorStoreName,
      isAssignedToVectorStore: !!vectorStoreId, // true nếu có vectorStoreId, false nếu không
      downloadURL: downloadURL || '#', // Đảm bảo luôn có URL
      createdAt: Number(file.createdAt) || Date.now(), // Đảm bảo createdAt là số
      updatedAt: Number(file.createdAt) || Date.now(), // Sử dụng createdAt thay cho updatedAt vì cột updated_at chưa tồn tại
      status: file.status, // Thêm trạng thái file vào response
    };

    // Chuyển đổi sang DTO
    return plainToInstance(FileResponseDto, fileResponse, {
      excludeExtraneousValues: true,
    });
  }
}
