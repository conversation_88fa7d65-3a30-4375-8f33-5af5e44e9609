import { ApiProperty } from '@nestjs/swagger';
import { Expose, Type } from 'class-transformer';
import { FileInVectorStoreDto } from './file-in-vector-store.dto';
import { OwnerType } from '@shared/enums';

export class VectorStoreResponseDto {
  @ApiProperty({
    description: 'ID của vector store',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000',
  })
  @Expose()
  storeId: string;

  @ApiProperty({
    description: 'Tên vector store',
    example: 'VectorStoreAI',
  })
  @Expose()
  storeName: string;

  @ApiProperty({
    description: 'Dung lượng đã dùng (bytes)',
    example: 1024,
  })
  @Expose()
  size: number;

  @ApiProperty({
    description: 'Số lượng agents sử dụng vector store này',
    example: 2,
  })
  @Expose()
  agents: number;

  @ApiProperty({
    description: 'Số lượng files trong vector store',
    example: 3,
  })
  @Expose()
  files: number;

  @ApiProperty({
    description: 'Thời điểm tạo (Unix timestamp)',
    example: 1629026400000,
  })
  @Expose()
  createdAt: number;

  @ApiProperty({
    description: 'Thời điểm cập nhật (Unix timestamp)',
    example: 1629026400000,
  })
  @Expose()
  updatedAt: number;

  @ApiProperty({
    description: 'Loại chủ sở hữu vector store',
    enum: OwnerType,
    example: OwnerType.ADMIN,
  })
  @Expose()
  ownerType: OwnerType;

  @ApiProperty({
    description: 'Danh sách các file trong vector store',
    type: [FileInVectorStoreDto],
    required: false,
  })
  @Expose()
  @Type(() => FileInVectorStoreDto)
  fileList?: FileInVectorStoreDto[];
}
