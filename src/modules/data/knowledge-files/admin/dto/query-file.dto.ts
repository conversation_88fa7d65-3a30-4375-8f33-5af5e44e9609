import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { QueryDto, SortDirection } from '@common/dto/query.dto';
import { OwnerType } from '@shared/enums';

export class QueryFileDto extends QueryDto {
  @ApiProperty({
    description: 'ID của vector store để lọc file',
    example: 'vs_123e4567-e89b-12d3-a456-426614174000',
    required: false,
  })
  @IsOptional()
  @IsString()
  vectorStoreId?: string;

  @ApiProperty({
    description: 'Lọc theo định dạng file (ví dụ: "pdf,docx,txt")',
    example: 'pdf,docx',
    required: false,
  })
  @IsOptional()
  @IsString()
  extensions?: string;

  @ApiProperty({
    description: 'Trường sắp xếp',
    enum: ['name', 'createdAt', 'storage'],
    default: 'createdAt',
    required: false,
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createdAt';

  @ApiProperty({
    description: 'Hướng sắp xếp',
    enum: SortDirection,
    default: SortDirection.DESC,
    required: false,
  })
  @IsOptional()
  @IsEnum(SortDirection)
  sortDirection?: SortDirection = SortDirection.DESC;

  @ApiProperty({
    description: 'Lọc theo loại chủ sở hữu (USER hoặc ADMIN)',
    enum: OwnerType,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnerType)
  ownerType?: OwnerType;
}
