import { ApiProperty } from '@nestjs/swagger';
import { ArrayMinSize, IsArray, IsNotEmpty, IsString } from 'class-validator';

/**
 * DTO để cập nhật trạng thái của các file tri thức
 */
export class UpdateFileStatusDto {
  @ApiProperty({
    description: 'Danh sách ID của các file cần cập nhật trạng thái',
    example: ['a1b2c3d4-e5f6-7890-abcd-ef1234567890', 'b2c3d4e5-f6a7-8901-bcde-f01234567890'],
    type: [String],
  })
  @IsArray({ message: 'fileIds phải là một mảng' })
  @ArrayMinSize(1, { message: '<PERSON>ải có ít nhất một file ID' })
  @IsString({ each: true, message: 'Mỗi file ID phải là chuỗi' })
  @IsNotEmpty({ each: true, message: 'File ID không được để trống' })
  fileIds: string[];
}
