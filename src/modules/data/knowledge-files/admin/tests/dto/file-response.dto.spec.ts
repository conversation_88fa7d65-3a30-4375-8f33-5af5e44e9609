import { plainToInstance } from 'class-transformer';
import { FileResponseDto } from '../../dto/file-response.dto';
import { OwnerType } from '@shared/enums';

describe('FileResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Test File.pdf',
      extension: 'pdf',
      storage: 1024,
      ownerType: OwnerType.USER,
      downloadURL: 'https://redaivn.hn.ss.bfcplatform.vn/knowledge/DOCUMENT/2025/06/test-file.pdf',
      vectorStoreId: 'vs_123e4567-e89b-12d3-a456-426614174000',
      vectorStoreName: 'Test Vector Store',
      isAssignedToVectorStore: true,
      createdAt: 1629026400,
      updatedAt: 1629026400,
      extraField: 'This should be excluded'
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.id).toBe(plainObject.id);
    expect(dto.name).toBe(plainObject.name);
    expect(dto.extension).toBe(plainObject.extension);
    expect(dto.storage).toBe(plainObject.storage);
    expect(dto.ownerType).toBe(plainObject.ownerType);
    expect(dto.downloadURL).toBe(plainObject.downloadURL);
    expect(dto.vectorStoreId).toBe(plainObject.vectorStoreId);
    expect(dto.vectorStoreName).toBe(plainObject.vectorStoreName);
    expect(dto.isAssignedToVectorStore).toBe(plainObject.isAssignedToVectorStore);
    expect(dto.createdAt).toBe(plainObject.createdAt);
    expect(dto.updatedAt).toBe(plainObject.updatedAt);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng với các trường tùy chọn', () => {
    // Arrange
    const plainObject = {
      id: '123e4567-e89b-12d3-a456-426614174000',
      name: 'Test File.pdf',
      extension: 'pdf',
      storage: 1024,
      ownerType: OwnerType.ADMIN,
      downloadURL: 'https://redaivn.hn.ss.bfcplatform.vn/knowledge/DOCUMENT/2025/06/test-file.pdf',
      isAssignedToVectorStore: false,
      createdAt: 1629026400,
      updatedAt: 1629026400
    };

    // Act
    const dto = plainToInstance(FileResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.id).toBe(plainObject.id);
    expect(dto.name).toBe(plainObject.name);
    expect(dto.extension).toBe(plainObject.extension);
    expect(dto.storage).toBe(plainObject.storage);
    expect(dto.ownerType).toBe(plainObject.ownerType);
    expect(dto.downloadURL).toBe(plainObject.downloadURL);
    expect(dto.vectorStoreId).toBeUndefined();
    expect(dto.vectorStoreName).toBeUndefined();
    expect(dto.isAssignedToVectorStore).toBe(plainObject.isAssignedToVectorStore);
    expect(dto.createdAt).toBe(plainObject.createdAt);
    expect(dto.updatedAt).toBe(plainObject.updatedAt);
  });

  it('nên chuyển đổi một mảng các đối tượng', () => {
    // Arrange
    const plainObjects = [
      {
        id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Test File 1.pdf',
        extension: 'pdf',
        storage: 1024,
        ownerType: OwnerType.USER,
        downloadURL: 'https://redaivn.hn.ss.bfcplatform.vn/knowledge/DOCUMENT/2025/06/test-file-1.pdf',
        createdAt: 1629026400,
        updatedAt: 1629026400
      },
      {
        id: '123e4567-e89b-12d3-a456-426614174001',
        name: 'Test File 2.docx',
        extension: 'docx',
        storage: 2048,
        ownerType: OwnerType.ADMIN,
        downloadURL: 'https://redaivn.hn.ss.bfcplatform.vn/knowledge/DOCUMENT/2025/06/test-file-2.docx',
        createdAt: 1629026500,
        updatedAt: 1629026500
      }
    ];

    // Act
    const dtos = plainToInstance(FileResponseDto, plainObjects, { excludeExtraneousValues: true });

    // Assert
    expect(dtos).toBeDefined();
    expect(dtos.length).toBe(2);
    expect(dtos[0].id).toBe(plainObjects[0].id);
    expect(dtos[0].name).toBe(plainObjects[0].name);
    expect(dtos[0].ownerType).toBe(plainObjects[0].ownerType);
    expect(dtos[1].id).toBe(plainObjects[1].id);
    expect(dtos[1].name).toBe(plainObjects[1].name);
    expect(dtos[1].ownerType).toBe(plainObjects[1].ownerType);
  });
});
