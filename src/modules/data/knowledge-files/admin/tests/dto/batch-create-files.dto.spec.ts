import { validate } from 'class-validator';
import { plainToInstance } from 'class-transformer';
import { BatchCreateFilesDto } from '../../dto/batch-create-files.dto';

describe('BatchCreateFilesDto', () => {
  it('nên xác thực DTO hợp lệ với nhiều file', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File 1.pdf',
          mime: 'application/pdf',
          storage: 1024
        },
        {
          name: 'Test File 2.docx',
          mime: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          storage: 2048
        }
      ]
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên xác thực DTO hợp lệ với một file', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          mime: 'application/pdf',
          storage: 1024
        }
      ]
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBe(0);
  });

  it('nên thất bại khi files là mảng rỗng', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: []
    });

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('arrayMinSize');
  });

  it('nên thất bại khi files không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {});

    // Act
    const errors = await validate(dto);

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    expect(errors[0].constraints).toHaveProperty('isArray');
  });

  it('nên thất bại khi tên file không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          mime: 'application/pdf',
          storage: 1024
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('name');
    expect(errorJson).toContain('isNotEmpty');
  });

  it('nên thất bại khi tên file vượt quá độ dài tối đa', async () => {
    // Arrange
    const longName = 'A'.repeat(256);
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: longName,
          mime: 'application/pdf',
          storage: 1024
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('name');
    expect(errorJson).toContain('maxLength');
  });

  it('nên thất bại khi mime không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          storage: 1024
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('mime');
    expect(errorJson).toContain('isNotEmpty');
  });

  it('nên thất bại khi mime vượt quá độ dài tối đa', async () => {
    // Arrange
    const longMime = 'A'.repeat(101);
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          mime: longMime,
          storage: 1024
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('mime');
    expect(errorJson).toContain('maxLength');
  });

  it('nên thất bại khi storage không được cung cấp', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          mime: 'application/pdf'
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('storage');
    expect(errorJson).toContain('isNotEmpty');
  });

  it('nên thất bại khi storage không phải là số', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          mime: 'application/pdf',
          storage: 'not-a-number'
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('storage');
    expect(errorJson).toContain('isNumber');
  });

  it('nên thất bại khi storage là số âm', async () => {
    // Arrange
    const dto = plainToInstance(BatchCreateFilesDto, {
      files: [
        {
          name: 'Test File.pdf',
          mime: 'application/pdf',
          storage: -1024
        }
      ]
    });

    // Act
    const errors = await validate(dto, { validationError: { target: false } });

    // Assert
    expect(errors.length).toBeGreaterThan(0);
    const errorJson = JSON.stringify(errors);
    expect(errorJson).toContain('storage');
    expect(errorJson).toContain('isPositive');
  });
});
