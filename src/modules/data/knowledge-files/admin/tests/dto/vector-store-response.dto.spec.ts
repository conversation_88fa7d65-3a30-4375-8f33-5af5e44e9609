import { plainToInstance } from 'class-transformer';
import { VectorStoreResponseDto } from '../../dto/vector-store-response.dto';
import { OwnerType } from '@shared/enums';

describe('VectorStoreResponseDto', () => {
  it('nên chuyển đổi đúng từ plain object sang DTO', () => {
    // Arrange
    const plainObject = {
      storeId: 'vs_123e4567-e89b-12d3-a456-426614174000',
      storeName: 'Test Vector Store',
      size: 1024,
      agents: 2,
      files: 3,
      createdAt: 1629026400,
      updatedAt: 1629026400,
      ownerType: OwnerType.ADMIN,
      fileList: [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test File.pdf',
          extension: 'pdf',
          storage: 1024,
          createdAt: 1629026400,
          status: 'APPROVED'
        }
      ],
      extraField: 'This should be excluded'
    };

    // Act
    const dto = plainToInstance(VectorStoreResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.storeId).toBe(plainObject.storeId);
    expect(dto.storeName).toBe(plainObject.storeName);
    expect(dto.size).toBe(plainObject.size);
    expect(dto.agents).toBe(plainObject.agents);
    expect(dto.files).toBe(plainObject.files);
    expect(dto.createdAt).toBe(plainObject.createdAt);
    expect(dto.updatedAt).toBe(plainObject.updatedAt);
    expect(dto.ownerType).toBe(plainObject.ownerType);
    expect(dto.fileList).toBeDefined();
    expect(dto.fileList?.length).toBe(1);
    expect(dto.fileList?.[0].id).toBe(plainObject.fileList[0].id);
    expect((dto as any).extraField).toBeUndefined();
  });

  it('nên chuyển đổi đúng với các trường tùy chọn', () => {
    // Arrange
    const plainObject = {
      storeId: 'vs_123e4567-e89b-12d3-a456-426614174000',
      storeName: 'Test Vector Store',
      size: 1024,
      agents: 2,
      files: 3,
      createdAt: 1629026400,
      updatedAt: 1629026400,
      ownerType: OwnerType.ADMIN
    };

    // Act
    const dto = plainToInstance(VectorStoreResponseDto, plainObject, { excludeExtraneousValues: true });

    // Assert
    expect(dto).toBeDefined();
    expect(dto.storeId).toBe(plainObject.storeId);
    expect(dto.storeName).toBe(plainObject.storeName);
    expect(dto.size).toBe(plainObject.size);
    expect(dto.agents).toBe(plainObject.agents);
    expect(dto.files).toBe(plainObject.files);
    expect(dto.createdAt).toBe(plainObject.createdAt);
    expect(dto.updatedAt).toBe(plainObject.updatedAt);
    expect(dto.ownerType).toBe(plainObject.ownerType);
    expect(dto.fileList).toBeUndefined();
  });

  it('nên chuyển đổi một mảng các đối tượng', () => {
    // Arrange
    const plainObjects = [
      {
        storeId: 'vs_123e4567-e89b-12d3-a456-426614174000',
        storeName: 'Test Vector Store 1',
        size: 1024,
        agents: 2,
        files: 3,
        createdAt: 1629026400,
        updatedAt: 1629026400
      },
      {
        storeId: 'vs_123e4567-e89b-12d3-a456-426614174001',
        storeName: 'Test Vector Store 2',
        size: 2048,
        agents: 1,
        files: 5,
        createdAt: 1629026500,
        updatedAt: 1629026500
      }
    ];

    // Act
    const dtos = plainToInstance(VectorStoreResponseDto, plainObjects, { excludeExtraneousValues: true });

    // Assert
    expect(dtos).toBeDefined();
    expect(dtos.length).toBe(2);
    expect(dtos[0].storeId).toBe(plainObjects[0].storeId);
    expect(dtos[0].storeName).toBe(plainObjects[0].storeName);
    expect(dtos[1].storeId).toBe(plainObjects[1].storeId);
    expect(dtos[1].storeName).toBe(plainObjects[1].storeName);
  });
});
