import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  HttpStatus,
  Post,
  Query,
  UseGuards,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiBody,
  ApiExtraModels,
  ApiOperation,
  ApiResponse,
  ApiTags,
} from '@nestjs/swagger';
import { KnowledgeFileAdminService } from '../services';
import {
  BatchCreateFilesDto,
  DeleteFilesDto,
  FileResponseDto,
  QueryFileDto,
  QueryUnassignedFileDto,
} from '../dto';
import { ApiResponseDto, PaginatedResult } from '@common/response/api-response-dto';
import { SWAGGER_API_TAGS } from '@common/swagger/swagger.tags';
import { CurrentEmployee } from '@/modules/auth/decorators/current-employee.decorator';
import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { PermissionsGuard } from '@modules/auth/guards/permissions.guard';
import { ApiErrorResponse } from '@common/decorators/api-error-response.decorator';
import { ApiErrorResponseDto } from '@common/dto/api-error-response.dto';
import { KNOWLEDGE_FILE_ERROR_CODES } from '../../exceptions';
import { Roles } from '@modules/auth/decorators/roles.decorator';

/**
 * Controller xử lý API liên quan đến quản lý file tri thức cho admin
 * Cung cấp các endpoint để tạo, xem danh sách và xóa file tri thức
 * Admin có thể quản lý tất cả các file tri thức trong hệ thống
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_KNOWLEDGE_FILES)
@ApiExtraModels(
  ApiResponseDto,
  FileResponseDto,
  PaginatedResult,
  ApiErrorResponseDto,
  BatchCreateFilesDto,
  DeleteFilesDto,
  QueryFileDto,
  QueryUnassignedFileDto
)
@ApiBearerAuth('JWT-auth')
@UseGuards(JwtEmployeeGuard, PermissionsGuard)
@Roles('admin')
@Controller('admin/knowledge-files')
export class KnowledgeFileAdminController {
  constructor(
    private readonly knowledgeFileAdminService: KnowledgeFileAdminService,
  ) {}

  /**
   * Thêm nhiều file tri thức
   * Tạo nhiều file tri thức mới với trạng thái mặc định là DRAFT
   * Trả về danh sách các URL ký sẵn để upload file
   * Sau khi tạo file, cần upload nội dung file lên các URL ký sẵn để hoàn tất quá trình tạo file
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param batchCreateFilesDto DTO chứa thông tin các file cần tạo
   * @returns Danh sách các URL ký sẵn để upload file
   */
  @ApiOperation({
    summary: 'Thêm nhiều file tri thức',
    description: 'Tạo nhiều file tri thức mới và trả về các URL ký sẵn để upload nội dung file'
  })
  @ApiBody({ type: BatchCreateFilesDto })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: 'Đã tạo files tri thức thành công.',
    schema: ApiResponseDto.getSchema(Array)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_CREATE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR
  )
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Dữ liệu đầu vào không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Dữ liệu đầu vào không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền thực hiện thao tác này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền thực hiện thao tác này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Post('batch')
  @HttpCode(HttpStatus.CREATED)
  async batchCreateFiles(
    @Body() batchCreateFilesDto: BatchCreateFilesDto,
    @CurrentEmployee('id') employeeId: number,
  ): Promise<ApiResponseDto<string[]>> {
    const result = await this.knowledgeFileAdminService.batchCreateFiles(
      batchCreateFilesDto,
      employeeId,
    );

    return ApiResponseDto.created<string[]>(result, 'Đã tạo files tri thức thành công.');
  }

  /**
   * Lấy danh sách files tri thức với phân trang, tìm kiếm, lọc và sắp xếp
   * Hỗ trợ các tham số query:
   * - search: Từ khóa tìm kiếm (tìm theo tên file)
   * - extensions: Lọc theo định dạng file (ví dụ: "pdf,docx,txt")
   * - vectorStoreId: Lọc theo ID của vector store
   * - page: Số trang (bắt đầu từ 1)
   * - limit: Số lượng file trên một trang
   * - sortBy: Sắp xếp theo trường (name, createdAt, storage)
   * - sortDirection: Thứ tự sắp xếp (asc, desc)
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param queryDto DTO chứa các tham số truy vấn
   * @returns Danh sách file tri thức phân trang
   */
  @ApiOperation({
    summary: 'Lấy danh sách files tri thức',
    description: 'Lấy danh sách files tri thức với phân trang, tìm kiếm, lọc và sắp xếp. Bao gồm thông tin về việc file đã được gán vào vector store hay chưa (isAssignedToVectorStore)'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách file thành công.',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.VECTOR_STORE_NOT_FOUND
  )
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Tham số không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Tham số không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Bạn không có quyền truy cập danh sách file này.',
    schema: {
      properties: {
        code: { type: 'number', example: 403 },
        message: {
          type: 'string',
          example: 'Bạn không có quyền truy cập danh sách file này.',
        },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Get()
  @HttpCode(HttpStatus.OK)
  async getFiles(
    @Query() queryDto: QueryFileDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<PaginatedResult<FileResponseDto>>> {
    const result = await this.knowledgeFileAdminService.getFiles(
      queryDto,
      employeeId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách file thành công.',
    );
  }

  /**
   * Lấy danh sách file tri thức chưa gắn với vector store cụ thể
   */
  @ApiOperation({
    summary: 'Lấy danh sách file tri thức chưa gắn với vector store cụ thể',
    description: 'Lấy danh sách file tri thức của admin chưa được gắn với vector store cụ thể. Yêu cầu truyền vectorStoreId để lấy các file chưa gắn với vector store đó (nhưng có thể đã gắn với vector store khác).'
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Lấy danh sách file chưa gắn với vector store cụ thể thành công.',
    schema: ApiResponseDto.getPaginatedSchema(FileResponseDto),
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_LIST_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR
  )
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: 'Tham số không hợp lệ.',
    schema: {
      properties: {
        code: { type: 'number', example: 400 },
        message: { type: 'string', example: 'Tham số không hợp lệ.' },
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Lỗi máy chủ, vui lòng thử lại sau.',
    schema: {
      properties: {
        code: { type: 'number', example: 500 },
        message: {
          type: 'string',
          example: 'Lỗi máy chủ, vui lòng thử lại sau.',
        },
      },
    },
  })
  @Get('unassigned')
  @HttpCode(HttpStatus.OK)
  async getUnassignedFiles(
    @Query() queryDto: QueryUnassignedFileDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<PaginatedResult<FileResponseDto>>> {
    const result = await this.knowledgeFileAdminService.getUnassignedFiles(
      queryDto,
      employeeId,
    );
    return ApiResponseDto.paginated(
      result,
      'Lấy danh sách file chưa gắn với vector store cụ thể thành công.',
    );
  }

  /**
   * Xóa nhiều file tri thức (xóa mềm - chuyển trạng thái sang DELETED)
   * Thực hiện xóa mềm nhiều file tri thức bằng cách cập nhật trạng thái thành DELETED
   * File đã xóa sẽ không xuất hiện trong danh sách file mặc định
   * Chỉ admin mới có quyền xóa file
   * Nếu file đang được gán vào vector store, sẽ cập nhật dung lượng của vector store
   *
   * @param employeeId ID của nhân viên thực hiện thao tác
   * @param deleteFilesDto DTO chứa danh sách ID file cần xóa
   * @returns Thông tin về việc xóa file thành công, bao gồm số lượng file đã xóa và danh sách file thất bại
   */
  @ApiOperation({
    summary: 'Xóa nhiều file tri thức',
    description: 'Xóa mềm nhiều file tri thức bằng cách chuyển trạng thái sang DELETED'
  })
  @ApiBody({ type: DeleteFilesDto })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Xóa file thành công.',
    schema: ApiResponseDto.getSchema(Object)
  })
  @ApiErrorResponse(
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_DELETE_ERROR,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_NOT_FOUND,
    KNOWLEDGE_FILE_ERROR_CODES.KNOWLEDGE_FILE_PERMISSION_ERROR
  )
  @Delete()
  @HttpCode(HttpStatus.OK)
  async deleteFiles(
    @Body() deleteFilesDto: DeleteFilesDto,
    @CurrentEmployee('id') employeeId: number
  ): Promise<ApiResponseDto<{success: boolean; deletedCount: number; failedItems?: {id: string; reason: string}[]}>> {
    const result = await this.knowledgeFileAdminService.deleteFile(deleteFilesDto.fileIds, employeeId);
    return ApiResponseDto.deleted(result, `Đã xóa ${result.deletedCount} file thành công.`);
  }

}