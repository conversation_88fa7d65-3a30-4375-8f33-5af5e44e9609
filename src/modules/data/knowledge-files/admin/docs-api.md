# Tài liệu API: Admin - Thống Kê Hệ Thống

## API Endpoint
`/api/admin/statistics`

## Method
`GET`

## Mô tả
API này được sử dụng để lấy thống kê về số lượng và dung lượng vector stores, files trong hệ thống. Chỉ quản trị viên mới có quyền truy cập API này.

## Query Parameters
- **`fromDate`**: `string` (tùy chọn) - <PERSON><PERSON><PERSON> bắt đầu thống kê (định dạng `YYYY-MM-DD`)
- **`toDate`**: `string` (tùy chọn) - <PERSON><PERSON><PERSON> kết thúc thống kê (định dạng `YYYY-MM-DD`)
- **`groupBy`**: `string` (tùy chọn) - Nhóm thống kê theo (`day`, `week`, `month`, `year`), mặc định là `month`

## Ví dụ Request
```
GET /api/admin/statistics?fromDate=2023-01-01&toDate=2023-12-31&groupBy=month
```

## Response

### Thành công
- **Code**: `200`
- **Response Body**:
```json
{
  "code": 200,
  "data": {
    "summary": {
      "totalVectorStores": 512,
      "totalFiles": 3840,
      "totalStorage": 20971520,
      "totalStorageFormatted": "20 GB",
      "avgFilesPerVectorStore": 7.5,
      "avgStoragePerFile": 5461,
      "avgStoragePerFileFormatted": "5.33 KB"
    },
    "topUsers": [
      {
        "userId": 1234,
        "username": "user1",
        "email": "<EMAIL>",
        "vectorStores": 25,
        "files": 180,
        "storage": 2097152,
        "storageFormatted": "2 GB"
      },
      {
        "userId": 5678,
        "username": "user2",
        "email": "<EMAIL>",
        "vectorStores": 18,
        "files": 145,
        "storage": 1572864,
        "storageFormatted": "1.5 GB"
      }
    ],
    "timeline": [
      {
        "period": "2023-01",
        "newVectorStores": 45,
        "newFiles": 320,
        "newStorage": 1572864,
        "newStorageFormatted": "1.5 GB",
        "totalVectorStores": 45,
        "totalFiles": 320,
        "totalStorage": 1572864,
        "totalStorageFormatted": "1.5 GB"
      },
      {
        "period": "2023-02",
        "newVectorStores": 52,
        "newFiles": 380,
        "newStorage": 1835008,
        "newStorageFormatted": "1.75 GB",
        "totalVectorStores": 97,
        "totalFiles": 700,
        "totalStorage": 3407872,
        "totalStorageFormatted": "3.25 GB"
      }
    ],
    "fileTypes": [
      {
        "extension": "pdf",
        "count": 2150,
        "storage": 12582912,
        "storageFormatted": "12 GB",
        "percentage": 60
      },
      {
        "extension": "docx",
        "count": 980,
        "storage": 5242880,
        "storageFormatted": "5 GB",
        "percentage": 25
      },
      {
        "extension": "txt",
        "count": 710,
        "storage": 3145728,
        "storageFormatted": "3 GB",
        "percentage": 15
      }
    ]
  },
  "message": "Lấy thống kê hệ thống thành công."
}
```

### Thất bại
- **Code**: `403`
    - **Response Body**:
      ```json
      {
        "code": 403,
        "message": "Bạn không có quyền truy cập thống kê hệ thống."
      }
      ```
- **Code**: `400`
    - **Response Body**:
      ```json
      {
        "code": 400,
        "message": "Tham số không hợp lệ."
      }
      ```
- **Code**: `500`
    - **Response Body**:
      ```json
      {
        "code": 500,
        "message": "Lỗi máy chủ, vui lòng thử lại sau."
      }
      ```

## Ghi chú
- API này yêu cầu quyền quản trị viên để truy cập.
- Đảm bảo các tham số `fromDate` và `toDate` được định dạng đúng (`YYYY-MM-DD`) để tránh lỗi `400`.