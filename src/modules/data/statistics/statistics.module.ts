import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { User } from '@modules/user/entities/user.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { Usage } from './entities/usage.entity';
import { StatisticsUserController } from './user/controllers/statistics-user.controller';
import { StatisticsAdminController } from './admin/controllers/statistics-admin.controller';

import { StatisticsUserService } from './user/services/statistics-user.service';
import { StatisticsAdminService } from './admin/services/statistics-admin.service';
import { UsageRepository } from './repositories/usage.repository';

@Module({
  imports: [
    TypeOrmModule.forFeature([User, KnowledgeFile, Media, Url, VectorStore, Usage]),
  ],
  controllers: [StatisticsUserController, StatisticsAdminController],
  providers: [
    StatisticsUserService,
    StatisticsAdminService,
    UsageRepository,
  ],
  exports: [StatisticsUserService, StatisticsAdminService, UsageRepository],
})
export class StatisticsModule {}
