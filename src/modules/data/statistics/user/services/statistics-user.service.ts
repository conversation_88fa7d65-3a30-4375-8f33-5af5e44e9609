import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { UserStorageResponseDto, UserStatisticsResponseDto } from '../../dto/statistics-response.dto';
import { plainToInstance } from 'class-transformer';
import { UsageRepository } from '../../repositories/usage.repository';
import { FileSizeFormatter } from '@shared/utils';
import { AppException } from '@common/exceptions';
import { STATISTICS_ERROR_CODES } from '../../exceptions/statistics.exception';
import { Usage } from '../../entities/usage.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { OwnerType } from '@shared/enums';

/**
 * Service xử lý các thống kê dữ liệu cho user
 * Chỉ sử dụng dữ liệu từ bảng usages, không tính toán từ các bảng khác
 */
@Injectable()
export class StatisticsUserService {
  private readonly logger = new Logger(StatisticsUserService.name);

  constructor(
    private readonly usageRepository: UsageRepository,
    @InjectRepository(Media)
    private readonly mediaRepository: Repository<Media>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(VectorStore)
    private readonly vectorStoreRepository: Repository<VectorStore>,
  ) {}

  /**
   * Lấy thống kê dung lượng dữ liệu của một người dùng từ bảng usages
   * @param userId ID của người dùng
   * @returns Promise<UserStorageResponseDto> Thống kê dung lượng dữ liệu
   */
  async getUserStorageUsage(userId: number): Promise<UserStorageResponseDto> {
    this.logger.log(`User ${userId} đang lấy thống kê dung lượng dữ liệu từ bảng usages...`);

    // Lấy thông tin usage từ bảng usages
    const usage = await this.usageRepository.findByUserAndCategory(userId, 'DATA');
    if (!usage) {
      this.logger.warn(`Không tìm thấy thông tin usage cho user ${userId}`);
      throw new AppException(
        STATISTICS_ERROR_CODES.USER_USAGE_NOT_FOUND,
        `Không tìm thấy thông tin sử dụng dung lượng cho người dùng ${userId}`
      );
    }

    try {
      // Tính phần trăm sử dụng
      const usagePercentage = usage.usageLimit > 0 ? (usage.currentUsage / usage.usageLimit) * 100 : 0;

      this.logger.log(
        `User ${userId} - Thống kê từ DB: ` +
        `Limit=${FileSizeFormatter.formatBytes(usage.usageLimit)}, ` +
        `Current=${FileSizeFormatter.formatBytes(usage.currentUsage)}, ` +
        `Remaining=${FileSizeFormatter.formatBytes(usage.remainingValue)}, ` +
        `Percentage=${usagePercentage.toFixed(2)}%`
      );

      // Chuyển đổi sang DTO - chỉ sử dụng dữ liệu từ bảng usages
      return plainToInstance(
        UserStorageResponseDto,
        {
          usageLimit: usage.usageLimit,
          usageLimitFormatted: FileSizeFormatter.formatBytes(usage.usageLimit),
          currentUsage: usage.currentUsage,
          currentUsageFormatted: FileSizeFormatter.formatBytes(usage.currentUsage),
          remainingValue: usage.remainingValue,
          remainingValueFormatted: FileSizeFormatter.formatBytes(usage.remainingValue),
          usageUnit: usage.usageUnit,
          usagePercentage: Math.round(usagePercentage * 100) / 100,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`User ${userId} - Lỗi khi lấy thống kê dung lượng dữ liệu: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR,
        `Lỗi dịch vụ khi lấy thống kê cho user ${userId}: ${error.message}`
      );
    }
  }

  /**
   * Lấy tất cả thông tin usage của user theo các category khác nhau
   * @param userId ID của người dùng
   * @returns Promise<Usage[]> Danh sách usage theo category
   */
  async getAllUserUsages(userId: number): Promise<Usage[]> {
    this.logger.log(`User ${userId} đang lấy tất cả thông tin usage...`);

    try {
      const usages = await this.usageRepository.findAllByUser(userId);

      this.logger.log(`User ${userId} có ${usages.length} bản ghi usage`);

      return usages;
    } catch (error) {
      this.logger.error(`User ${userId} - Lỗi khi lấy tất cả usage: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_SERVICE_ERROR,
        `Lỗi khi lấy tất cả thông tin usage cho user ${userId}: ${error.message}`
      );
    }
  }

  /**
   * Lấy thống kê số lượng dữ liệu của người dùng
   * Bao gồm số lượng media files, knowledge files, URLs và vector stores
   * @param userId ID của người dùng
   * @returns Promise<UserStatisticsResponseDto> Thống kê số lượng dữ liệu
   */
  async getUserStatistics(userId: number): Promise<UserStatisticsResponseDto> {
    this.logger.log(`User ${userId} đang lấy thống kê số lượng dữ liệu...`);

    try {
      // Thực hiện các truy vấn đếm song song để tối ưu hiệu suất
      const [totalMedia, totalKnowledgeFiles, totalUrls, totalVectorStores] = await Promise.all([
        this.mediaRepository.count({ where: { ownedBy: userId } }),
        this.knowledgeFileRepository.count({
          where: {
            ownedBy: userId,
            ownerType: OwnerType.USER
          }
        }),
        this.urlRepository.count({ where: { ownedBy: userId } }),
        this.vectorStoreRepository.count({
          where: {
            ownerId: userId,
            ownerType: OwnerType.USER
          }
        }),
      ]);

      this.logger.log(
        `User ${userId} - Thống kê số lượng: Media=${totalMedia}, KnowledgeFiles=${totalKnowledgeFiles}, ` +
        `URLs=${totalUrls}, VectorStores=${totalVectorStores}`
      );

      // Chuyển đổi sang DTO
      return plainToInstance(
        UserStatisticsResponseDto,
        {
          totalMedia,
          totalKnowledgeFiles,
          totalUrls,
          totalVectorStores,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`User ${userId} - Lỗi khi lấy thống kê số lượng dữ liệu: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_CALCULATION_ERROR,
        `Lỗi khi tính toán thống kê số lượng dữ liệu cho user ${userId}: ${error.message}`
      );
    }
  }
}
