import { Injectable, Logger } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { User } from '@modules/user/entities/user.entity';
import { KnowledgeFile } from '@modules/data/knowledge-files/entities/knowledge-file.entity';
import { Media } from '@modules/data/media/entities/media.entity';
import { Url } from '@modules/data/url/entities/url.entity';
import { VectorStore } from '@modules/data/knowledge-files/entities/vector-store.entity';
import { StatisticsResponseDto } from '../../dto/statistics-response.dto';
import { plainToInstance } from 'class-transformer';
import { AppException } from '@common/exceptions';
import { STATISTICS_ERROR_CODES } from '../../exceptions/statistics.exception';

/**
 * Service xử lý các thống kê dữ liệu cho admin
 * Cung cấp các phương thức để lấy thống kê tổng quan về số lượng người dùng, knowledge file, media, URL và vector store
 */
@Injectable()
export class StatisticsAdminService {
  private readonly logger = new Logger(StatisticsAdminService.name);

  constructor(
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    @InjectRepository(KnowledgeFile)
    private readonly knowledgeFileRepository: Repository<KnowledgeFile>,
    @InjectRepository(Media)
    private readonly mediaRepository: Repository<Media>,
    @InjectRepository(Url)
    private readonly urlRepository: Repository<Url>,
    @InjectRepository(VectorStore)
    private readonly vectorStoreRepository: Repository<VectorStore>,
  ) {}

  /**
   * Lấy thống kê tổng số lượng người dùng, knowledge file, media, URL và vector store
   * Chỉ admin mới có quyền truy cập phương thức này
   * @returns Promise<StatisticsResponseDto> Thống kê dữ liệu tổng quan
   */
  async getStatistics(): Promise<StatisticsResponseDto> {
    this.logger.log('Admin đang lấy thống kê dữ liệu tổng quan...');

    try {
      // Thực hiện các truy vấn đếm song song để tối ưu hiệu suất
      const [totalUsers, totalKnowledgeFiles, totalMedia, totalUrls, totalVectorStores] = await Promise.all([
        this.userRepository.count(),
        this.knowledgeFileRepository.count(),
        this.mediaRepository.count(),
        this.urlRepository.count(),
        this.vectorStoreRepository.count(),
      ]);

      this.logger.log(
        `Admin - Thống kê tổng quan: Users=${totalUsers}, KnowledgeFiles=${totalKnowledgeFiles}, ` +
        `Media=${totalMedia}, URLs=${totalUrls}, VectorStores=${totalVectorStores}`
      );

      // Chuyển đổi sang DTO
      return plainToInstance(
        StatisticsResponseDto,
        {
          totalUsers,
          totalKnowledgeFiles,
          totalMedia,
          totalUrls,
          totalVectorStores,
        },
        { excludeExtraneousValues: true },
      );
    } catch (error) {
      this.logger.error(`Admin - Lỗi khi lấy thống kê dữ liệu tổng quan: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_CALCULATION_ERROR,
        `Lỗi khi tính toán thống kê tổng quan: ${error.message}`
      );
    }
  }

  /**
   * Lấy thống kê chi tiết theo từng loại dữ liệu
   * @returns Promise<StatisticsResponseDto & { timestamp: string; generatedBy: string }> Thống kê chi tiết
   */
  async getDetailedStatistics(): Promise<StatisticsResponseDto & { timestamp: string; generatedBy: string }> {
    this.logger.log('Admin đang lấy thống kê chi tiết...');

    try {
      // Có thể mở rộng thêm các thống kê chi tiết khác cho admin
      const statistics = await this.getStatistics();

      // Thêm thông tin chi tiết nếu cần
      const detailedStats = {
        ...statistics,
        timestamp: new Date().toISOString(),
        generatedBy: 'admin',
      };

      this.logger.log('Admin đã lấy thống kê chi tiết thành công');

      return detailedStats;
    } catch (error) {
      this.logger.error(`Admin - Lỗi khi lấy thống kê chi tiết: ${error.message}`, error.stack);
      throw new AppException(
        STATISTICS_ERROR_CODES.STATISTICS_CALCULATION_ERROR,
        `Lỗi khi tính toán thống kê chi tiết: ${error.message}`
      );
    }
  }
}
