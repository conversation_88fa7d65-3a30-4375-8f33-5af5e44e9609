import { Entity, PrimaryGeneratedColumn, Column, CreateDateColumn, UpdateDateColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng usages trong cơ sở dữ liệu
 * Bảng quản lý việc sử dụng tài nguyên của người dùng
 */
@Entity('usages')
export class Usage {
  /**
   * ID duy nhất của bản ghi sử dụng
   */
  @PrimaryGeneratedColumn('increment', { comment: 'ID duy nhất của bản ghi sử dụng' })
  id: number;

  /**
   * Người dùng sở hữu subscription
   */
  @Column({ 
    name: 'user_id', 
    type: 'integer', 
    nullable: false,
    comment: 'Người dùng sở hữu subscription'
  })
  userId: number;

  /**
   * Tổng số lượng tài nguyên được phép sử dụng
   */
  @Column({ 
    name: 'usage_limit', 
    type: 'bigint', 
    nullable: false, 
    default: 0,
    comment: 'Tổng số lượng tài nguyên được phép sử dụng'
  })
  usageLimit: number;

  /**
   * S<PERSON> lượng tài nguyên đã sử dụng
   */
  @Column({ 
    name: 'current_usage', 
    type: 'bigint', 
    nullable: false, 
    default: 0,
    comment: 'Số lượng tài nguyên đã sử dụng'
  })
  currentUsage: number;

  /**
   * Số lượng tài nguyên còn lại
   */
  @Column({ 
    name: 'remaining_value', 
    type: 'bigint', 
    nullable: false, 
    default: 0,
    comment: 'Số lượng tài nguyên còn lại'
  })
  remainingValue: number;

  /**
   * Đơn vị của tài nguyên
   */
  @Column({ 
    name: 'usage_unit', 
    type: 'varchar', 
    length: 50, 
    nullable: false, 
    default: 'MB',
    comment: 'Đơn vị của tài nguyên'
  })
  usageUnit: string;

  /**
   * Loại tài nguyên sử dụng
   */
  @Column({ 
    name: 'category', 
    type: 'varchar', 
    length: 20, 
    nullable: false, 
    default: 'DATA',
    comment: 'Loại tài nguyên sử dụng'
  })
  category: string;

  /**
   * Thời điểm tạo bản ghi
   */
  @CreateDateColumn({ 
    name: 'created_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'Thời điểm tạo bản ghi'
  })
  createdAt: Date;

  /**
   * Thời điểm cập nhật bản ghi
   */
  @UpdateDateColumn({ 
    name: 'updated_at',
    type: 'timestamp',
    default: () => 'CURRENT_TIMESTAMP',
    comment: 'Thời điểm cập nhật bản ghi'
  })
  updatedAt: Date;
}
