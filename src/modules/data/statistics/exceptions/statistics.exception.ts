import { HttpStatus } from '@nestjs/common';
import { ErrorCode } from '@common/exceptions';

/**
 * Error codes related to Statistics module (7000-7099)
 */
export const STATISTICS_ERROR_CODES = {
  /**
   * Error when user usage information is not found
   */
  USER_USAGE_NOT_FOUND: new ErrorCode(
    7000,
    'Không tìm thấy thông tin sử dụng dung lượng cho người dùng này',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Error when user usage information is not initialized
   */
  USER_USAGE_NOT_INITIALIZED: new ErrorCode(
    7001,
    'Thông tin sử dụng dung lượng chưa được khởi tạo cho người dùng này',
    HttpStatus.NOT_FOUND,
  ),

  /**
   * Error when usage limit is exceeded
   */
  USAGE_LIMIT_EXCEEDED: new ErrorCode(
    7002,
    'Đã vượt quá giới hạn dung lượng cho phép',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Error when usage category is invalid
   */
  INVALID_USAGE_CATEGORY: new ErrorCode(
    7003,
    'Loại tài nguyên không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Error when access to statistics is denied
   */
  STATISTICS_ACCESS_DENIED: new ErrorCode(
    7020,
    'Không có quyền truy cập thống kê này',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Error when only admin can access statistics
   */
  ADMIN_STATISTICS_ONLY: new ErrorCode(
    7021,
    'Chỉ admin mới có quyền xem thống kê tổng quan',
    HttpStatus.FORBIDDEN,
  ),

  /**
   * Error when statistics calculation fails
   */
  STATISTICS_CALCULATION_ERROR: new ErrorCode(
    7040,
    'Lỗi khi tính toán thống kê',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Error when date range is invalid
   */
  INVALID_DATE_RANGE: new ErrorCode(
    7041,
    'Khoảng thời gian không hợp lệ',
    HttpStatus.BAD_REQUEST,
  ),

  /**
   * Error when statistics service encounters an error
   */
  STATISTICS_SERVICE_ERROR: new ErrorCode(
    7060,
    'Lỗi dịch vụ thống kê',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),

  /**
   * Error when statistics data is corrupted
   */
  STATISTICS_DATA_CORRUPTED: new ErrorCode(
    7061,
    'Dữ liệu thống kê bị lỗi',
    HttpStatus.INTERNAL_SERVER_ERROR,
  ),
} as const;
