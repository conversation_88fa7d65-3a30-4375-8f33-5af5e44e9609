// <PERSON><PERSON> báo các enum và type cần thiết
type SortDirectionTypeService = {
  ASC: string;
  DESC: string;
};

const SortDirectionEnumAdminService: SortDirectionTypeService = {
  ASC: 'ASC',
  DESC: 'DESC'
};

// <PERSON>hai báo các interface cần thiết
interface CreateUrlAdminDto {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
  isActive?: boolean;
}

interface UpdateUrlAdminDto {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
  ownedBy?: number;
  isActive?: boolean;
}

// Mock UrlAdminException class
class UrlAdminException extends Error {
  constructor(errorCode: any, message?: string) {
    super(message || errorCode?.message || 'URL admin exception');
    this.name = 'UrlAdminException';
  }
}

// Mock UrlRepository class
class UrlRepositoryAdmin {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
}

// Mock UrlAdminService class
class UrlAdminServiceMock {
  constructor(
    private readonly urlRepository: any,
    private readonly urlCustomRepository: UrlRepository
  ) {}

  async findUrlById(id: string): Promise<any> {
    try {
      if (!id) {
        throw new UrlAdminException(null, 'ID URL là bắt buộc');
      }

      const url = await this.urlCustomRepository.findUrlById(id);

      if (!url) {
        throw new UrlAdminException(null, 'Không tìm thấy URL');
      }

      return url;
    } catch (error) {
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể lấy thông tin URL: ${error.message}`);
    }
  }

  async findAllUrls(queryParams: any): Promise<any> {
    try {
      // Sử dụng repository để lấy danh sách URL
      const result = await this.urlCustomRepository.findUrlsByOwner(null, queryParams);
      return result;
    } catch (error) {
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể tìm kiếm URL: ${error.message}`);
    }
  }

  async searchUrls(keyword: string, limit: number = 10): Promise<any[]> {
    try {
      // Kiểm tra tham số đầu vào
      if (!keyword || keyword.trim() === '') {
        throw new UrlAdminException(null, 'Từ khóa tìm kiếm không được để trống');
      }

      // Tìm kiếm URL theo từ khóa
      const urls = await this.urlCustomRepository.searchUrls(keyword, limit);
      return urls;
    } catch (error) {
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể tìm kiếm URL: ${error.message}`);
    }
  }

  async createUrl(employeeId: number, createUrlDto: CreateUrlAdminDto): Promise<any> {
    try {
      // Kiểm tra URL có hợp lệ không
      try {
        new URL(createUrlDto.url);
      } catch (error) {
        throw new UrlAdminException(null, 'URL không đúng định dạng');
      }

      // Kiểm tra URL đã tồn tại chưa trong toàn bộ hệ thống
      const existingUrl = await this.urlRepository.findOne?.({
        where: {
          url: createUrlDto.url
        }
      });

      if (existingUrl) {
        throw new UrlAdminException(null, 'URL này đã tồn tại trong hệ thống');
      }

      // Kiểm tra title và content không được để trống
      if (!createUrlDto.title || createUrlDto.title.trim() === '') {
        throw new UrlAdminException(null, 'Tiêu đề URL không được để trống');
      }

      if (!createUrlDto.content || createUrlDto.content.trim() === '') {
        throw new UrlAdminException(null, 'Nội dung URL không được để trống');
      }

      // Tạo URL mới với employeeId được chỉ định
      const newUrl = this.urlRepository.create?.({
        ...createUrlDto,
        ownedBy: employeeId,
        createdAt: Date.now(),
        updatedAt: Date.now()
      });

      // Lưu URL vào database
      return this.urlRepository.save?.(newUrl);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể tạo URL: ${error.message}`);
    }
  }

  async updateUrl(id: string, updateUrlDto: UpdateUrlAdminDto): Promise<any> {
    try {
      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Kiểm tra URL có hợp lệ không
      if (updateUrlDto.url) {
        try {
          new URL(updateUrlDto.url);
        } catch (error) {
          throw new UrlAdminException(null, 'URL không đúng định dạng');
        }

        // Kiểm tra URL mới đã tồn tại chưa trong toàn bộ hệ thống (nếu khác URL cũ)
        if (updateUrlDto.url !== url.url) {
          const existingUrl = await this.urlRepository.findOne?.({
            where: {
              url: updateUrlDto.url
            }
          });

          if (existingUrl) {
            throw new UrlAdminException(null, 'URL này đã tồn tại trong hệ thống');
          }
        }
      }

      // Kiểm tra title không được để trống
      if (updateUrlDto.title !== undefined && (updateUrlDto.title === null || updateUrlDto.title.trim() === '')) {
        throw new UrlAdminException(null, 'Tiêu đề URL không được để trống');
      }

      // Kiểm tra content không được để trống
      if (updateUrlDto.content !== undefined && (updateUrlDto.content === null || updateUrlDto.content.trim() === '')) {
        throw new UrlAdminException(null, 'Nội dung URL không được để trống');
      }

      // Cập nhật thông tin URL
      Object.assign(url, {
        ...updateUrlDto,
        updatedAt: Date.now()
      });

      // Lưu URL vào database
      return this.urlRepository.save?.(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể cập nhật URL: ${error.message}`);
    }
  }

  async deleteUrl(id: string): Promise<void> {
    try {
      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Xóa URL
      await this.urlRepository.remove?.(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể xóa URL: ${error.message}`);
    }
  }

  async updateUrlStatus(id: string, isActive: boolean): Promise<any> {
    try {
      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Cập nhật trạng thái kích hoạt
      url.isActive = isActive;
      url.updatedAt = Date.now();

      // Lưu URL vào database
      return this.urlRepository.save?.(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể cập nhật trạng thái URL: ${error.message}`);
    }
  }

  async toggleUrlStatus(id: string): Promise<any> {
    try {
      // Kiểm tra URL tồn tại
      const url = await this.findUrlById(id);

      // Đảo ngược trạng thái kích hoạt
      url.isActive = !url.isActive;
      url.updatedAt = Date.now();

      // Lưu URL vào database
      return this.urlRepository.save?.(url);
    } catch (error) {
      // Nếu lỗi đã được xử lý, ném lại
      if (error instanceof UrlAdminException) {
        throw error;
      }

      throw new UrlAdminException(null, `Không thể đảo ngược trạng thái URL: ${error.message}`);
    }
  }
}

describe('Kiểm thử Service URL của admin', () => {
  let service: UrlAdminServiceMock;
  let repository: any;
  let urlCustomRepository: UrlRepositoryAdmin;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  const mockUrls = [
    { ...mockUrl },
    {
      ...mockUrl,
      id: 'test-id-2',
      url: 'https://example.com/2',
      title: 'Test URL 2',
      isActive: false,
    },
  ];

  const mockPaginatedResult = {
    items: mockUrls,
    meta: {
      totalItems: 2,
      itemCount: 2,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
    },
  };

  beforeEach(() => {
    // Tạo mock repository
    repository = {
      create: jest.fn().mockImplementation((dto) => dto),
      save: jest.fn().mockImplementation((url) => Promise.resolve({ id: 'test-id', ...url })),
      findOne: jest.fn().mockImplementation((options) => {
        if (options?.where?.id === 'test-id' && options?.where?.ownedBy === 1) {
          return Promise.resolve(mockUrl);
        }
        // Kiểm tra URL đã tồn tại trong toàn bộ hệ thống
        if (options?.where?.url === 'https://example.com') {
          return Promise.resolve(mockUrl);
        }
        return Promise.resolve(null);
      }),
      remove: jest.fn().mockResolvedValue(mockUrl),
    };

    // Tạo mock custom repository
    urlCustomRepository = new UrlRepositoryAdmin();
    urlCustomRepository.findUrlById.mockImplementation((id) => {
      if (id === 'test-id') {
        return Promise.resolve(mockUrl);
      }
      if (id === 'another-user-id') {
        return Promise.resolve({ ...mockUrl, ownedBy: 2 });
      }
      return Promise.resolve(null);
    });
    urlCustomRepository.findUrlsByOwner.mockResolvedValue(mockPaginatedResult);
    urlCustomRepository.searchUrls.mockResolvedValue(mockUrls);

    // Tạo service với các repository đã mock
    service = new UrlAdminServiceMock(repository, urlCustomRepository);
  });

  it('Service phải được định nghĩa', () => {
    expect(service).toBeDefined();
  });

  describe('findUrlById - Lấy URL theo ID', () => {
    it('Phải trả về URL nếu nó tồn tại', async () => {
      const result = await service.findUrlById('test-id');
      expect(result).toEqual(mockUrl);
      expect(urlCustomRepository.findUrlById).toHaveBeenCalledWith('test-id');
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.findUrlById('non-existent-id')).rejects.toThrow('Không tìm thấy URL');
    });

    it('Phải ném ngoại lệ nếu ID không được cung cấp', async () => {
      await expect(service.findUrlById('')).rejects.toThrow('ID URL là bắt buộc');
    });
  });

  describe('findAllUrls - Lấy danh sách URL với phân trang', () => {
    it('Phải trả về danh sách URL có phân trang', async () => {
      const result = await service.findAllUrls({});
      expect(result).toEqual(mockPaginatedResult);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, expect.any(Object));
    });

    it('Phải truyền các tham số tìm kiếm đúng', async () => {
      const queryParams = {
        page: 1,
        limit: 10,
        sortBy: 'createdAt',
        sortDirection: SortDirectionEnumAdminService.DESC,
        keyword: 'test',
        type: 'web',
        tags: ['nestjs'],
        userId: 1,
        isActive: true
      };

      await service.findAllUrls(queryParams);
      expect(urlCustomRepository.findUrlsByOwner).toHaveBeenCalledWith(null, queryParams);
    });
  });

  describe('searchUrls - Tìm kiếm URL theo từ khóa', () => {
    it('Phải trả về danh sách URL phù hợp với từ khóa', async () => {
      const result = await service.searchUrls('test');
      expect(result).toEqual(mockUrls);
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 10);
    });

    it('Phải ném ngoại lệ nếu từ khóa trống', async () => {
      await expect(service.searchUrls('')).rejects.toThrow('Từ khóa tìm kiếm không được để trống');
    });

    it('Phải giới hạn số lượng kết quả trả về', async () => {
      await service.searchUrls('test', 5);
      expect(urlCustomRepository.searchUrls).toHaveBeenCalledWith('test', 5);
    });
  });

  describe('createUrl - Tạo URL mới', () => {
    it('Phải tạo được URL mới', async () => {
      const employeeId = 1;
      const createUrlDto: CreateUrlAdminDto = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: 'New content',
        type: 'web',
        tags: ['test'],
        isActive: true
      };

      // Mock findOne để trả về null (URL chưa tồn tại)
      repository.findOne.mockResolvedValueOnce(null);

      const result = await service.createUrl(employeeId, createUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.create).toHaveBeenCalledWith({
        ...createUrlDto,
        ownedBy: employeeId,
        createdAt: expect.any(Number),
        updatedAt: expect.any(Number),
      });
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu định dạng URL không hợp lệ', async () => {
      const employeeId = 1;
      const createUrlDto: CreateUrlAdminDto = {
        url: 'invalid-url',
        title: 'Invalid URL',
        content: 'Invalid content',
        type: 'web',
        tags: ['test']
      };

      await expect(service.createUrl(employeeId, createUrlDto)).rejects.toThrow('URL không đúng định dạng');
    });

    it('Phải ném ngoại lệ nếu URL đã tồn tại', async () => {
      const employeeId = 1;
      const createUrlDto: CreateUrlAdminDto = {
        url: 'https://example.com',
        title: 'Existing URL',
        content: 'Existing content',
        type: 'web',
        tags: ['test']
      };

      await expect(service.createUrl(employeeId, createUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề trống', async () => {
      const employeeId = 1;
      const createUrlDto: CreateUrlAdminDto = {
        url: 'https://newexample.com',
        title: '',
        content: 'New content',
        type: 'web',
        tags: ['test']
      };

      await expect(service.createUrl(employeeId, createUrlDto)).rejects.toThrow('Tiêu đề URL không được để trống');
    });

    it('Phải ném ngoại lệ nếu nội dung trống', async () => {
      const employeeId = 1;
      const createUrlDto: CreateUrlAdminDto = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: '',
        type: 'web',
        tags: ['test']
      };

      await expect(service.createUrl(employeeId, createUrlDto)).rejects.toThrow('Nội dung URL không được để trống');
    });
  });

  describe('updateUrl - Cập nhật URL', () => {
    it('Phải cập nhật được URL đã tồn tại', async () => {
      const updateUrlDto: UpdateUrlAdminDto = {
        title: 'Updated URL',
        content: 'Updated content',
        isActive: false
      };

      const result = await service.updateUrl('test-id', updateUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      const updateUrlDto: UpdateUrlAdminDto = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('non-existent-id', updateUrlDto)).rejects.toThrow('Không tìm thấy URL');
    });

    it('Phải ném ngoại lệ nếu định dạng URL cập nhật không hợp lệ', async () => {
      const updateUrlDto: UpdateUrlAdminDto = {
        url: 'invalid-url',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('URL không đúng định dạng');
    });

    it('Phải ném ngoại lệ nếu URL cập nhật đã tồn tại trong hệ thống', async () => {
      // Mock URL mới đã tồn tại trong hệ thống
      repository.findOne.mockImplementationOnce(() => Promise.resolve({ id: 'another-id', url: 'https://newurl.com' }));

      const updateUrlDto: UpdateUrlAdminDto = {
        url: 'https://newurl.com',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlAdminDto = {
        title: '',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('Tiêu đề URL không được để trống');
    });

    it('Phải ném ngoại lệ nếu nội dung cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlAdminDto = {
        title: 'Updated URL',
        content: '',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('Nội dung URL không được để trống');
    });
  });

  describe('deleteUrl - Xóa URL', () => {
    it('Phải xóa được URL đã tồn tại', async () => {
      await service.deleteUrl('test-id');
      expect(repository.remove).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.deleteUrl('non-existent-id')).rejects.toThrow('Không tìm thấy URL');
    });
  });

  describe('updateUrlStatus - Cập nhật trạng thái URL', () => {
    it('Phải cập nhật được trạng thái URL từ true sang false', async () => {
      const url = { ...mockUrl, isActive: true };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.updateUrlStatus('test-id', false);
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(false);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải cập nhật được trạng thái URL từ false sang true', async () => {
      const url = { ...mockUrl, isActive: false };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.updateUrlStatus('test-id', true);
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(true);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.updateUrlStatus('non-existent-id', true)).rejects.toThrow('Không tìm thấy URL');
    });
  });

  describe('toggleUrlStatus - Đảo ngược trạng thái URL', () => {
    it('Phải đảo ngược được trạng thái URL từ true sang false', async () => {
      const url = { ...mockUrl, isActive: true };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.toggleUrlStatus('test-id');
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(false);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải đảo ngược được trạng thái URL từ false sang true', async () => {
      const url = { ...mockUrl, isActive: false };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.toggleUrlStatus('test-id');
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(true);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.toggleUrlStatus('non-existent-id')).rejects.toThrow('Không tìm thấy URL');
    });
  });
});
