// <PERSON><PERSON><PERSON> ngh<PERSON>a c<PERSON>c interface thay vì class để tránh trùng lặp
interface CreateUrlAdminDtoType3 {
  url: string;
  title: string;
  content: string;
  type?: string;
  tags?: string[];
  isActive?: boolean;
}

interface UpdateUrlAdminDtoType3 {
  url?: string;
  title?: string;
  content?: string;
  type?: string;
  tags?: string[];
  ownedBy?: number;
  isActive?: boolean;
}

// Mock UrlAdminException class
class UrlAdminExceptionTest3 extends Error {
  constructor(errorCode: any, message?: string) {
    super(message || errorCode?.message || 'URL admin exception');
    this.name = 'UrlAdminExceptionTest3';
  }
}

// Mock UrlRepository class
class UrlRepositoryTest3 {
  findUrlById = jest.fn();
  findUrlsByOwner = jest.fn();
  searchUrls = jest.fn();
}

// Mock UrlAdminService class
class UrlAdminServiceTest3 {
  constructor(
    private readonly urlRepository: any,
    private readonly urlCustomRepository: UrlRepositoryTest3
  ) {}

  async findUrlById(id: string): Promise<any> {
    const url = await this.urlCustomRepository.findUrlById(id);
    if (!url) {
      throw new Error('URL không tồn tại');
    }
    return url;
  }

  async createUrl(employeeId: number, createUrlDto: CreateUrlAdminDtoType3): Promise<any> {
    try {
      new URL(createUrlDto.url);
    } catch (error) {
      throw new Error('URL không đúng định dạng');
    }

    if (!createUrlDto.title || createUrlDto.title.trim() === '') {
      throw new Error('Tiêu đề URL không được để trống');
    }

    if (!createUrlDto.content || createUrlDto.content.trim() === '') {
      throw new Error('Nội dung URL không được để trống');
    }

    const existingUrl = await this.urlRepository.findOne?.({
      where: {
        url: createUrlDto.url
      }
    });

    if (existingUrl) {
      throw new Error('URL này đã tồn tại trong hệ thống');
    }

    const newUrl = this.urlRepository.create?.({
      ...createUrlDto,
      ownedBy: userId,
      createdAt: Date.now(),
      updatedAt: Date.now()
    });

    return this.urlRepository.save?.(newUrl);
  }

  async updateUrl(id: string, updateUrlDto: UpdateUrlAdminDtoType3): Promise<any> {
    const url = await this.findUrlById(id);

    if (updateUrlDto.url) {
      try {
        new URL(updateUrlDto.url);
      } catch (error) {
        throw new Error('URL không đúng định dạng');
      }

      // Kiểm tra URL mới đã tồn tại chưa trong toàn bộ hệ thống (nếu khác URL cũ)
      if (updateUrlDto.url !== url.url) {
        const existingUrl = await this.urlRepository.findOne?.({
          where: {
            url: updateUrlDto.url
          }
        });

        if (existingUrl) {
          throw new Error('URL này đã tồn tại trong hệ thống');
        }
      }
    }

    if (updateUrlDto.title !== undefined && (updateUrlDto.title === null || updateUrlDto.title.trim() === '')) {
      throw new Error('Tiêu đề URL không được để trống');
    }

    if (updateUrlDto.content !== undefined && (updateUrlDto.content === null || updateUrlDto.content.trim() === '')) {
      throw new Error('Nội dung URL không được để trống');
    }

    Object.assign(url, {
      ...updateUrlDto,
      updatedAt: Date.now()
    });

    return this.urlRepository.save?.(url);
  }

  async deleteUrl(id: string): Promise<void> {
    const url = await this.findUrlById(id);
    await this.urlRepository.remove?.(url);
  }

  async updateUrlStatus(id: string, isActive: boolean): Promise<any> {
    const url = await this.findUrlById(id);
    url.isActive = isActive;
    url.updatedAt = Date.now();
    return this.urlRepository.save?.(url);
  }

  async toggleUrlStatus(id: string): Promise<any> {
    const url = await this.findUrlById(id);
    url.isActive = !url.isActive;
    url.updatedAt = Date.now();
    return this.urlRepository.save?.(url);
  }
}

describe('Kiểm thử CRUD URL của admin', () => {
  let service: UrlAdminServiceTest3;
  let urlCustomRepository: UrlRepositoryTest3;
  let repository: any;

  const mockUrl = {
    id: 'test-id',
    url: 'https://example.com',
    title: 'Test URL',
    content: 'Test content',
    type: 'web',
    tags: ['test'],
    ownedBy: 1,
    createdAt: Date.now(),
    updatedAt: Date.now(),
    urlEmbedding: null,
    titleEmbedding: null,
    contentEmbedding: null,
    isActive: true,
  };

  beforeEach(() => {
    // Tạo repository và service
    repository = {
      create: jest.fn().mockImplementation((dto) => dto),
      save: jest.fn().mockImplementation((url) => Promise.resolve({ id: 'test-id', ...url })),
      findOne: jest.fn().mockImplementation((options) => {
        // Kiểm tra URL đã tồn tại trong toàn bộ hệ thống
        if (options?.where?.url === 'https://example.com') {
          return Promise.resolve(mockUrl);
        }
        return Promise.resolve(null);
      }),
      remove: jest.fn().mockResolvedValue(mockUrl),
    };

    urlCustomRepository = new UrlRepositoryTest3();
    urlCustomRepository.findUrlById.mockImplementation((id) => {
      if (id === 'test-id') {
        return Promise.resolve(mockUrl);
      }
      return Promise.resolve(null);
    });

    service = new UrlAdminServiceTest3(repository, urlCustomRepository);
  });

  describe('findUrlById - Lấy URL theo ID', () => {
    it('Phải trả về URL nếu nó tồn tại', async () => {
      const result = await service.findUrlById('test-id');
      expect(result).toEqual(mockUrl);
      expect(urlCustomRepository.findUrlById).toHaveBeenCalledWith('test-id');
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.findUrlById('non-existent-id')).rejects.toThrow('URL không tồn tại');
    });
  });

  describe('createUrl - Tạo URL mới', () => {
    it('Phải tạo được URL mới', async () => {
      const userId = 1;
      const createUrlDto: CreateUrlAdminDtoType3 = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: 'New content',
        type: 'web',
        tags: ['test'],
        isActive: true
      };

      // Mock findOne để trả về null (URL chưa tồn tại)
      repository.findOne.mockResolvedValueOnce(null);

      const result = await service.createUrl(userId, createUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.create).toHaveBeenCalledWith({
        ...createUrlDto,
        ownedBy: userId,
        createdAt: expect.any(Number),
        updatedAt: expect.any(Number),
      });
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu định dạng URL không hợp lệ', async () => {
      const createUrlDto: CreateUrlAdminDtoType3 = {
        url: 'invalid-url',
        title: 'Invalid URL',
        content: 'Invalid content',
        type: 'web',
        tags: ['test'],
        ownedBy: 1
      };

      await expect(service.createUrl(createUrlDto)).rejects.toThrow('URL không đúng định dạng');
    });

    it('Phải ném ngoại lệ nếu URL đã tồn tại', async () => {
      const createUrlDto: CreateUrlAdminDtoType3 = {
        url: 'https://example.com',
        title: 'Existing URL',
        content: 'Existing content',
        type: 'web',
        tags: ['test'],
        ownedBy: 1
      };

      await expect(service.createUrl(createUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề trống', async () => {
      const createUrlDto: CreateUrlAdminDtoType3 = {
        url: 'https://newexample.com',
        title: '',
        content: 'New content',
        type: 'web',
        tags: ['test'],
        ownedBy: 1
      };

      await expect(service.createUrl(createUrlDto)).rejects.toThrow('Tiêu đề URL không được để trống');
    });

    it('Phải ném ngoại lệ nếu nội dung trống', async () => {
      const createUrlDto: CreateUrlAdminDtoType3 = {
        url: 'https://newexample.com',
        title: 'New URL',
        content: '',
        type: 'web',
        tags: ['test'],
        ownedBy: 1
      };

      await expect(service.createUrl(createUrlDto)).rejects.toThrow('Nội dung URL không được để trống');
    });
  });

  describe('updateUrl - Cập nhật URL', () => {
    it('Phải cập nhật được URL đã tồn tại', async () => {
      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        title: 'Updated URL',
        content: 'Updated content',
        isActive: false
      };

      const result = await service.updateUrl('test-id', updateUrlDto);
      expect(result).toHaveProperty('id');
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('non-existent-id', updateUrlDto)).rejects.toThrow('URL không tồn tại');
    });

    it('Phải ném ngoại lệ nếu định dạng URL cập nhật không hợp lệ', async () => {
      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        url: 'invalid-url',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('URL không đúng định dạng');
    });

    it('Phải ném ngoại lệ nếu URL cập nhật đã tồn tại trong hệ thống', async () => {
      // Mock URL mới đã tồn tại trong hệ thống
      repository.findOne.mockImplementationOnce(() => Promise.resolve({ id: 'another-id', url: 'https://newurl.com' }));

      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        url: 'https://newurl.com',
        title: 'Updated URL',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('URL này đã tồn tại trong hệ thống');
    });

    it('Phải ném ngoại lệ nếu tiêu đề cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        title: '',
        content: 'Updated content',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('Tiêu đề URL không được để trống');
    });

    it('Phải ném ngoại lệ nếu nội dung cập nhật trống', async () => {
      const updateUrlDto: UpdateUrlAdminDtoType3 = {
        title: 'Updated URL',
        content: '',
      };

      await expect(service.updateUrl('test-id', updateUrlDto)).rejects.toThrow('Nội dung URL không được để trống');
    });
  });

  describe('deleteUrl - Xóa URL', () => {
    it('Phải xóa được URL đã tồn tại', async () => {
      await service.deleteUrl('test-id');
      expect(repository.remove).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.deleteUrl('non-existent-id')).rejects.toThrow('URL không tồn tại');
    });
  });

  describe('updateUrlStatus - Cập nhật trạng thái URL', () => {
    it('Phải cập nhật được trạng thái URL từ true sang false', async () => {
      const url = { ...mockUrl, isActive: true };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.updateUrlStatus('test-id', false);
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(false);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải cập nhật được trạng thái URL từ false sang true', async () => {
      const url = { ...mockUrl, isActive: false };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.updateUrlStatus('test-id', true);
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(true);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.updateUrlStatus('non-existent-id', true)).rejects.toThrow('URL không tồn tại');
    });
  });

  describe('toggleUrlStatus - Đảo ngược trạng thái URL', () => {
    it('Phải đảo ngược được trạng thái URL từ true sang false', async () => {
      const url = { ...mockUrl, isActive: true };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.toggleUrlStatus('test-id');
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(false);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải đảo ngược được trạng thái URL từ false sang true', async () => {
      const url = { ...mockUrl, isActive: false };
      urlCustomRepository.findUrlById.mockResolvedValueOnce(url);
      const result = await service.toggleUrlStatus('test-id');
      expect(result).toHaveProperty('id');
      expect(result.isActive).toBe(true);
      expect(repository.save).toHaveBeenCalled();
    });

    it('Phải ném ngoại lệ nếu URL không tồn tại', async () => {
      urlCustomRepository.findUrlById.mockResolvedValueOnce(null);
      await expect(service.toggleUrlStatus('non-existent-id')).rejects.toThrow('URL không tồn tại');
    });
  });
});
