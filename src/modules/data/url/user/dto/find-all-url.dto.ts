import { QueryDto } from "@common/dto";
import { ApiProperty } from "@nestjs/swagger";
import { IsOptional, IsString, IsArray, IsEnum } from 'class-validator';
import { Transform, Type } from 'class-transformer';
import { OwnerType } from '../../constants/owner-type.enum';

export class FindAllUrlDto extends QueryDto {
  @ApiProperty({
    description: 'Từ khóa tìm kiếm URL, tìm kiếm theo title, content và url',
    example: 'google',
    required: false,
  })
  @IsOptional()
  @IsString()
  keyword?: string;

  @ApiProperty({
    description: 'Loại URL cần lọc',
    example: 'web',
    required: false,
  })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({
    description: 'Các thẻ cần lọc',
    example: ['nestjs', 'tutorial'],
    required: false,
    type: [String],
  })
  @IsOptional()
  @IsArray()
  @Type(() => String)
  @Transform(({ value }) => {
    // Xử lý trường hợp tags=tag1,tag2,tag3
    if (typeof value === 'string' && value.includes(',')) {
      return value.split(',').map(tag => tag.trim());
    }
    // Xử lý trường hợp tags là một string
    if (typeof value === 'string') {
      return [value];
    }
    // Trường hợp tags đã là mảng
    return value;
  })
  tags?: string[];

  @ApiProperty({
    description: 'Loại người sở hữu URL (ADMIN hoặc USER)',
    example: OwnerType.USER,
    enum: OwnerType,
    required: false,
  })
  @IsOptional()
  @IsEnum(OwnerType)
  ownedByEnum?: OwnerType;
}
