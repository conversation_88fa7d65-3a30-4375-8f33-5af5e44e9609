import { AppException } from '@/common';
import { ApiResponseDto, PaginatedResult } from '@/common/response';
import { MediaQueryDto } from '@/modules/data/media/dto/media-query.dto';
import { CdnService } from '@/shared/services/cdn.service';
import { S3Service } from '@/shared/services/s3.service';
import {
  AudioType,
  CategoryFolderEnum,
  FileSizeEnum,
  generateS3Key, ImageType,
  MediaType,
  MediaTypeUtil,
  TimeIntervalEnum, VideoType,
} from '@/shared/utils';
import { SortDirection } from '@common/dto';
import { AgentMediaRepository } from '@modules/agent/repositories/agent-media.repository';
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
import { MediaTypeEnum } from '@modules/data/media/enums/media-type.enum';
import { MEDIA_ERROR_CODES } from '@modules/data/media/exception';
import { Injectable, Logger } from '@nestjs/common';
import { DeepPartial } from 'typeorm';
import { Transactional } from 'typeorm-transactional';
import { MediaUploadUrlDto, PresignedUrlResponseDto } from '../../dto/media-upload-url.dto';
import { MediaCreateResponseDto, MediaDto } from '../../dto/media-user.dto';
import { Media } from '../../entities/media.entity';
import { OwnerTypeEnum } from '../../enums/owner-type.enum';
import { MediaValidationHelper } from '../../helpers/validation.helper';
import { MediaUserMapper } from '../../mappers/media-user.mapper';
import { MediaRepository } from '../../repositories';

/**
 * Interface định nghĩa cấu trúc trả về của phương thức resolveMediaConfig
 */
interface MediaConfig {
  mimeType: MediaType;
  expirationTime: TimeIntervalEnum;
  maxSize: FileSizeEnum;
  CategoryFolderEnum: CategoryFolderEnum;
}

@Injectable()
export class MediaUserService {
  private logger = new Logger(MediaUserService.name);

  constructor(
    private readonly mediaRepository: MediaRepository,
    private readonly s3Service: S3Service,
    private readonly cdnService: CdnService,
    private readonly agentMediaRepository: AgentMediaRepository,
    private readonly mediaValidationHelper: MediaValidationHelper,
  ) { }

  async findById(id: string, userId: number): Promise<MediaDto> {
    const media = await this.mediaRepository.findOneBy({ id });

    if (!media) {
      throw new AppException(
        MEDIA_ERROR_CODES.NOT_FOUND,
        `Media với id ${id} không tồn tại.`,
      );
    }

    if (media.ownedBy !== userId) {
      throw new AppException(
        MEDIA_ERROR_CODES.FORBIDDEN,
        'Bạn không có quyền xem media này.',
      );
    }

    return await MediaUserMapper.toUserDto(media, this.cdnService);
  }

  /**
   * Xóa mềm nhiều media theo danh sách ID (cập nhật trạng thái thành DELETED)
   * @param userId ID của người dùng đang thực hiện xóa
   * @param mediaIds Danh sách ID của media cần xóa
   * @returns Kết quả xóa mềm
   */
  @Transactional()
  async deleteManyByUser(
    userId: number,
    mediaIds: string[],
  ): Promise<ApiResponseDto<null>> {
    // Kiểm tra danh sách ID không được rỗng
    this.mediaValidationHelper.validateMediaLength(
      mediaIds.length,
      'Danh sách ID media không được rỗng.'
    );

    const deletedIds: string[] = [];
    const skippedIds: string[] = [];
    const failedIds: { id: string; reason: string }[] = [];

    // Xử lý từng media ID
    for (const mediaId of mediaIds) {
      try {
        // Tìm kiếm media theo ID
        const media = await this.mediaRepository.findOne({
          where: { id: mediaId },
        });

        if (!media) {
          skippedIds.push(mediaId);
          continue; // Nếu media không tồn tại trong DB, bỏ qua
        }

        // Kiểm tra quyền sở hữu
        if (!this.mediaValidationHelper.validateUserPermission(userId, media.ownedBy)) {
          skippedIds.push(mediaId);
          continue; // Nếu không phải chủ sở hữu, bỏ qua
        }

        // Cập nhật trạng thái thành DELETED thay vì xóa hoàn toàn
        await this.mediaRepository.update(
          { id: mediaId },
          {
            status: MediaStatusEnum.DELETED,
            updatedAt: Date.now()
          }
        );

        deletedIds.push(mediaId);
      } catch (error) {
        this.logger.error(`Lỗi khi xóa mềm media ${mediaId}: ${error.message}`, error.stack);
        failedIds.push({ id: mediaId, reason: error.message });
      }
    }

    this.logger.log(`Xóa mềm media: đã xóa ${deletedIds.length}, bỏ qua ${skippedIds.length}, lỗi ${failedIds.length}`);

    return ApiResponseDto.success(null, 'Media đã được xóa mềm thành công.');
  }

  /**
   * Lấy danh sách media thuộc về người dùng cụ thể với phân trang, tìm kiếm và sắp xếp.
   *
   * @param userId - ID của người dùng đang đăng nhập
   * @param query - Tham số query gồm phân trang, tìm kiếm, sắp xếp
   * @returns ApiResponseDto chứa danh sách media phân trang
   */
  async findAllByUser(
    userId: number,
    query: MediaQueryDto,
  ): Promise<PaginatedResult<MediaDto>> {
    // Thiết lập các tham số mặc định và ghi đè nếu có truyền vào từ query
    const options: MediaQueryDto = {
      page: query.page || 1, // Trang hiện tại (mặc định 1)
      limit: query.limit || 10, // Số bản ghi mỗi trang (mặc định 10)
      search: query.search, // Từ khóa tìm kiếm nếu có
      sortBy: query.sortBy || 'createdAt', // Trường sắp xếp (mặc định 'createdAt')
      sortDirection: query.sortDirection || SortDirection.DESC, // Hướng sắp xếp (mặc định DESC)
    };

    // Gọi repository để lấy danh sách media của người dùng theo điều kiện đã thiết lập
    const result = await this.mediaRepository.findAllUserMedia(userId, options);

    // Trả về kết quả dạng ApiResponseDto có phân trang
    return {
      items: await MediaUserMapper.toUserList(result.items, this.cdnService),
      meta: result.meta,
    }
  }

  async createPresignedUrlsFromMediaList(
    mediaList: MediaDto[],
    userId: number,
  ): Promise<string[]> {
    const presignedUrls: string[] = [];
    const mediaEntities: DeepPartial<Media>[] = [];

    for (const media of mediaList) {
      // Validate MIME type - name là tên mô tả, không phải file name
      this.mediaValidationHelper.validateAllowedMimeType(media.type);

      const { mimeType, expirationTime, maxSize, CategoryFolderEnum } =
        this.resolveMediaConfig(media.type);

      // Tạo file name từ name và type
      const extension = this.getFileExtensionFromMimeType(media.type);
      const fileName = `${media.name.replace(/[^a-zA-Z0-9]/g, '_')}.${extension}`;

      const storageKey = generateS3Key({
        baseFolder: 'media',
        categoryFolder: CategoryFolderEnum,
        fileName: fileName,
        prefix: `user_${userId}`,
        useTimeFolder: true,
      });

      // Kiểm tra kích thước file
      this.mediaValidationHelper.validateMediaSize(
        media.size,
        maxSize,
      );


      const presignedUrl = await this.s3Service.createPresignedWithID(
        storageKey,
        expirationTime,
        mimeType,
        maxSize,
      );

      presignedUrls.push(presignedUrl);

      const mediaEntity: DeepPartial<Media> = {
        name: media.name,
        description: media.description, // Có thể null/undefined, entity đã hỗ trợ nullable
        size: media.size,
        tags: media.tags,
        ownedBy: userId,
        ownerType: OwnerTypeEnum.USER,
        status: MediaStatusEnum.PENDING,
        storageKey,
        mediaType: this.getMediaTypeFromMimeType(media.type), // Xác định loại media từ MIME type
        createdAt: Date.now(),
        updatedAt: Date.now(),
      };

      mediaEntities.push(mediaEntity);
    }

    await this.mediaRepository.save(mediaEntities);

    return (presignedUrls);
  }

  /**
   * Tạo URL tạm thời để upload tài nguyên media
   * @param userId ID của người dùng đang đăng nhập
   * @param dto Thông tin về loại media và kích thước file
   * @returns Thông tin URL tạm thời và key
   */
  async createMediaUploadUrl(
    userId: number,
    dto: MediaUploadUrlDto,
  ): Promise<PresignedUrlResponseDto> {
    try {
      // Xác định loại media và thư mục phù hợp
      let mediaType: MediaType;
      let categoryFolder: CategoryFolderEnum;

      // Validate MIME type và file name nếu có
      this.mediaValidationHelper.validateAllowedMimeType(dto.mediaType);
      if (dto.fileName) {
        this.mediaValidationHelper.validateAllowedFileExtension(dto.fileName);
      }

      // Xác định loại media từ MIME type - chỉ hỗ trợ image, video và audio
      if (dto.mediaType.startsWith('image/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.IMAGE;
      } else if (dto.mediaType.startsWith('video/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.VIDEO;
      } else if (dto.mediaType.startsWith('audio/')) {
        mediaType = dto.mediaType as MediaType;
        categoryFolder = CategoryFolderEnum.AUDIO;
      } else {
        // Không bao giờ đến đây vì đã kiểm tra ở trên
        throw new AppException(
          MEDIA_ERROR_CODES.FILE_TYPE_NOT_FOUND,
          `Loại media không được hỗ trợ: ${dto.mediaType}`,
        );
      }

      // Tạo tên file nếu không được cung cấp
      const fileName = dto.fileName || `file-${Date.now()}`;

      // Tạo S3 key cho file
      const key = generateS3Key({
        baseFolder: 'media',
        categoryFolder: categoryFolder,
        fileName: fileName,
        prefix: `user_${userId}`,
        useTimeFolder: true,
      });

      // Tạo presigned URL
      const expirationTime = TimeIntervalEnum.FIFTEEN_MINUTES;
      const uploadUrl = await this.s3Service.createPresignedWithID(
        key,
        expirationTime,
        mediaType,
        dto.fileSize,
      );

      // Tính thời gian hết hạn
      const expiresAt = Date.now() + expirationTime * 1000;

      return {
        uploadUrl,
        key,
        expiresAt,
      };
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
        `Không thể tạo URL upload: ${error.message}`,
      );
    }
  }

  /**
   * Tạo URL tạm thời để upload tài nguyên media
   * @param userId ID của người dùng đang đăng nhập
   * @param dto Thông tin về loại media và kích thước file
   * @returns Thông tin URL tạm thời và key
   */
  async createImageUploadUrl(
    userId: number,
    dto: MediaUploadUrlDto[],
  ): Promise<MediaCreateResponseDto[]> {
    try {
      // Validate input array
      if (!dto || dto.length === 0) {
        throw new AppException(
          MEDIA_ERROR_CODES.BAD_REQUEST,
          'Danh sách media upload không được để trống',
        );
      }

      const results: MediaCreateResponseDto[] = [];

      // Sử dụng Promise.all để xử lý tất cả async operations
      await Promise.all(dto.map(async (mediaUploadDto) => {
        try {
          // Validate MIME type và file name nếu có
          this.mediaValidationHelper.validateAllowedMimeType(mediaUploadDto.mediaType);
          if (mediaUploadDto.fileName) {
            this.mediaValidationHelper.validateAllowedFileExtension(mediaUploadDto.fileName);
          }

          const keyS3 = generateS3Key({
            baseFolder: userId.toString(),
            categoryFolder: CategoryFolderEnum.IMAGE,
            fileName: mediaUploadDto.fileName,
            useTimeFolder: true,
          });

          // Tạo URL upload (không sử dụng trong response nhưng cần để validate)
          const uploadUrl = await this.s3Service.createPresignedWithID(
            keyS3,
            TimeIntervalEnum.FIFTEEN_MINUTES,
            ImageType.getType(mediaUploadDto.mediaType),
            mediaUploadDto.fileSize,
          );

          const mediaEntity: DeepPartial<Media> = {
            name: mediaUploadDto.fileName,
            description: mediaUploadDto.description,
            size: mediaUploadDto.fileSize,
            tags: mediaUploadDto.tags,
            ownedBy: userId,
            ownerType: OwnerTypeEnum.USER,
            status: MediaStatusEnum.PENDING,
            storageKey: keyS3,
            mediaType: this.getMediaTypeFromMimeType(mediaUploadDto.mediaType), // Xác định loại media từ MIME type
            createdAt: Date.now(),
            updatedAt: Date.now(),
          };

          const savedMedia = await this.mediaRepository.save(mediaEntity);

          // Thêm vào kết quả
          results.push({
            id: savedMedia.id,
            name: mediaUploadDto.fileName || savedMedia.name,
            tags: mediaUploadDto.tags || [],
            key: keyS3,
            uploadUrl,
          });
        } catch (error) {
          this.logger.error(`Lỗi khi validate media upload: ${error.message}`, error.stack);
          throw error;
        }
      }));

      this.logger.log(`Tạo thành công ${results.length} URL upload cho user ${userId}`);
      return results;
    } catch (error) {
      this.logger.error(`Lỗi khi tạo URL upload: ${error.message}`, error.stack);
      if (error instanceof AppException) {
        throw error;
      }
      throw new AppException(
        MEDIA_ERROR_CODES.DATA_FETCH_ERROR,
        `Không thể tạo URL upload: ${error.message}`,
      );
    }
  }

  /**
   * Xác định cấu hình media dựa trên loại media
   * Chỉ hỗ trợ Image, Video và Audio - không hỗ trợ Document files
   * @param type Loại media
   * @returns Cấu hình media bao gồm mimeType, expirationTime, maxSize, và CategoryFolderEnum
   * @throws AppException nếu loại media không được hỗ trợ
   */
  private resolveMediaConfig(type: string): MediaConfig {
    // Thử xử lý như hình ảnh
    try {
      const typeImage: MediaType = ImageType.getType(type);
      return {
        mimeType: typeImage,
        expirationTime: TimeIntervalEnum.FIVE_MINUTES,
        maxSize: FileSizeEnum.FIVE_MB,
        CategoryFolderEnum: CategoryFolderEnum.IMAGE,
      };
    } catch (error) {
      // Thử xử lý như video
      try {
        const typeVideo: MediaType = VideoType.getType(type);
        return {
          mimeType: typeVideo,
          expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
          maxSize: FileSizeEnum.FIFTY_MB,
          CategoryFolderEnum: CategoryFolderEnum.VIDEO,
        };
      } catch (error) {
        // Thử xử lý như audio
        try {
          const typeAudio: MediaType = AudioType.getType(type);
          return {
            mimeType: typeAudio,
            expirationTime: TimeIntervalEnum.FIFTEEN_MINUTES,
            maxSize: FileSizeEnum.TWENTY_MB,
            CategoryFolderEnum: CategoryFolderEnum.AUDIO,
          };
        } catch (innerError) {
          // Nếu tất cả các trường hợp đều thất bại, ném lỗi
          throw new AppException(
            MEDIA_ERROR_CODES.FILE_TYPE_NOT_FOUND,
            `Loại media không được hỗ trợ. Chỉ hỗ trợ hình ảnh, video và audio: ${type}`
          );
        }
      }
    }
  }

  /**
   * Xác định MediaTypeEnum từ MIME type sử dụng shared utils
   * @param mimeType MIME type string (ví dụ: 'image/jpeg', 'video/mp4')
   * @returns MediaTypeEnum tương ứng
   * @throws Error if MIME type is not supported in media module
   */
  private getMediaTypeFromMimeType(mimeType: string): MediaTypeEnum {
    // Sử dụng MediaTypeUtil để kiểm tra loại media được phép
    if (!MediaTypeUtil.isAllowedInMediaModule(mimeType)) {
      throw new Error(`MIME type ${mimeType} is not allowed in media module`);
    }

    if (mimeType.startsWith('image/')) {
      return MediaTypeEnum.IMAGE;
    } else if (mimeType.startsWith('video/')) {
      return MediaTypeEnum.VIDEO;
    } else if (mimeType.startsWith('audio/')) {
      return MediaTypeEnum.AUDIO;
    }

    // Không bao giờ đến đây vì MediaTypeUtil.isAllowedInMediaModule đã kiểm tra
    throw new Error(`Unsupported MIME type: ${mimeType}`);
  }

  /**
   * Lấy file extension từ MIME type
   * @param mimeType MIME type
   * @returns File extension
   */
  private getFileExtensionFromMimeType(mimeType: string): string {
    const mimeToExtension: Record<string, string> = {
      // Image types
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/gif': 'gif',
      'image/webp': 'webp',

      // Video types
      'video/mp4': 'mp4',
      'video/webm': 'webm',
      'video/quicktime': 'mov',
      'video/x-msvideo': 'avi',
      'video/x-matroska': 'mkv',
      'video/x-flv': 'flv',
      'video/x-ms-wmv': 'wmv',
      'video/mpeg': 'mpg',
      'video/3gpp': '3gp',

      // Audio types
      'audio/mpeg': 'mp3',
      'audio/wav': 'wav',
      'audio/ogg': 'ogg',
      'audio/aac': 'aac',
      'audio/flac': 'flac',
      'audio/mp4': 'm4a',
      'audio/x-ms-wma': 'wma',
      'audio/webm': 'webm',
    };

    return mimeToExtension[mimeType] || 'bin';
  }

  /**
   * Confirm upload thành công và cập nhật status thành APPROVED
   * @param userId ID của người dùng
   * @param storageKeys Danh sách storage keys đã upload thành công
   * @returns Kết quả cập nhật
   */
  @Transactional()
  async confirmUploadSuccess(
    userId: number,
    storageKeys: string[],
  ): Promise<{ updatedIds: string[]; skippedKeys: string[] }> {
    const updatedIds: string[] = [];
    const skippedKeys: string[] = [];

    for (const storageKey of storageKeys) {
      try {
        // Tìm media theo storageKey và userId
        const media = await this.mediaRepository.findOne({
          where: {
            storageKey: storageKey,
            ownedBy: userId,
            status: MediaStatusEnum.PENDING
          },
        });

        if (!media) {
          skippedKeys.push(storageKey);
          continue;
        }

        // Cập nhật status thành APPROVED
        await this.mediaRepository.update(
          { id: media.id },
          {
            status: MediaStatusEnum.APPROVED,
            updatedAt: Date.now()
          }
        );

        updatedIds.push(media.id);
      } catch (error) {
        this.logger.error(`Lỗi khi confirm upload cho ${storageKey}: ${error.message}`, error.stack);
        skippedKeys.push(storageKey);
      }
    }

    return { updatedIds, skippedKeys };
  }
}
