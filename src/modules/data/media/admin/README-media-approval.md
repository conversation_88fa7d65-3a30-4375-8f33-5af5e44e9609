# Media Approval APIs

## Tổng quan

Các <PERSON> này cho phép admin phê duy<PERSON>t, từ chối hoặc thay đổi trạng thái của media trong hệ thống. Hỗ trợ cả cập nhật đơn lẻ và bulk update.

## Endpoints

### 1. Cậ<PERSON> nhật trạng thái một media

**Endpoint:** `PATCH /admin/media/:id/status`

**Mô tả:** Cập nhật trạng thái của một media cụ thể

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

**Path Parameters:**
- `id` (string, required): UUID của media cần cập nhật

**Request Body:**
```json
{
  "status": "APPROVED" | "REJECTED" | "PENDING",
  "note": "<PERSON>hi chú về việc thay đổi trạng thái (tù<PERSON> chọn)"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Cập nhật trạng thái media thành công",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "status": "APPROVED",
    "updatedAt": 1625097600000
  }
}
```

**Response Error (404):**
```json
{
  "success": false,
  "message": "Media với id 550e8400-e29b-41d4-a716-************ không tồn tại",
  "errorCode": 10300
}
```

### 2. Cập nhật trạng thái nhiều media

**Endpoint:** `PATCH /admin/media/status`

**Mô tả:** Cập nhật trạng thái của nhiều media cùng lúc

**Headers:**
- `Authorization: Bearer <JWT_TOKEN>`
- `Content-Type: application/json`

**Request Body:**
```json
{
  "mediaIds": [
    "550e8400-e29b-41d4-a716-************",
    "550e8400-e29b-41d4-a716-446655440001"
  ],
  "status": "APPROVED" | "REJECTED" | "PENDING",
  "note": "Ghi chú về việc thay đổi trạng thái (tùy chọn)"
}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Cập nhật trạng thái nhiều media thành công",
  "data": {
    "updatedIds": ["550e8400-e29b-41d4-a716-************"],
    "skippedIds": ["550e8400-e29b-41d4-a716-446655440001"],
    "failedIds": [
      {
        "id": "550e8400-e29b-41d4-a716-446655440002",
        "reason": "Media không tồn tại"
      }
    ]
  }
}
```

## Quy tắc chuyển đổi trạng thái

### Trạng thái được phép:
- `PENDING`: Đang chờ duyệt
- `APPROVED`: Đã được phê duyệt
- `REJECTED`: Bị từ chối

### Quy tắc chuyển đổi:
1. **Không được chuyển từ `DELETED`**: Media đã bị xóa không thể thay đổi trạng thái
2. **Không được chuyển sang `DELETED`**: Sử dụng API xóa media thay vì API này
3. **Không được chuyển sang `DRAFT`**: Trạng thái DRAFT chỉ dành cho media mới tạo
4. **Có thể chuyển tự do giữa**: `PENDING` ↔ `APPROVED` ↔ `REJECTED`

## Validation

### Request Body Validation:
- `status`: Bắt buộc, chỉ chấp nhận `APPROVED`, `REJECTED`, hoặc `PENDING`
- `note`: Tùy chọn, phải là string nếu có
- `mediaIds` (bulk): Bắt buộc, mảng không rỗng, mỗi phần tử phải là UUID hợp lệ

### Authorization:
- Yêu cầu JWT token hợp lệ
- Chỉ admin mới có quyền thực hiện (`isAdmin: true`)

## Error Codes

| Code | Message | HTTP Status | Mô tả |
|------|---------|-------------|-------|
| 10300 | Media not found | 404 | Media không tồn tại |
| 10310 | Failed to send request | 400 | Request không hợp lệ |
| 10312 | General media error | 500 | Lỗi hệ thống |

## Ví dụ sử dụng

### Phê duyệt một media:
```bash
curl -X PATCH "https://api.example.com/admin/media/550e8400-e29b-41d4-a716-************/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "status": "APPROVED",
    "note": "Media đã được kiểm tra và phê duyệt"
  }'
```

### Từ chối nhiều media:
```bash
curl -X PATCH "https://api.example.com/admin/media/status" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "mediaIds": [
      "550e8400-e29b-41d4-a716-************",
      "550e8400-e29b-41d4-a716-446655440001"
    ],
    "status": "REJECTED",
    "note": "Media không phù hợp với tiêu chuẩn"
  }'
```

## Lưu ý

1. **Performance**: Bulk update được xử lý theo batch (1000 items/batch) để tránh timeout
2. **Transaction**: Mỗi operation được wrap trong transaction để đảm bảo consistency
3. **Logging**: Tất cả thao tác đều được log để audit
4. **Idempotent**: Có thể gọi lại API với cùng trạng thái mà không gây lỗi
