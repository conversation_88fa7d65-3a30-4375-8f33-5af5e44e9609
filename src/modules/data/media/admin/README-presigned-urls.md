# Admin Media Presigned URLs API

## <PERSON>ô tả
API này cho phép admin tạo presigned URLs để upload media files lên S3. Tương tự như API user nhưng dành riêng cho admin với quyền cao hơn.

## Endpoint
```
POST /admin/media/presigned-urls
```

## Authentication
- <PERSON><PERSON><PERSON> c<PERSON>u JWT token của employee với role admin
- Header: `Authorization: Bearer <admin_jwt_token>`

## Request Body
Array của MediaDto objects:

```json
[
  {
    "name": "tên file mô tả",
    "description": "mô tả chi tiết về media",
    "size": 1048576,
    "tags": ["tag1", "tag2"],
    "type": "image/jpeg",
    "viewUrl": ""
  }
]
```

### Các trường bắt buộc:
- `name`: Tên mô tả của media (string)
- `description`: <PERSON>ô tả chi tiết (string)
- `size`: <PERSON><PERSON><PERSON> thước file tính bằng bytes (number)
- `tags`: <PERSON><PERSON>ng các tag phân loại (string[])
- `type`: MIME type của file (string)
- `viewUrl`: URL xem (string, có thể để trống)

### Các loại file được hỗ trợ:
- **Images**: image/jpeg, image/png, image/gif, image/webp
- **Videos**: video/mp4, video/webm, video/quicktime, video/avi, etc.
- **Audio**: audio/mpeg, audio/wav, audio/ogg, audio/aac, etc.

### Giới hạn kích thước:
- Images: 5MB
- Videos: 50MB
- Audio: 20MB

## Response
```json
{
  "success": true,
  "result": [
    "https://s3-presigned-url-1.amazonaws.com/...",
    "https://s3-presigned-url-2.amazonaws.com/..."
  ],
  "message": "Success"
}
```

## Cách sử dụng:
1. Gọi API để nhận presigned URLs
2. Sử dụng URLs để upload files trực tiếp lên S3
3. Files sẽ được lưu với prefix `admin_{employeeId}`
4. Status ban đầu là `PENDING`, cần confirm sau khi upload thành công

## Lỗi có thể gặp:
- `FILE_TYPE_NOT_FOUND`: Loại file không được hỗ trợ
- `FILE_SIZE_EXCEEDED`: Kích thước file vượt quá giới hạn
- `FORBIDDEN`: Không có quyền admin
- `INTERNAL_SERVER_ERROR`: Lỗi server

## Test file:
Sử dụng file `test-presigned-urls.http` để test API.
