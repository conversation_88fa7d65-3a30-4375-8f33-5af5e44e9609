### Test Admin Media Presigned URLs API (với owner_type = 'ADMIN')
POST http://localhost:3000/admin/media/presigned-urls
Content-Type: application/json
Authorization: Bearer {{admin_token}}

[
  {
    "name": "admin-test-image",
    "description": "Test image uploaded by admin - owner_type sẽ được set thành ADMIN",
    "size": 1048576,
    "tags": ["admin", "test", "image"],
    "type": "image/jpeg",
    "viewUrl": ""
  },
  {
    "name": "admin-test-video",
    "description": "Test video uploaded by admin - owner_type sẽ được set thành ADMIN",
    "size": 10485760,
    "tags": ["admin", "test", "video"],
    "type": "video/mp4",
    "viewUrl": ""
  }
]

### Test User Media Presigned URLs API (với owner_type = 'USER')
POST http://localhost:3000/media/presigned-urls
Content-Type: application/json
Authorization: Bearer {{user_token}}

[
  {
    "name": "user-test-image",
    "description": "Test image uploaded by user - owner_type sẽ được set thành USER",
    "size": 1048576,
    "tags": ["user", "test", "image"],
    "type": "image/jpeg",
    "viewUrl": ""
  },
  {
    "name": "user-test-audio",
    "description": "Test audio uploaded by user - owner_type sẽ được set thành USER",
    "size": 5242880,
    "tags": ["user", "test", "audio"],
    "type": "audio/mp3",
    "viewUrl": ""
  }
]

### Variables
@admin_token = your_admin_jwt_token_here
@user_token = your_user_jwt_token_here
