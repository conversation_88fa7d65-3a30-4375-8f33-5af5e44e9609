### Test Media Approval APIs
### <PERSON><PERSON><PERSON> thay thế {{baseUrl}} và {{authToken}} với giá trị thực tế

### 1. <PERSON>ê duyệt một media
PATCH {{baseUrl}}/admin/media/550e8400-e29b-41d4-a716-446655440000/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "status": "APPROVED",
  "note": "Media đã được kiểm tra và phê duyệt"
}

### 2. Từ chối một media
PATCH {{baseUrl}}/admin/media/550e8400-e29b-41d4-a716-446655440001/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "status": "REJECTED",
  "note": "Media không phù hợp với tiêu chuẩn"
}

### 3. Chuyển media về trạng thái pending
PATCH {{baseUrl}}/admin/media/550e8400-e29b-41d4-a716-446655440002/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "status": "PENDING",
  "note": "Cần xem xét lại"
}

### 4. Phê duyệt nhiều media cùng lúc
PATCH {{baseUrl}}/admin/media/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "mediaIds": [
    "550e8400-e29b-41d4-a716-446655440003",
    "550e8400-e29b-41d4-a716-446655440004",
    "550e8400-e29b-41d4-a716-446655440005"
  ],
  "status": "APPROVED",
  "note": "Batch approval cho media đã kiểm tra"
}

### 5. Từ chối nhiều media cùng lúc
PATCH {{baseUrl}}/admin/media/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "mediaIds": [
    "550e8400-e29b-41d4-a716-446655440006",
    "550e8400-e29b-41d4-a716-446655440007"
  ],
  "status": "REJECTED",
  "note": "Batch rejection cho media không phù hợp"
}

### 6. Test với media ID không tồn tại (sẽ trả về lỗi)
PATCH {{baseUrl}}/admin/media/00000000-0000-0000-0000-000000000000/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "status": "APPROVED",
  "note": "Test với ID không tồn tại"
}

### 7. Test với trạng thái không hợp lệ (sẽ trả về lỗi validation)
PATCH {{baseUrl}}/admin/media/550e8400-e29b-41d4-a716-446655440000/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "status": "INVALID_STATUS",
  "note": "Test với trạng thái không hợp lệ"
}

### 8. Test bulk update với mảng rỗng (sẽ trả về lỗi validation)
PATCH {{baseUrl}}/admin/media/status
Authorization: Bearer {{authToken}}
Content-Type: application/json

{
  "mediaIds": [],
  "status": "APPROVED",
  "note": "Test với mảng rỗng"
}

### 9. Lấy danh sách media để kiểm tra trạng thái
GET {{baseUrl}}/admin/media?page=1&limit=10&status=PENDING
Authorization: Bearer {{authToken}}

### 10. Lấy chi tiết một media để kiểm tra trạng thái
GET {{baseUrl}}/admin/media/550e8400-e29b-41d4-a716-446655440000
Authorization: Bearer {{authToken}}
