import { ApiProperty } from '@nestjs/swagger';
import { IsA<PERSON>y, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';


export class MediaUploadDto {
  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'My beautiful image' })
  name: string;

  @IsOptional()
  @IsString()
  @ApiProperty({ example: 'An image uploaded by user', required: false })
  description?: string;

  @IsNumber()
  @IsNotEmpty()
  @ApiProperty({ example: 1048576 })
  size: number;

  @IsArray()
  @IsNotEmpty()
  @ApiProperty({ example: ['gif', 'funny'] })
  tags: string[];

  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  @IsNotEmpty()
  @ApiProperty({ example: 'test/test-1744857340088-abc123' })
  storageKey: string;

  @IsNumber()
  @IsOptional()
  @ApiProperty({ example: 1 })
  ownedBy: number;

  // Embedding có thể null ban đầu (sẽ gen sau)

  @IsOptional()
  nameEmbedding?: number[];

  @IsOptional()
  descriptionEmbedding?: number[];
}

