import { ApiProperty } from '@nestjs/swagger';
import { IsString } from 'class-validator';

export class CopyFileDto {
  @ApiProperty({ description: 'Source key of the file to copy' })
  @IsString()
  sourceKey: string;

  @ApiProperty({ description: 'Destination key for the copied file' })
  @IsString()
  destinationKey: string;

  @ApiProperty({ description: 'Destination bucket for the copied file' })
  @IsString()
  destinationBucket: string;
}
