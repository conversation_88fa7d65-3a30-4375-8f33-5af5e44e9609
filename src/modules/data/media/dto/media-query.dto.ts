import { QueryDto, SortDirection } from "@common/dto";
import { MediaStatusEnum } from '@modules/data/media/enums/media-status.enum';
import { OwnerTypeEnum } from '@modules/data/media/enums/owner-type.enum';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsOptional, IsString } from 'class-validator';
import { Type } from "class-transformer";

/**
 * Lớp mở rộng QueryDto để bao gồm trường `status` cho việc lọc media theo trạng thái
 */
export class MediaQueryDto extends QueryDto {
    @ApiProperty({
        description: 'Trường cần sắp xếp',
        example: 'createdAt',
        default: 'createdAt',
        required: false,
    })
    @IsOptional()
    @IsString()
    sortBy: string = 'createdAt';

    @ApiProperty({
        description: 'Hướng sắp xếp',
        enum: SortDirection,
        example: SortDirection.DESC,
        default: SortDirection.DESC,
        required: false,
    })
    @IsOptional()
    @IsEnum(SortDirection)
    sortDirection: SortDirection = SortDirection.DESC;
    @ApiProperty({
        description: 'Trạng thái của media',
        enum: MediaStatusEnum,
        required: false,
    })
    @IsOptional()
    @IsEnum([MediaStatusEnum.DRAFT, MediaStatusEnum.APPROVED, MediaStatusEnum.PENDING, MediaStatusEnum.REJECTED], {
        message: 'status chỉ có thể là DRAFT, APPROVED, PENDING hoặc REJECTED'
    })
    @Type(() => String)
    status?: MediaStatusEnum;

    @ApiProperty({
        description: 'Loại chủ sở hữu media',
        enum: OwnerTypeEnum,
        required: false,
        example: OwnerTypeEnum.USER
    })
    @IsOptional()
    @IsEnum(OwnerTypeEnum, {
        message: 'ownerType chỉ có thể là USER hoặc ADMIN'
    })
    ownerType?: OwnerTypeEnum;
}
