import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional, IsString, IsUUID, ArrayMinSize, IsArray } from 'class-validator';
import { MediaStatusEnum } from '../enums/media-status.enum';

/**
 * DTO cho việc cập nhật trạng thái một media
 */
export class UpdateMediaStatusDto {
  /**
   * Trạng thái mới của media
   */
  @ApiProperty({
    description: 'Trạng thái mới của media',
    enum: [MediaStatusEnum.APPROVED, MediaStatusEnum.REJECTED, MediaStatusEnum.PENDING],
    example: MediaStatusEnum.APPROVED,
  })
  @IsEnum([MediaStatusEnum.APPROVED, MediaStatusEnum.REJECTED, MediaStatusEnum.PENDING], {
    message: 'status chỉ có thể là APPROVED, REJECTED hoặc PENDING',
  })
  @IsNotEmpty({ message: 'status không được để trống' })
  status: MediaStatusEnum.APPROVED | MediaStatusEnum.REJECTED | MediaStatusEnum.PENDING;

  /**
   * Ghi chú về việc thay đổi trạng thái (tùy chọn)
   */
  @ApiProperty({
    description: 'Ghi chú về việc thay đổi trạng thái',
    example: 'Media đã được kiểm tra và phê duyệt',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'note phải là chuỗi' })
  note?: string;
}

/**
 * DTO cho việc cập nhật trạng thái nhiều media
 */
export class UpdateMultipleMediaStatusDto {
  /**
   * Danh sách ID của các media cần cập nhật trạng thái
   */
  @ApiProperty({
    description: 'Danh sách ID của các media cần cập nhật trạng thái',
    example: ['550e8400-e29b-41d4-a716-************', '6ba7b810-9dad-11d1-80b4-00c04fd430c8'],
    type: [String],
  })
  @IsArray({ message: 'mediaIds phải là một mảng' })
  @ArrayMinSize(1, { message: 'Phải có ít nhất một media ID' })
  @IsUUID('4', { each: true, message: 'Mỗi media ID phải là UUID hợp lệ' })
  mediaIds: string[];

  /**
   * Trạng thái mới của media
   */
  @ApiProperty({
    description: 'Trạng thái mới của media',
    enum: [MediaStatusEnum.APPROVED, MediaStatusEnum.REJECTED, MediaStatusEnum.PENDING],
    example: MediaStatusEnum.APPROVED,
  })
  @IsEnum([MediaStatusEnum.APPROVED, MediaStatusEnum.REJECTED, MediaStatusEnum.PENDING], {
    message: 'status chỉ có thể là APPROVED, REJECTED hoặc PENDING',
  })
  @IsNotEmpty({ message: 'status không được để trống' })
  status: MediaStatusEnum.APPROVED | MediaStatusEnum.REJECTED | MediaStatusEnum.PENDING;

  /**
   * Ghi chú về việc thay đổi trạng thái (tùy chọn)
   */
  @ApiProperty({
    description: 'Ghi chú về việc thay đổi trạng thái',
    example: 'Batch approval cho media đã kiểm tra',
    required: false,
  })
  @IsOptional()
  @IsString({ message: 'note phải là chuỗi' })
  note?: string;
}
