import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

/**
 * Entity đại diện cho bảng blog_purchases trong cơ sở dữ liệu
 */
@Entity('blog_purchases')
export class BlogPurchase {
  @PrimaryGeneratedColumn({ name: 'id' })
  id: number;

  @Column({ name: 'user_id', type: 'integer', nullable: true })
  userId: number;

  @Column({ name: 'blog_id', type: 'integer', nullable: true })
  blogId: number;

  @Column({ name: 'point', type: 'bigint', nullable: true })
  point: number;

  @Column({ name: 'purchased_at', type: 'bigint', nullable: true })
  purchasedAt: number;

  @Column({
    name: 'platform_fee_percent',
    type: 'double precision',
    nullable: true,
  })
  platformFeePercent: number;

  @Column({ name: 'seller_receive_price', type: 'bigint', nullable: true })
  sellerReceivePrice: number;
}
