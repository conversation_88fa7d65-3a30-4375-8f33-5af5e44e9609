# Blog Module API Documentation

## Tổng Quan

Module Blog cung cấp hệ thống quản lý blog hoàn chỉnh cho ngư<PERSON>i dùng, bao gồm tạo bà<PERSON> viết, qu<PERSON><PERSON> lý bình l<PERSON>, mua bài viết và theo dõi thống kê.

## Cấu Trúc Module

```
src/modules/blog/
├── user/                           # User APIs
│   ├── controllers/                # User controllers
│   │   ├── blog-user.controller.ts
│   │   ├── blog-comment-user.controller.ts
│   │   └── blog-purchase-user.controller.ts
│   ├── services/                   # User services
│   ├── dto/                        # User DTOs
│   └── blog-user.module.ts         # User module
├── entities/                       # Database entities
├── repositories/                   # Custom repositories
├── dto/                           # Shared DTOs
├── enums/                         # Enums
├── exceptions/                    # Error definitions
├── schemas/                       # Swagger schemas
├── swagger.json                   # Swagger documentation
└── blog.module.ts                 # Main module
```

## Entities

### 1. Blog
- **Bảng**: `blogs`
- **<PERSON><PERSON> tả**: Thông tin bài viết blog
- **<PERSON><PERSON><PERSON> trường chính**:
  - `id`: ID bài viết
  - `title`: Tiêu đề bài viết
  - `description`: Mô tả bài viết
  - `content`: URL file content trên CDN
  - `point`: Số point cần để mua bài viết
  - `viewCount`: Số lượt xem
  - `thumbnailUrl`: URL thumbnail
  - `tags`: Tags của bài viết (JSONB)
  - `userId`: ID người dùng tạo (nullable)
  - `employeeId`: ID nhân viên tạo (nullable)
  - `employeeModerator`: ID nhân viên kiểm duyệt
  - `authorType`: Loại tác giả (USER, SYSTEM)
  - `status`: Trạng thái (DRAFT, PENDING, APPROVED, REJECTED)
  - `enable`: Trạng thái hiển thị
  - `like`: Số lượt thích

### 2. BlogComment
- **Bảng**: `blog_comments`
- **Mô tả**: Bình luận bài viết
- **Các trường chính**:
  - `id`: ID bình luận
  - `blogId`: ID bài viết
  - `userId`: ID người dùng (nullable)
  - `employeeId`: ID nhân viên (nullable)
  - `content`: Nội dung bình luận
  - `authorType`: Loại tác giả
  - `parentCommentId`: ID bình luận cha (nullable)

### 3. BlogPurchase
- **Bảng**: `blog_purchases`
- **Mô tả**: Giao dịch mua bài viết
- **Các trường chính**:
  - `id`: ID giao dịch
  - `blogId`: ID bài viết
  - `userId`: ID người mua
  - `point`: Số point đã trả
  - `purchasedAt`: Thời gian mua

## API Endpoints

### User APIs

#### 1. Blog Management
- **GET** `/user/blogs` - Lấy danh sách bài viết với filtering
- **GET** `/user/blogs/detail/{id}` - Lấy chi tiết bài viết
- **POST** `/user/blogs` - Tạo bài viết mới
- **PUT** `/user/blogs/{id}/media` - Cập nhật media (content/thumbnail)
- **PUT** `/user/blogs/{id}/submit` - Gửi bài viết để kiểm duyệt
- **DELETE** `/user/blogs/{id}` - Xóa bài viết

#### 2. Blog Comments
- **POST** `/user/blogs/{blogId}/comments` - Tạo bình luận
- **GET** `/user/blogs/{blogId}/comments` - Lấy danh sách bình luận
- **DELETE** `/user/blogs/comments/{id}` - Xóa bình luận

#### 3. Blog Purchase
- **POST** `/user/blogs/{blogId}/purchase` - Mua bài viết
- **GET** `/user/blogs/{blogId}/purchased` - Kiểm tra trạng thái mua
- **GET** `/user/blogs/purchases` - Lấy danh sách bài viết đã mua
- **GET** `/user/blogs/purchases/{purchaseId}` - Lấy chi tiết giao dịch mua

## Enums

### BlogStatusEnum
- `DRAFT`: Bản nháp
- `PENDING`: Chờ kiểm duyệt
- `APPROVED`: Đã phê duyệt
- `REJECTED`: Bị từ chối

### AuthorTypeEnum
- `USER`: Người dùng
- `SYSTEM`: Hệ thống

### BlogOwnershipEnum
- `CREATED`: Bài viết do người dùng tạo
- `PURCHASED`: Bài viết người dùng đã mua
- `NOT_OWNED`: Bài viết người dùng chưa sở hữu

## Error Codes

Module Blog sử dụng các mã lỗi từ 20001-20099:

### Blog Errors (20001-20099)
- `20001`: Không tìm thấy bài viết hoặc bài viết không khả dụng
- `20002`: Bạn không có quyền truy cập bài viết này
- `20003`: Trạng thái bài viết không hợp lệ cho thao tác này
- `20004`: Bài viết đã được gửi để kiểm duyệt
- `20005`: Chỉ bài viết ở trạng thái nháp mới có thể gửi kiểm duyệt
- `20006`: Lỗi khi tạo bài viết
- `20007`: Lỗi khi cập nhật bài viết
- `20008`: Lỗi khi xóa bài viết
- `20009`: Bài viết không thể xóa ở trạng thái hiện tại

### Blog Comment Errors (20010-20019)
- `20010`: Không tìm thấy bình luận
- `20011`: Bạn không có quyền xóa bình luận này
- `20012`: Lỗi khi tạo bình luận
- `20013`: Lỗi khi xóa bình luận

### Blog Purchase Errors (20020-20029)
- `20020`: Bài viết đã được mua trước đó
- `20021`: Không đủ point để mua bài viết
- `20022`: Không thể mua bài viết của chính mình
- `20023`: Bài viết chưa được phê duyệt
- `20024`: Lỗi khi xử lý giao dịch mua
- `20025`: Không tìm thấy giao dịch mua

## Authentication & Authorization

- **User APIs**: Sử dụng `JwtUserGuard` - Yêu cầu token JWT của người dùng
- **Bearer Token**: Tất cả APIs yêu cầu header `Authorization: Bearer <token>`

## Response Format

Tất cả APIs đều trả về response theo format chuẩn:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    // Dữ liệu response
  }
}
```

Đối với APIs có phân trang:

```json
{
  "code": 200,
  "message": "Success",
  "result": {
    "content": [...],
    "totalItems": 100,
    "itemCount": 10,
    "itemsPerPage": 10,
    "totalPages": 10,
    "currentPage": 1
  }
}
```

## Validation Rules

### Blog Creation
- Tiêu đề: Bắt buộc, tối đa 500 ký tự
- Mô tả: Bắt buộc, tối đa 1000 ký tự
- Point: Bắt buộc, >= 0
- Tags: Bắt buộc, mỗi tag tối đa 50 ký tự

### Blog Comments
- Nội dung: Bắt buộc, tối đa 1000 ký tự
- Parent comment ID: Tùy chọn (để tạo reply)

### Phân Trang
- Page: >= 1
- Limit: 1-100
- Sort direction: ASC hoặc DESC

## Business Logic

### Blog Ownership & Access Control
1. **CREATED**: Người dùng có thể xem tất cả bài viết của mình với mọi trạng thái
2. **PURCHASED**: Người dùng chỉ xem được bài viết đã mua ở trạng thái APPROVED
3. **NOT_OWNED**: Người dùng chỉ xem được bài viết chưa sở hữu ở trạng thái APPROVED
4. **Content Access**: Trường content chỉ trả về khi:
   - Người dùng là tác giả của bài viết, HOẶC
   - Người dùng đã mua bài viết

### Blog Status Workflow
1. **DRAFT** → **PENDING**: Gửi để kiểm duyệt
2. **PENDING** → **APPROVED/REJECTED**: Admin kiểm duyệt
3. **REJECTED** → **PENDING**: Có thể gửi lại để kiểm duyệt
4. **Xóa**: Chỉ có thể xóa bài viết ở trạng thái DRAFT hoặc REJECTED

### Purchase Logic
1. Không thể mua bài viết của chính mình
2. Không thể mua bài viết đã mua trước đó
3. Chỉ có thể mua bài viết ở trạng thái APPROVED
4. Phải có đủ point để mua

## Performance Considerations

1. **Indexing**: Các trường thường xuyên query đã được index
2. **Pagination**: Tất cả list APIs đều hỗ trợ phân trang
3. **Query Optimization**: Sử dụng QueryBuilder và join hiệu quả
4. **Content Access**: Lazy loading cho content để tối ưu performance

## Security

1. **Input Validation**: Tất cả input đều được validate
2. **Authorization**: Kiểm tra quyền sở hữu cho từng thao tác
3. **Content Sanitization**: Làm sạch input để tránh XSS
4. **Rate Limiting**: Áp dụng rate limiting cho các APIs quan trọng

## File Upload

Module sử dụng S3 service để upload file:
1. **Content**: Upload dưới dạng HTML/text
2. **Thumbnail**: Upload dưới dạng image
3. **Pre-signed URLs**: Sử dụng pre-signed URLs để upload trực tiếp lên S3
4. **File Validation**: Validate file type và size trước khi upload
