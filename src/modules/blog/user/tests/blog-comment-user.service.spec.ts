import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { BlogCommentUserService } from '../services/blog-comment-user.service';
import { Blog, BlogComment } from '../../entities';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';
import { CreateBlogCommentDto } from '../../dto/create-blog-comment.dto';
import { NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { GetBlogCommentsDto } from '../../dto/get-blog-comments.dto';
import { SqlHelper } from '@/common/helpers/sql.helper';
import { BlogUserException, BlogUserErrorCode } from '@/modules/blog/exceptions/blog-user.exception';

// Mock data
const mockBlog = {
  id: 1,
  title: 'Test Blog Title',
  content: 'https://cdn.example.com/blogs/content-123.html',
  point: 100,
  viewCount: 1000,
  thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
  tags: ['nestjs', 'typescript', 'backend'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userId: 2,
  employeeId: null as number | null,
  employeeModerator: null as number | null,
  authorType: AuthorTypeEnum.USER,
  status: BlogStatusEnum.APPROVED,
  enable: true,
  like: 500
};

const mockUser = {
  id: 1,
  fullName: 'Nguyễn Văn A',
  email: '<EMAIL>',
  phoneNumber: '0912345678',
  isActive: true,
  avatar: 'https://cdn.example.com/avatars/user1.jpg',
};

const mockComment = {
  id: 1,
  blogId: 1,
  userId: 1,
  content: 'This is a test comment',
  createdAt: 1625097600000,
  authorType: AuthorTypeEnum.USER,
  employeeId: null as number | null,
  parentCommentId: null as number | null,
};

const mockReplyComment = {
  id: 2,
  blogId: 1,
  userId: 1,
  content: 'This is a reply comment',
  createdAt: 1625097700000,
  authorType: AuthorTypeEnum.USER,
  employeeId: null as number | null,
  parentCommentId: 1,
};

describe('BlogCommentUserService', () => {
  let service: BlogCommentUserService;
  let sqlHelper: SqlHelper;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogCommentUserService,
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(BlogComment),
          useValue: {
            find: jest.fn(),
            findOne: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            delete: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[mockComment], 1]),
              getMany: jest.fn().mockResolvedValue([mockComment]),
            })),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            select: jest.fn(),
            exists: jest.fn(),
            insert: jest.fn().mockResolvedValue(mockComment),
            update: jest.fn().mockResolvedValue([{ affected: 1 }]),
            count: jest.fn().mockResolvedValue(1),
            delete: jest.fn().mockResolvedValue({ affected: 1 }),
            executeInTransaction: jest.fn().mockImplementation(async (callback) => {
              return await callback({} as any);
            })
          },
        },
      ],
    }).compile();

    service = module.get<BlogCommentUserService>(BlogCommentUserService);
    sqlHelper = module.get<SqlHelper>(SqlHelper);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createComment', () => {
    it('should create a new comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const dto: CreateBlogCommentDto = {
        content: 'This is a test comment',
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(mockComment);

      // Act
      const result = await service.createComment(blogId, userId, dto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.insert).toHaveBeenCalled();
      expect(result).toEqual(mockComment);
    });

    it('should create a reply comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const dto: CreateBlogCommentDto = {
        content: 'This is a reply comment',
        parent_comment_id: 1,
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'select').mockResolvedValueOnce([mockComment]);
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(mockReplyComment);

      // Act
      const result = await service.createComment(blogId, userId, dto);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.insert).toHaveBeenCalled();
      expect(result).toEqual(mockReplyComment);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const userId = 1;
      const dto: CreateBlogCommentDto = {
        content: 'This is a test comment',
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.createComment(blogId, userId, dto)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_FOUND)
      );
    });

    it('should throw NotFoundException when parent comment not found', async () => {
      // Arrange
      const blogId = 1;
      const userId = 1;
      const dto: CreateBlogCommentDto = {
        content: 'This is a reply comment',
        parent_comment_id: 999,
      };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.createComment(blogId, userId, dto)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_COMMENT_PARENT_NOT_FOUND)
      );
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment successfully', async () => {
      // Arrange
      const commentId = 1;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockComment]);
      jest.spyOn(sqlHelper, 'delete').mockResolvedValue([{ affected: 1 }]);

      // Act
      await service.deleteComment(commentId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.delete).toHaveBeenCalled();
    });

    it('should delete a comment and its replies', async () => {
      // Arrange
      const commentId = 1;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockComment]);
      jest.spyOn(sqlHelper, 'delete').mockResolvedValueOnce([{ affected: 1 }]).mockResolvedValueOnce([{ affected: 1 }]);

      // Act
      await service.deleteComment(commentId, userId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.delete).toHaveBeenCalledTimes(2);
    });

    it('should throw NotFoundException when comment not found', async () => {
      // Arrange
      const commentId = 999;
      const userId = 1;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.deleteComment(commentId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_COMMENT_NOT_FOUND)
      );
    });

    it('should throw ForbiddenException when user is not the comment owner', async () => {
      // Arrange
      const commentId = 1;
      const userId = 2; // Different from comment.userId
      const commentByOtherUser = {
        ...mockComment,
        userId: 3, // Different from the requesting user
      };

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([commentByOtherUser]);

      // Act & Assert
      await expect(service.deleteComment(commentId, userId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_COMMENT_ACCESS_DENIED)
      );
    });
  });

  describe('getBlogComments', () => {
    it('should return paginated comments', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockComment]) // Root comments
        .mockResolvedValueOnce([]) // Replies
        .mockResolvedValueOnce([]); // User info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(1);

      // Act
      const result = await service.getBlogComments(blogId, query);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalledTimes(3);
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });

    it('should return comments with replies', async () => {
      // Arrange
      const blogId = 1;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true);
      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockComment]) // Root comments
        .mockResolvedValueOnce([mockReplyComment]) // Replies
        .mockResolvedValueOnce([]); // User info
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(1);

      // Act
      const result = await service.getBlogComments(blogId, query);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalledTimes(4);
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result.content[0]).toHaveProperty('replies');
      expect(result.content[0].replies.length).toBe(1);
      expect(result.content[0].replies[0].id).toBe(mockReplyComment.id);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const query: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false);

      // Act & Assert
      await expect(service.getBlogComments(blogId, query)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_FOUND)
      );
    });
  });
});
