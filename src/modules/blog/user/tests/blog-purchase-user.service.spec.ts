import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogPurchaseUserService } from '../services/blog-purchase-user.service';
import { Blog, BlogPurchase } from '../../entities';
import { User } from '@/modules/user/entities/user.entity';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';
import { GetPurchaseListDto } from '../../dto';
import { BlogPurchaseRepository } from '../../repositories/blog-purchase.repository';
import { SqlHelper } from '@common/helpers/sql.helper';
import { BlogUserException, BlogUserErrorCode } from '@modules/blog/exceptions/blog-user.exception';

// Mock data
const mockBlog = {
  id: 1,
  title: 'Test Blog Title',
  content: 'https://cdn.example.com/blogs/content-123.html',
  point: 100,
  viewCount: 1000,
  thumbnailUrl: 'https://cdn.example.com/blogs/thumbnail-123.jpg',
  tags: ['nestjs', 'typescript', 'backend'],
  createdAt: 1625097600000,
  updatedAt: 1625097600000,
  userId: 2, // Different from the buyer
  employeeId: null,
  employeeModerator: null,
  authorType: AuthorTypeEnum.USER,
  status: BlogStatusEnum.APPROVED,
  enable: true,
  like: 500
};

const mockUser = {
  id: 1,
  fullName: 'Nguyễn Văn A',
  email: '<EMAIL>',
  phoneNumber: '0912345678',
  isActive: true,
  pointsBalance: 500, // Enough points to purchase
};

const mockPurchase = {
  id: 1,
  blogId: 1,
  userId: 1,
  purchasedAt: 1625097600000,
  point: 100,
  platformFeePercent: 10,
  sellerReceivePrice: 90,
};

describe('BlogPurchaseUserService', () => {
  let service: BlogPurchaseUserService;
  let sqlHelper: SqlHelper;
  let blogRepository: Repository<Blog>;
  let userRepository: Repository<User>;
  let blogPurchaseRepository: Repository<BlogPurchase>;
  let customBlogPurchaseRepository: BlogPurchaseRepository;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogPurchaseUserService,
        {
          provide: getRepositoryToken(BlogPurchase),
          useValue: {
            findOne: jest.fn(),
            create: jest.fn(),
            save: jest.fn(),
            createQueryBuilder: jest.fn(() => ({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[mockPurchase], 1]),
            })),
          },
        },
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            findOne: jest.fn(),
          },
        },
        {
          provide: getRepositoryToken(User),
          useValue: {
            findOne: jest.fn(),
            save: jest.fn(),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            select: jest.fn(),
            exists: jest.fn(),
            insert: jest.fn().mockResolvedValue({ id: 1 }),
            update: jest.fn().mockResolvedValue([{ affected: 1 }]),
            count: jest.fn().mockResolvedValue(1),
            executeInTransaction: jest.fn().mockImplementation(async (callback) => {
              return await callback({} as any);
            })
          },
        },
        {
          provide: BlogPurchaseRepository,
          useValue: {
            getPurchasedBlogs: jest.fn(),
            purchaseBlogWithTransaction: jest.fn().mockResolvedValue(mockPurchase),
          },
        },
      ],
    }).compile();

    service = module.get<BlogPurchaseUserService>(BlogPurchaseUserService);
    sqlHelper = module.get<SqlHelper>(SqlHelper);
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    userRepository = module.get<Repository<User>>(getRepositoryToken(User));
    blogPurchaseRepository = module.get<Repository<BlogPurchase>>(getRepositoryToken(BlogPurchase));
    customBlogPurchaseRepository = module.get<BlogPurchaseRepository>(BlogPurchaseRepository);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('purchaseBlog', () => {
    it('should purchase a blog successfully', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([mockUser]) // User
        .mockResolvedValueOnce([]); // No purchase yet
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // Not purchased yet
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue(mockPurchase);
      jest.spyOn(sqlHelper, 'update').mockResolvedValue([{ affected: 1 }]);



      // Act
      await service.purchaseBlog(userId, blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(customBlogPurchaseRepository.purchaseBlogWithTransaction).toHaveBeenCalled();
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const userId = 1;
      const blogId = 999;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.purchaseBlog(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_FOUND)
      );
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const userId = 999;
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([]); // User not found

      // Act & Assert
      await expect(service.purchaseBlog(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED, 'User not found or not active')
      );
    });

    it('should throw BadRequestException when blog is already purchased', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([mockUser]); // User
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // Already purchased

      // Act & Assert
      await expect(service.purchaseBlog(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ALREADY_PURCHASED)
      );
    });

    it('should throw BadRequestException when user has insufficient points', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;
      const userWithLowPoints = {
        ...mockUser,
        pointsBalance: 50, // Not enough points (blog costs 100)
      };

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([userWithLowPoints]); // User with low points
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // Not purchased yet

      // Act & Assert
      await expect(service.purchaseBlog(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_INSUFFICIENT_POINTS)
      );
    });

    it('should not deduct points when blog is free', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;
      const freeBlog = {
        ...mockBlog,
        point: 0, // Free blog
      };

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([freeBlog]) // Free blog
        .mockResolvedValueOnce([mockUser]) // User
        .mockResolvedValueOnce([]); // No purchase yet
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // Not purchased yet
      jest.spyOn(sqlHelper, 'insert').mockResolvedValue({ ...mockPurchase, point: 0 });
      jest.spyOn(sqlHelper, 'update').mockResolvedValue([{ affected: 1 }]);



      // Act
      await service.purchaseBlog(userId, blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(customBlogPurchaseRepository.purchaseBlogWithTransaction).toHaveBeenCalled();
      // No points deduction for free blog
    });

    it('should not allow purchase of own blog', async () => {
      // Arrange
      const userId = 2; // Same as blog.userId
      const blogId = 1;
      const blogByUser = {
        ...mockBlog,
        userId: 2, // Same as the buyer
      };

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([blogByUser]) // Blog by user
        .mockResolvedValueOnce([{ ...mockUser, id: 2 }]); // User is the author

      // Act & Assert
      await expect(service.purchaseBlog(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_CANNOT_PURCHASE_OWN)
      );
    });
  });

  describe('checkPurchaseStatus', () => {
    it('should return purchase status when blog is purchased', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([mockPurchase]); // Purchase

      // Act
      const result = await service.checkPurchaseStatus(userId, blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        purchased: true,
        purchased_at: mockPurchase.purchasedAt,
      });
    });

    it('should return not purchased status when blog is not purchased', async () => {
      // Arrange
      const userId = 1;
      const blogId = 1;

      jest.spyOn(sqlHelper, 'select')
        .mockResolvedValueOnce([mockBlog]) // Blog
        .mockResolvedValueOnce([]); // No purchase

      // Act
      const result = await service.checkPurchaseStatus(userId, blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalledTimes(2);
      expect(result).toEqual({
        purchased: false,
      });
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const userId = 1;
      const blogId = 999;

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([]);

      // Act & Assert
      await expect(service.checkPurchaseStatus(userId, blogId)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_NOT_FOUND)
      );
    });

    it('should return purchased status when user is the blog author', async () => {
      // Arrange
      const userId = 2; // Same as blog.userId
      const blogId = 1;
      const blogByUser = {
        ...mockBlog,
        userId: 2, // Same as the requesting user
      };

      jest.spyOn(sqlHelper, 'select').mockResolvedValue([blogByUser]);

      // Act
      const result = await service.checkPurchaseStatus(userId, blogId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalledTimes(1);
      expect(result).toEqual({
        purchased: true,
      });
    });
  });

  describe('getPurchasedBlogs', () => {
    it('should return paginated purchased blogs', async () => {
      // Arrange
      const userId = 1;
      const query: GetPurchaseListDto = { page: 1, limit: 10 };

      // Mock SqlHelper
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // User exists
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockPurchase]); // Purchases
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(1); // Total count

      // Act
      const result = await service.getPurchasedBlogs(userId, query);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });

    it('should filter by blog_id', async () => {
      // Arrange
      const userId = 1;
      const query: GetPurchaseListDto = {
        page: 1,
        limit: 10,
        blog_id: 1
      };

      // Mock SqlHelper
      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(true); // User exists
      jest.spyOn(sqlHelper, 'select').mockResolvedValue([mockPurchase]); // Purchases
      jest.spyOn(sqlHelper, 'count').mockResolvedValue(1); // Total count

      // Act
      const result = await service.getPurchasedBlogs(userId, query);

      // Assert
      expect(sqlHelper.exists).toHaveBeenCalled();
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.count).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
    });

    it('should throw NotFoundException when user not found', async () => {
      // Arrange
      const userId = 999;
      const query: GetPurchaseListDto = { page: 1, limit: 10 };

      jest.spyOn(sqlHelper, 'exists').mockResolvedValue(false); // User not found

      // Act & Assert
      await expect(service.getPurchasedBlogs(userId, query)).rejects.toThrow(
        new BlogUserException(BlogUserErrorCode.BLOG_ACCESS_DENIED, 'User not found or not active')
      );
    });
  });
});
