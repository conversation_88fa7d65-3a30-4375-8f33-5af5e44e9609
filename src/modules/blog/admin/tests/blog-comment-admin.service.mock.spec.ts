import { Test, TestingModule } from '@nestjs/testing';
import { getRepositoryToken } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogCommentAdminService } from '../services/blog-comment-admin.service';
import { BlogComment } from '../../entities/blog-comment.entity';
import { Blog } from '../../entities/blog.entity';
import { CreateBlogCommentDto } from '../../dto/create-blog-comment.dto';
import { GetBlogCommentsDto } from '../../dto/get-blog-comments.dto';
import { NotFoundException } from '@nestjs/common';
import { AuthorTypeEnum } from '../../enums';
import { SqlHelper } from '@/common/helpers/sql.helper';



describe('BlogCommentAdminService', () => {
  let service: BlogCommentAdminService;
  let blogRepository: Repository<Blog>;
  let blogCommentRepository: Repository<BlogComment>;
  let sqlHelper: any;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        BlogCommentAdminService,
        {
          provide: getRepositoryToken(Blog),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              leftJoinAndSelect: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
            }),
          },
        },
        {
          provide: getRepositoryToken(BlogComment),
          useValue: {
            findOne: jest.fn(),
            find: jest.fn(),
            save: jest.fn(),
            create: jest.fn(),
            createQueryBuilder: jest.fn().mockReturnValue({
              where: jest.fn().mockReturnThis(),
              andWhere: jest.fn().mockReturnThis(),
              orderBy: jest.fn().mockReturnThis(),
              skip: jest.fn().mockReturnThis(),
              take: jest.fn().mockReturnThis(),
              getManyAndCount: jest.fn().mockResolvedValue([[], 0]),
            }),
          },
        },
        {
          provide: SqlHelper,
          useValue: {
            select: jest.fn(),
            insert: jest.fn(),
            delete: jest.fn(),
            exists: jest.fn(),
            count: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get<BlogCommentAdminService>(BlogCommentAdminService);
    blogRepository = module.get<Repository<Blog>>(getRepositoryToken(Blog));
    blogCommentRepository = module.get<Repository<BlogComment>>(getRepositoryToken(BlogComment));
    sqlHelper = module.get<SqlHelper>(SqlHelper);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('createSystemComment', () => {
    it('should create a new comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin comment',
      };
      const mockBlog = { id: 1, title: 'Test Blog' };
      const mockComment = {
        id: 1,
        blogId: 1,
        userId: null,
        employeeId: 5,
        content: 'This is an admin comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.SYSTEM,
        parentCommentId: null,
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as any);
      sqlHelper.insert.mockResolvedValue(mockComment);

      // Act
      const result = await service.createSystemComment(blogId, employeeId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(sqlHelper.insert).toHaveBeenCalledWith(
        'blog_comments',
        expect.objectContaining({
          blog_id: blogId,
          user_id: null,
          content: dto.content,
          author_type: AuthorTypeEnum.SYSTEM,
          parent_comment_id: null
        }),
        expect.any(Array)
      );
      expect(result).toEqual(mockComment);
    });

    it('should create a reply comment successfully', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin reply comment',
        parent_comment_id: 1,
      };
      const mockBlog = { id: 1, title: 'Test Blog' };
      const mockReplyComment = {
        id: 2,
        blogId: 1,
        userId: null,
        employeeId: 5,
        content: 'This is an admin reply comment',
        createdAt: Date.now(),
        authorType: AuthorTypeEnum.SYSTEM,
        parentCommentId: 1,
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as any);
      sqlHelper.select.mockResolvedValue([{ id: 1, parentCommentId: null }]);
      sqlHelper.insert.mockResolvedValue(mockReplyComment);

      // Act
      const result = await service.createSystemComment(blogId, employeeId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.insert).toHaveBeenCalledWith(
        'blog_comments',
        expect.objectContaining({
          blog_id: blogId,
          user_id: null,
          content: dto.content,
          author_type: AuthorTypeEnum.SYSTEM,
          parent_comment_id: dto.parent_comment_id
        }),
        expect.any(Array)
      );
      expect(result).toEqual(mockReplyComment);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin comment',
      };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.createSystemComment(blogId, employeeId, dto)).rejects.toThrow(
        new NotFoundException(`Blog with ID ${blogId} not found`)
      );
    });

    it('should throw NotFoundException when parent comment not found', async () => {
      // Arrange
      const blogId = 1;
      const employeeId = 5;
      const dto: CreateBlogCommentDto = {
        content: 'This is an admin reply comment',
        parent_comment_id: 999,
      };
      const mockBlog = { id: 1, title: 'Test Blog' };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as any);
      sqlHelper.select.mockResolvedValue([]);

      // Act & Assert
      await expect(service.createSystemComment(blogId, employeeId, dto)).rejects.toThrow(
        new NotFoundException(`Parent comment with ID ${dto.parent_comment_id} not found`)
      );
    });
  });

  describe('deleteComment', () => {
    it('should delete a comment successfully', async () => {
      // Arrange
      const commentId = 1;

      sqlHelper.select.mockResolvedValue([{ id: commentId }]);
      sqlHelper.delete.mockResolvedValue(null);

      // Act
      const result = await service.deleteComment(commentId);

      // Assert
      expect(sqlHelper.select).toHaveBeenCalled();
      expect(sqlHelper.delete).toHaveBeenCalledTimes(2); // Once for replies, once for the comment itself
      expect(result).toBeNull();
    });

    it('should throw NotFoundException when comment not found', async () => {
      // Arrange
      const commentId = 999;

      sqlHelper.select.mockResolvedValue([]);

      // Act & Assert
      await expect(service.deleteComment(commentId)).rejects.toThrow(
        new NotFoundException(`Comment with ID ${commentId} not found`)
      );
    });
  });

  describe('getComments', () => {
    it('should return paginated comments', async () => {
      // Arrange
      const blogId = 1;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockBlog = { id: 1, title: 'Test Blog' };
      const mockComments = [
        {
          id: 1,
          blogId: 1,
          userId: null,
          employeeId: 5,
          content: 'This is an admin comment',
          createdAt: Date.now(),
          authorType: AuthorTypeEnum.SYSTEM,
          parentCommentId: null
        }
      ];
      const mockReplies = [
        {
          id: 2,
          blogId: 1,
          userId: null,
          employeeId: 5,
          content: 'This is an admin reply comment',
          createdAt: Date.now(),
          authorType: AuthorTypeEnum.SYSTEM,
          parentCommentId: 1
        }
      ];

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as any);
      jest.spyOn(blogCommentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([mockComments, 1])
      } as any);
      jest.spyOn(blogCommentRepository, 'find').mockResolvedValue(mockReplies as any);

      // Act
      const result = await service.getComments(blogId, dto);

      // Assert
      expect(blogRepository.findOne).toHaveBeenCalledWith({ where: { id: blogId } });
      expect(blogCommentRepository.createQueryBuilder).toHaveBeenCalled();
      expect(blogCommentRepository.find).toHaveBeenCalled();
      expect(result.content.length).toBe(1);
      expect(result.totalItems).toBe(1);
      expect(result.currentPage).toBe(1);
    });

    it('should throw NotFoundException when blog not found', async () => {
      // Arrange
      const blogId = 999;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(null);

      // Act & Assert
      await expect(service.getComments(blogId, dto)).rejects.toThrow(
        new NotFoundException(`Blog with ID ${blogId} not found`)
      );
    });

    it('should return empty array when no comments found', async () => {
      // Arrange
      const blogId = 1;
      const dto: GetBlogCommentsDto = { page: 1, limit: 10 };
      const mockBlog = { id: 1, title: 'Test Blog' };

      jest.spyOn(blogRepository, 'findOne').mockResolvedValue(mockBlog as any);
      jest.spyOn(blogCommentRepository, 'createQueryBuilder').mockReturnValue({
        where: jest.fn().mockReturnThis(),
        andWhere: jest.fn().mockReturnThis(),
        orderBy: jest.fn().mockReturnThis(),
        skip: jest.fn().mockReturnThis(),
        take: jest.fn().mockReturnThis(),
        getManyAndCount: jest.fn().mockResolvedValue([[], 0])
      } as any);

      // Act
      const result = await service.getComments(blogId, dto);

      // Assert
      expect(result.content).toEqual([]);
      expect(result.totalItems).toBe(0);
    });
  });
});
