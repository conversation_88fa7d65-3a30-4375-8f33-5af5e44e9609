import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { BlogPurchase } from '../../entities/blog-purchase.entity';
import { BlogPurchaseQueryDto, BlogPurchaseStatisticsDto, BlogPurchaseStatisticsResponseDto } from '../../dto';
import { SqlHelper, WhereCondition, PaginationInfo, SqlParamValue } from '@common/helpers/sql.helper';
import { Blog } from '../../entities/blog.entity';
import { User } from '@/modules/user/entities/user.entity';

@Injectable()
export class BlogPurchaseAdminService {
  constructor(
    @InjectRepository(BlogPurchase)
    private readonly blogPurchaseRepository: Repository<BlogPurchase>,
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly sqlHelper: SqlHelper,
  ) {}

  /**
   * L<PERSON>y thống kê giao dịch mua bài viết
   * @param dto Tham số lọc
   * @returns Thống kê giao dịch
   */
  async getStatistics(dto: BlogPurchaseStatisticsDto): Promise<BlogPurchaseStatisticsResponseDto> {
    const conditions: WhereCondition[] = [];

    // Thêm điều kiện lọc theo thời gian nếu có
    if (dto.start_date) {
      conditions.push({
        condition: 'purchased_at >= :startDate',
        params: { startDate: dto.start_date as SqlParamValue }
      });
    }

    if (dto.end_date) {
      conditions.push({
        condition: 'purchased_at <= :endDate',
        params: { endDate: dto.end_date as SqlParamValue }
      });
    }

    // Lấy tổng số giao dịch
    const totalPurchases = await this.sqlHelper.count(
      'blog_purchases',
      conditions
    );

    // Lấy tổng số điểm đã sử dụng
    const totalPointsResult = await this.sqlHelper.select(
      'blog_purchases',
      ['SUM(point) as total'],
      conditions
    );
    // Ensure totalPoints is a number, not an empty object
    const totalPoints = typeof totalPointsResult[0]?.total === 'object' && Object.keys(totalPointsResult[0]?.total || {}).length === 0
      ? 0
      : Number(totalPointsResult[0]?.total || 0);

    // Tính tổng phí nền tảng và số điểm người bán nhận được
    const platformFeeResult = await this.sqlHelper.select(
      'blog_purchases',
      [
        'SUM(point * platform_fee_percent) as totalPlatformFee',
        'SUM(seller_receive_price) as totalToSellers'
      ],
      conditions
    );

    // Ensure totalPlatformFee is a number, not an empty object
    const totalPlatformFee = typeof platformFeeResult[0]?.totalPlatformFee === 'object' && Object.keys(platformFeeResult[0]?.totalPlatformFee || {}).length === 0
      ? 0
      : Math.round(Number(platformFeeResult[0]?.totalPlatformFee || 0));

    // Ensure totalToSellers is a number, not an empty object
    const totalToSellers = typeof platformFeeResult[0]?.totalToSellers === 'object' && Object.keys(platformFeeResult[0]?.totalToSellers || {}).length === 0
      ? 0
      : Math.round(Number(platformFeeResult[0]?.totalToSellers || 0));

    return {
      total_purchases: totalPurchases,
      total_points: totalPoints, // Already converted to number above
      total_platform_fee: totalPlatformFee,
      total_to_sellers: totalToSellers
    };
  }

  /**
   * Lấy danh sách giao dịch mua bài viết
   * @param dto Tham số lọc và phân trang
   * @returns Danh sách giao dịch có phân trang
   */
  async getPurchases(dto: BlogPurchaseQueryDto) {
    const { page = 1, limit = 10, blog_id, user_id, start_date, end_date } = dto;
    const offset = (page - 1) * limit;
    const conditions: WhereCondition[] = [];

    // Thêm các điều kiện lọc
    if (blog_id) {
      conditions.push({
        condition: 'blog_id = :blogId',
        params: { blogId: blog_id as SqlParamValue }
      });
    }

    if (user_id) {
      conditions.push({
        condition: 'user_id = :userId',
        params: { userId: user_id as SqlParamValue }
      });
    }

    if (start_date) {
      conditions.push({
        condition: 'purchased_at >= :startDate',
        params: { startDate: start_date as SqlParamValue }
      });
    }

    if (end_date) {
      conditions.push({
        condition: 'purchased_at <= :endDate',
        params: { endDate: end_date as SqlParamValue }
      });
    }

    // Lấy danh sách giao dịch
    const purchases = await this.sqlHelper.select(
      'blog_purchases',
      [
        'id', 'user_id as userId', 'blog_id as blogId', 'point',
        'purchased_at as purchasedAt', 'platform_fee_percent as platformFeePercent',
        'seller_receive_price as sellerReceivePrice'
      ],
      conditions,
      [],
      [{ column: 'purchased_at', direction: 'DESC' }],
      [],
      undefined,
      { limit, offset } as PaginationInfo
    );

    // Lấy tổng số giao dịch
    const totalItems = await this.sqlHelper.count(
      'blog_purchases',
      conditions
    );

    // Lấy thông tin bài viết và người dùng
    const enrichedPurchases = await Promise.all(
      purchases.map(async (purchase) => {
        // Lấy thông tin bài viết
        const blog = await this.sqlHelper.select(
          'blogs',
          ['id', 'title', 'author_type as authorType'],
          [{ condition: 'id = :id', params: { id: purchase.blogId as SqlParamValue } }]
        );

        // Lấy thông tin người dùng
        const user = await this.sqlHelper.select(
          'users',
          ['id', 'full_name as name'],
          [{ condition: 'id = :id', params: { id: purchase.userId as SqlParamValue } }]
        );

        return {
          ...purchase,
          blog: blog[0] || null,
          user: user[0] || null
        };
      })
    );

    return {
      content: enrichedPurchases,
      totalItems,
      itemCount: purchases.length,
      itemsPerPage: limit,
      totalPages: Math.ceil(totalItems / limit),
      currentPage: page,
    };
  }
}
