import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Blog } from '@modules/blog/entities';
import { User } from '@/modules/user/entities/user.entity';
import {
  BlogResponseDto,
  CreateBlogDto,
  GetAdminBlogsDto,
  ModerateBlogDto,
  PaginatedBlogResponseDto,
  UpdateBlogMediaDto,
} from '../../dto';
import { AuthorTypeEnum, BlogStatusEnum } from '../../enums';
import { SqlHelper } from '@common/helpers/sql.helper';
import { S3Service } from '@/shared/services/s3.service';
import { CategoryFolderEnum, FileSizeEnum, FileTypeEnum, generateS3Key, TimeIntervalEnum, MediaType } from '@/shared/utils';
import { ImageType, ImageTypeEnum } from '@/shared/utils/file/image-media_type.util';
import { FileType } from '@/shared/utils/file/file-media-type.util';

@Injectable()
export class BlogAdminService {
  constructor(
    @InjectRepository(Blog)
    private readonly blogRepository: Repository<Blog>,
    @InjectRepository(User)
    private readonly userRepository: Repository<User>,
    private readonly sqlHelper: SqlHelper,
    private readonly s3Service: S3Service,
  ) {}

  /**
   * Lấy danh sách tất cả bài viết với phân trang và lọc
   */
  async findAll(dto: GetAdminBlogsDto): Promise<PaginatedBlogResponseDto> {
    try {
      const paginatedResult = await this.sqlHelper.getPaginatedData<
        Blog,
        BlogResponseDto
      >(this.blogRepository, dto, {
        alias: 'blog',
        selectFields: [
          'id',
          'title',
          'content',
          'point',
          'viewCount',
          'thumbnailUrl',
          'tags',
          'createdAt',
          'updatedAt',
          'userId',
          'authorType',
          'status',
          'enable',
          'like',
          'employeeModerator',
          'employeeId',
          'description',
        ],
        searchFields: ['title'],
        customize: (qb) => {
          // Áp dụng các điều kiện lọc
          if (dto.status) {
            qb.andWhere('blog.status = :status', { status: dto.status });
          }

          // Xử lý authorType
          if (dto.authorType) {
            qb.andWhere('blog.author_type = :authorType', {
              authorType: dto.authorType,
            });
          }

          if (dto.userId) {
            qb.andWhere('blog.user_id = :userId', { userId: dto.userId });
          }

          if (dto.employeeId) {
            qb.andWhere('blog.employee_id = :employeeId', {
              employeeId: dto.employeeId,
            });
          }

          // Join với bảng user để lấy thông tin tác giả
          qb.leftJoin(User, 'user', 'blog.user_id = user.id').addSelect([
            'user.id',
            'user.fullName',
            'user.avatar',
          ]);

          return qb;
        },
        dtoType: BlogResponseDto,
      });

      // Transform the data to match the expected response format
      const transformedItems = paginatedResult.items.map((item) => {
        // Format content URL if needed
        let content = item.content;
        if (content && !content.startsWith('http')) {
          const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
          content = `${cdnUrl}/${content}`;
        }

        // Format thumbnail URL if needed
        let thumbnailUrl = item.thumbnailUrl;
        if (thumbnailUrl && !thumbnailUrl.startsWith('http')) {
          const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
          thumbnailUrl = `${cdnUrl}/${thumbnailUrl}`;
        }

        return {
          ...item,
          content,
          thumbnailUrl,
          author: {
            id:
              item.authorType === AuthorTypeEnum.USER
                ? (item.userId ?? null)
                : (item.employeeId ?? null),
            name: item.user?.fullName || 'System',
            type: item.authorType || AuthorTypeEnum.SYSTEM,
            avatar:
              item.user?.avatar ||
              'https://cdn.example.com/avatars/default.jpg',
          },
        };
      });

      return {
        content: transformedItems,
        totalItems: paginatedResult.meta.totalItems,
        itemCount: paginatedResult.meta.itemCount,
        itemsPerPage: paginatedResult.meta.itemsPerPage,
        totalPages: paginatedResult.meta.totalPages,
        currentPage: paginatedResult.meta.currentPage,
      };
    } catch (error) {
      throw new NotFoundException(
        'Failed to fetch blogs. Please try again later.',
      );
    }
  }

  /**
   * Lấy danh sách bài viết đang chờ kiểm duyệt
   */
  async findPendingBlogs(
    dto: GetAdminBlogsDto,
  ): Promise<PaginatedBlogResponseDto> {
    // Gán status là PENDING và gọi lại hàm findAll
    return this.findAll({
      ...dto,
      status: BlogStatusEnum.PENDING,
    });
  }

  /**
   * Lấy chi tiết bài viết theo ID
   */
  async findOne(id: number): Promise<BlogResponseDto> {
    try {
      // Sử dụng SqlHelper để lấy thông tin bài viết và tác giả
      const blogs = await this.sqlHelper.select(
        'blogs',
        [
          'blogs.id',
          'blogs.title',
          'blogs.content',
          'blogs.point',
          'blogs.view_count as "viewCount"',
          'blogs.thumbnail_url as "thumbnailUrl"',
          'blogs.tags',
          'blogs.created_at as "createdAt"',
          'blogs.updated_at as "updatedAt"',
          'blogs.user_id as "userId"',
          'blogs.author_type as "authorType"',
          'blogs.employee_moderator as "employeeModerator"',
          'blogs.employee_id as "employeeId"',
          'blogs.status',
          'blogs.enable',
          'blogs.like',
          'users.id as "author.id"',
          'users.full_name as "author.name"',
          'users.avatar as "author.avatar"',
        ],
        [{ condition: 'blogs.id = :id', params: { id } }],
        [
          {
            type: 'LEFT',
            table: 'users',
            alias: 'users',
            condition: 'blogs.user_id = users.id',
          },
        ],
        [],
        [],
        undefined,
        undefined,
        { raw: true },
        BlogResponseDto,
      );

      if (!blogs || blogs.length === 0) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      // Transform the data to match the expected response format
      const blog = blogs[0];

      // Format content URL if needed
      let content = blog.content;
      if (content && !content.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        content = `${cdnUrl}/${content}`;
      }

      // Format thumbnail URL if needed
      let thumbnailUrl = blog.thumbnailUrl;
      if (thumbnailUrl && !thumbnailUrl.startsWith('http')) {
        const cdnUrl = process.env.CDN_URL || 'https://cdn.redai.vn';
        thumbnailUrl = `${cdnUrl}/${thumbnailUrl}`;
      }

      const transformedBlog = {
        ...blog,
        content,
        thumbnailUrl,
        author: {
          id:
            blog.authorType === AuthorTypeEnum.USER
              ? (blog.userId ?? null)
              : (blog.employeeId ?? null),
          name: blog.author?.name || 'System',
          type: blog.authorType || AuthorTypeEnum.SYSTEM,
          avatar:
            blog.author?.avatar ||
            'https://cdn.example.com/avatars/default.jpg',
        },
      };

      return transformedBlog;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Failed to fetch blog with ID ${id}`);
    }
  }

  /**
   * Tạo bài viết mới (Bài viết hệ thống)
   */
  async create(dto: CreateBlogDto): Promise<BlogResponseDto> {
    try {
      const now = Date.now();

      // Tạo các key S3 cho content và thumbnail
      const contentKey = generateS3Key({
        baseFolder: 'blogs',
        categoryFolder: CategoryFolderEnum.DOCUMENT,
        prefix: 'content',
      });

      const thumbnailKey = generateS3Key({
        baseFolder: 'blogs',
        categoryFolder: CategoryFolderEnum.IMAGE,
        prefix: 'thumbnail',
      });

      // Sử dụng SqlHelper để tạo bài viết mới
      const savedBlog = await this.sqlHelper.insert<{ id: number }>(
        'blogs',
        {
          title: dto.title,
          description: dto.description,
          content: contentKey, // Lưu key S3 cho content
          thumbnail_url: thumbnailKey, // Lưu key S3 cho thumbnail
          point: dto.point,
          tags: JSON.stringify(dto.tags),
          author_type: dto.authorType || AuthorTypeEnum.SYSTEM,
          status: dto.status || BlogStatusEnum.APPROVED,
          enable: true,
          created_at: now,
          updated_at: now,
          view_count: 0,
          like: 0,
        },
        ['id'],
      );

      if (!savedBlog || !savedBlog.id) {
        throw new NotFoundException('Failed to create blog');
      }

      // Tạo URL tải lên cho nội dung và thumbnail
      // Chuyển đổi contentMediaType từ string sang MediaType
      let contentMediaType: MediaType;
      try {
        if (dto.contentMediaType === 'text/html') {
          contentMediaType = FileTypeEnum.HTML;
        } else {
          contentMediaType = FileType.getMimeType(dto.contentMediaType);
        }
      } catch (error) {
        contentMediaType = FileTypeEnum.HTML; // Mặc định nếu không tìm thấy
      }

      const contentUploadUrl = await this.s3Service.createPresignedWithID(
        contentKey,
        TimeIntervalEnum.ONE_HOUR,
        contentMediaType,
        FileSizeEnum.FIVE_MB,
      );

      // Chuyển đổi thumbnailMediaType từ string sang MediaType
      let thumbnailMediaType: MediaType;
      try {
        thumbnailMediaType = ImageType.getType(dto.thumbnailMediaType);
      } catch (error) {
        thumbnailMediaType = ImageTypeEnum.JPEG; // Mặc định nếu không tìm thấy
      }

      const thumbnailUploadUrl = await this.s3Service.createPresignedWithID(
        thumbnailKey,
        TimeIntervalEnum.ONE_HOUR,
        thumbnailMediaType,
        FileSizeEnum.TWO_MB,
      );

      // Lấy thông tin bài viết vừa tạo
      const blogs = await this.sqlHelper.select(
        'blogs',
        [
          'id',
          'title',
          'description',
          'content',
          'thumbnail_url as "thumbnailUrl"',
          'point',
          'tags',
          'author_type',
          'status',
          'created_at',
          'updated_at',
        ],
        [{ condition: 'id = :id', params: { id: savedBlog.id } }],
      );

      if (!blogs || blogs.length === 0) {
        throw new NotFoundException('Failed to retrieve created blog');
      }

      // Tạo response
      const response = new BlogResponseDto();
      Object.assign(response, blogs[0]);
      response.contentUploadUrl = contentUploadUrl;
      response.thumbnailUploadUrl = thumbnailUploadUrl;

      return response;
    } catch (error) {
      console.error('Error creating blog:', error);
      throw new NotFoundException(
        'Failed to create blog. Please try again later.',
      );
    }
  }

  /**
   * Cập nhật media cho bài viết
   */
  async updateMedia(
    id: number,
    dto: UpdateBlogMediaDto,
  ): Promise<{ upload_url: string }> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogExists = await this.sqlHelper.exists('blogs', [
        { condition: 'id = :id', params: { id } },
      ]);

      if (!blogExists) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      // Xác định loại thư mục dựa trên loại media
      const categoryFolder =
        dto.mediaType === 'content'
          ? CategoryFolderEnum.DOCUMENT
          : CategoryFolderEnum.IMAGE;

      // Tạo S3 key sử dụng utility function
      const mediaKey = generateS3Key({
        baseFolder: 'blogs',
        prefix: `${dto.mediaType}`,
        categoryFolder: categoryFolder,
      });

      // Cập nhật key vào cơ sở dữ liệu
      if (dto.mediaType === 'content') {
        await this.sqlHelper.update(
          'blogs',
          [{ column: 'content', value: mediaKey }],
          [{ condition: 'id = :id', params: { id } }],
        );
      } else if (dto.mediaType === 'thumbnail') {
        await this.sqlHelper.update(
          'blogs',
          [{ column: 'thumbnail_url', value: mediaKey }],
          [{ condition: 'id = :id', params: { id } }],
        );
      }

      // Chuyển đổi mediaContentType từ string sang MediaType
      let mediaContentType: MediaType;
      try {
        if (dto.mediaType === 'content' && dto.mediaContentType === 'text/html') {
          mediaContentType = FileTypeEnum.HTML;
        } else if (dto.mediaType === 'thumbnail') {
          mediaContentType = ImageType.getType(dto.mediaContentType);
        } else {
          mediaContentType = FileType.getMimeType(dto.mediaContentType);
        }
      } catch (error) {
        // Mặc định nếu không tìm thấy
        mediaContentType = dto.mediaType === 'content' ? FileTypeEnum.HTML : ImageTypeEnum.JPEG;
      }

      // Tạo URL tải lên
      const uploadUrl = await this.s3Service.createPresignedWithID(
        mediaKey,
        TimeIntervalEnum.ONE_HOUR,
        mediaContentType,
        dto.mediaType === 'content' ? FileSizeEnum.FIVE_MB : FileSizeEnum.TWO_MB,
      );

      // Trả về chỉ URL tải lên
      return {
        upload_url: uploadUrl,
      };
    } catch (error) {
      console.error('Error updating media:', error);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(
        `Failed to update media for blog with ID ${id}`,
      );
    }
  }

  /**
   * Xóa bài viết (soft delete)
   */
  async delete(id: number): Promise<void> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogExists = await this.sqlHelper.exists('blogs', [
        { condition: 'id = :id', params: { id } },
        { condition: 'enable = :enable', params: { enable: true } },
      ]);

      if (!blogExists) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      // Xóa mềm bài viết bằng cách đặt enable = false
      const now = Date.now();
      await this.sqlHelper.update(
        'blogs',
        [
          { column: 'enable', value: false },
          { column: 'updated_at', value: now },
        ],
        [{ condition: 'id = :id', params: { id } }],
      );
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Failed to delete blog with ID ${id}`);
    }
  }

  /**
   * Phê duyệt/Từ chối bài viết
   */
  async moderate(
    id: number,
    dto: ModerateBlogDto,
  ): Promise<{ id: number; status: string; employeeModerator: number | null }> {
    try {
      // Kiểm tra bài viết có tồn tại không
      const blogExists = await this.sqlHelper.exists('blogs', [
        { condition: 'id = :id', params: { id } },
      ]);

      if (!blogExists) {
        throw new NotFoundException(`Blog with ID ${id} not found`);
      }

      const now = Date.now();
      // Remove hardcoded employee ID to avoid foreign key constraint issues
      // const employeeId = 5; // Giả sử ID của nhân viên đang đăng nhập là 5

      // Cập nhật trạng thái và người kiểm duyệt
      await this.sqlHelper.update(
        'blogs',
        [
          { column: 'status', value: dto.status },
          // Remove employee_moderator to avoid foreign key constraint issues
          // { column: 'employee_moderator', value: employeeId },
          { column: 'updated_at', value: now },
        ],
        [{ condition: 'id = :id', params: { id } }],
      );

      return {
        id: id,
        status: dto.status,
        // Return null for employeeModerator
        employeeModerator: null,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new NotFoundException(`Failed to moderate blog with ID ${id}`);
    }
  }

  /**
   * Lấy phần mở rộng từ loại nội dung
   */
  private getExtensionFromContentType(contentType: string): string {
    switch (contentType) {
      case 'text/html':
        return 'html';
      case 'image/jpeg':
        return 'jpg';
      case 'image/png':
        return 'png';
      default:
        return 'txt';
    }
  }
}
