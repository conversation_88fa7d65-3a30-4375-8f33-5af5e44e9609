import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, Min } from 'class-validator';
import { Type } from 'class-transformer';

export class GetPurchaseListDto {
  @ApiProperty({
    description: 'Trang hiện tại',
    default: 1,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  page: number = 1;

  @ApiProperty({
    description: 'Số lượng bản ghi trên mỗi trang',
    default: 10,
    required: false,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  @Min(1)
  limit: number = 10;

  @ApiProperty({
    description: 'Timestamp bắt đầu',
    required: false,
    example: 1625097600000,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  start_date?: number;

  @ApiProperty({
    description: 'Timestamp kết thúc',
    required: false,
    example: 1632474086123,
  })
  @IsOptional()
  @Type(() => Number)
  @IsNumber()
  end_date?: number;
}
