import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class BlogPurchaseStatisticsDto {
  @ApiProperty({
    description: 'Timestamp bắt đầu',
    example: 1632474086123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  start_date?: number;

  @ApiProperty({
    description: 'Timestamp kết thúc',
    example: 1632574086123,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  end_date?: number;
}

export class BlogPurchaseStatisticsResponseDto {
  @ApiProperty({
    description: 'Tổng số giao dịch mua',
    example: 120,
  })
  total_purchases: number;

  @ApiProperty({
    description: 'Tổng số điểm đã sử dụng',
    example: 12500,
  })
  total_points: number;

  @ApiProperty({
    description: 'Tổng phí nền tảng',
    example: 625,
  })
  total_platform_fee: number;

  @ApiProperty({
    description: 'Tổng số điểm người bán nhận được',
    example: 11875,
  })
  total_to_sellers: number;
}