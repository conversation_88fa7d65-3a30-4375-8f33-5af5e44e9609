import { Injectable } from '@nestjs/common';
import { DataSource, Repository, SelectQueryBuilder } from 'typeorm';
import { AdminTask } from '../entities/admin-task.entity';

/**
 * Repository cho entity AdminTask
 * Xử lý các thao tác truy vấn dữ liệu liên quan đến bảng admin_tasks
 */
@Injectable()
export class AdminTaskRepository extends Repository<AdminTask> {
  /**
   * Constructor
   * @param dataSource Nguồn dữ liệu TypeORM
   */
  constructor(private dataSource: DataSource) {
    super(AdminTask, dataSource.createEntityManager());
  }

  /**
   * Tạo query builder c<PERSON> bản cho AdminTask
   * @returns SelectQueryBuilder cho AdminTask
   */
  private createBaseQuery(): SelectQueryBuilder<AdminTask> {
    return this.createQueryBuilder('task');
  }
}
