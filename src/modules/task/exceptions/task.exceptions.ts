import { ErrorCode } from '@/common';
import { HttpStatus } from '@nestjs/common';

/**
 * Mã lỗi cho các thao tác liên quan đến task
 * Phạm vi mã lỗi: 10000 - 10099
 */
export const TASK_ERROR_CODES = {
  // Lỗi chung
  TASK_NOT_FOUND: new ErrorCode(10000, 'Không tìm thấy nhiệm vụ', HttpStatus.NOT_FOUND),
  TASK_CREATION_FAILED: new ErrorCode(10001, 'Tạo nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  TASK_UPDATE_FAILED: new ErrorCode(10002, 'Cập nhật nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  TASK_DELETE_FAILED: new ErrorCode(10003, '<PERSON><PERSON><PERSON> nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  TASK_FETCH_FAILED: new ErrorCode(10004, '<PERSON><PERSON><PERSON> thông tin nhiệm vụ thất bại', HttpStatus.INTERNAL_SERVER_ERROR),
  
  // Lỗi xác thực và phân quyền
  TASK_UNAUTHORIZED: new ErrorCode(10010, 'Không có quyền truy cập nhiệm vụ này', HttpStatus.FORBIDDEN),
  TASK_INVALID_OWNER: new ErrorCode(10011, 'Bạn không phải là chủ sở hữu của nhiệm vụ này', HttpStatus.FORBIDDEN),
  
  // Lỗi trạng thái
  TASK_INVALID_STATUS: new ErrorCode(10020, 'Trạng thái nhiệm vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  TASK_ALREADY_COMPLETED: new ErrorCode(10021, 'Nhiệm vụ đã hoàn thành, không thể thay đổi', HttpStatus.BAD_REQUEST),
  TASK_ALREADY_CANCELLED: new ErrorCode(10022, 'Nhiệm vụ đã bị hủy, không thể thay đổi', HttpStatus.BAD_REQUEST),
  
  // Lỗi dữ liệu
  TASK_INVALID_DATA: new ErrorCode(10030, 'Dữ liệu nhiệm vụ không hợp lệ', HttpStatus.BAD_REQUEST),
  TASK_MISSING_AGENT: new ErrorCode(10031, 'Thiếu thông tin agent cho nhiệm vụ', HttpStatus.BAD_REQUEST),
  TASK_AGENT_NOT_FOUND: new ErrorCode(10032, 'Không tìm thấy agent cho nhiệm vụ', HttpStatus.NOT_FOUND),
  TASK_NAME_REQUIRED: new ErrorCode(10033, 'Tên nhiệm vụ là bắt buộc', HttpStatus.BAD_REQUEST),
  TASK_NAME_TOO_LONG: new ErrorCode(10034, 'Tên nhiệm vụ quá dài (tối đa 255 ký tự)', HttpStatus.BAD_REQUEST),
  
  // Lỗi giới hạn
  TASK_LIMIT_EXCEEDED: new ErrorCode(10040, 'Đã vượt quá giới hạn số lượng nhiệm vụ', HttpStatus.BAD_REQUEST),
};
