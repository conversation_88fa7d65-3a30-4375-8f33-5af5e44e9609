import { Injectable, Logger } from '@nestjs/common';
import { AdminStepConnectionRepository } from '../../repositories';

/**
 * Service xử lý logic liên quan đến kết nối gi<PERSON>a các bước trong task của admin
 */
@Injectable()
export class AdminConnectionService {
  /**
   * <PERSON>gger cho AdminConnectionService
   */
  private readonly logger = new Logger(AdminConnectionService.name);

  /**
   * Constructor
   * @param adminStepConnectionRepository Repository xử lý dữ liệu kết nối gi<PERSON>a các bước trong task của admin
   */
  constructor(
    private readonly adminStepConnectionRepository: AdminStepConnectionRepository,
  ) {}
}
