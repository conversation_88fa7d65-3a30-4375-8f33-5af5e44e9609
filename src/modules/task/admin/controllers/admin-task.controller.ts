import { JwtEmployeeGuard } from '@/modules/auth/guards';
import { Controller, UseGuards } from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { AdminTaskService } from '../services';
import { SWAGGER_API_TAGS } from '@/common/swagger';

/**
 * Controller xử lý các endpoint liên quan đến task của admin
 */
@ApiTags(SWAGGER_API_TAGS.ADMIN_TASK)
@Controller('admin/tasks')
@UseGuards(JwtEmployeeGuard)
@ApiBearerAuth('JWT-auth')
export class AdminTaskController {
  /**
   * Constructor
   * @param adminTaskService Service xử lý logic liên quan đến task của admin
   */
  constructor(private readonly adminTaskService: AdminTaskService) {}
}
