# Tà<PERSON> liệu luồng Task cho User

## <PERSON><PERSON><PERSON> l<PERSON>
1. [<PERSON><PERSON><PERSON><PERSON> thiệu](#giới-thiệu)
2. [Qu<PERSON>n lý Task](#quản-lý-task)
   - [Xem danh sách Task](#xem-danh-sách-task)
   - [Tạo Task mới](#tạo-task-mới)
   - [Xem chi tiết Task](#xem-chi-tiết-task)
   - [Cập nhật Task](#cập-nhật-task)
   - [Xóa Task](#xóa-task)
   - [Kích hoạt/Vô hiệu hóa Task](#kích-hoạtvô-hiệu-hóa-task)
3. [Quản lý Step](#quản-lý-step)
   - [Xem danh sách Step](#xem-danh-sách-step)
   - [Thêm Step mới](#thêm-step-mới)
   - [Cập nhật Step](#cập-nhật-step)
   - [<PERSON>ắ<PERSON> xếp thứ tự Step](#sắp-xếp-thứ-tự-step)
   - [Xóa Step](#xóa-step)
4. [Quản lý Connection](#quản-lý-connection)
   - [Xem danh sách Connection](#xem-danh-sách-connection)
   - [Tạo Connection mới](#tạo-connection-mới)
   - [Cập nhật Connection](#cập-nhật-connection)
   - [Xóa Connection](#xóa-connection)
5. [Thực thi Task](#thực-thi-task)
   - [Chạy Task](#chạy-task)
   - [Xem lịch sử Execution](#xem-lịch-sử-execution)
   - [Xem chi tiết Execution](#xem-chi-tiết-execution)
   - [Hủy Execution đang chạy](#hủy-execution-đang-chạy)
   
## Giới thiệu

Tài liệu này mô tả chi tiết các luồng xử lý liên quan đến quản lý Task trong hệ thống dành cho User. User có quyền quản lý các Task của mình, bao gồm tạo, cập nhật, xóa và thực thi Task.

## Quản lý Task

### Xem danh sách Task

**Endpoint:** GET /user/tasks

**Mô tả:** Lấy danh sách các Task của người dùng hiện tại với khả năng lọc và phân trang.

**Luồng xử lý:**
1. User truy cập trang quản lý Task
2. Hệ thống gửi request GET đến endpoint /user/tasks với các tham số lọc và phân trang
3. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Truy vấn dữ liệu từ bảng user_tasks với điều kiện userId = ID của người dùng hiện tại
   - Áp dụng phân trang và sắp xếp
4. Backend trả về danh sách Task kèm thông tin phân trang
5. Frontend hiển thị danh sách Task cho User

**Tham số:**
- page: Số trang (mặc định: 1)
- limit: Số lượng Task trên mỗi trang (mặc định: 10)
- sortBy: Trường sắp xếp (mặc định: createdAt)
- sortOrder: Thứ tự sắp xếp (ASC/DESC, mặc định: DESC)
- agentId: Lọc theo Agent ID
- status: Lọc theo trạng thái (ACTIVE, SUSPENDED, COMPLETED, CANCELLED)
- search: Tìm kiếm theo tên Task

### Tạo Task mới

**Endpoint:** POST /user/tasks

**Mô tả:** Tạo một Task mới cho người dùng hiện tại.

**Luồng xử lý:**
1. User truy cập trang tạo Task mới
2. User nhập thông tin Task:
   - Tên Task (bắt buộc)
   - Mô tả Task (tùy chọn)
   - Chọn Agent (bắt buộc)
3. User nhấn nút "Tạo Task"
4. Frontend gửi request POST đến endpoint /user/tasks với dữ liệu Task
5. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Validate dữ liệu đầu vào
   - Kiểm tra giới hạn số lượng Task của người dùng
   - Tạo bản ghi mới trong bảng user_tasks với userId = ID của người dùng hiện tại
6. Backend trả về thông tin Task vừa tạo
7. Frontend chuyển hướng đến trang chi tiết Task

**Dữ liệu đầu vào:**
```json
{
  "taskName": "Tên Task",
  "taskDescription": "Mô tả chi tiết về Task",
  "agentId": "uuid-của-agent"
}
```

### Xem chi tiết Task

**Endpoint:** GET /user/tasks/{taskId}

**Mô tả:** Xem thông tin chi tiết của một Task cụ thể.

**Luồng xử lý:**
1. User nhấp vào một Task trong danh sách
2. Frontend gửi request GET đến endpoint /user/tasks/{taskId}
3. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Truy vấn thông tin Task từ bảng user_tasks
   - Truy vấn danh sách Step của Task từ bảng user_steps
   - Truy vấn danh sách Connection của Task từ bảng user_step_connections
4. Backend trả về thông tin chi tiết Task
5. Frontend hiển thị thông tin chi tiết Task, danh sách Step và Connection

### Cập nhật Task

**Endpoint:** PUT /user/tasks/{taskId}

**Mô tả:** Cập nhật thông tin của một Task.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Chỉnh sửa"
3. User cập nhật thông tin Task
4. User nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /user/tasks/{taskId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng user_tasks
7. Backend trả về thông tin Task đã cập nhật
8. Frontend hiển thị thông tin Task đã cập nhật

**Dữ liệu đầu vào:**
```json
{
  "taskName": "Tên Task đã cập nhật",
  "taskDescription": "Mô tả chi tiết đã cập nhật",
  "status": "ACTIVE"
}
```

### Xóa Task

**Endpoint:** DELETE /user/tasks/{taskId}

**Mô tả:** Xóa một Task khỏi hệ thống (soft delete).

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. User xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /user/tasks/{taskId}
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Cập nhật trường deletedAt trong bảng user_tasks
7. Backend trả về thông báo xóa thành công
8. Frontend chuyển hướng đến trang danh sách Task

### Kích hoạt/Vô hiệu hóa Task

**Endpoint:** PUT /user/tasks/{taskId}/active

**Mô tả:** Kích hoạt hoặc vô hiệu hóa một Task.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Kích hoạt" hoặc "Vô hiệu hóa"
3. Frontend gửi request PUT đến endpoint /user/tasks/{taskId}/active với trạng thái mới
4. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Cập nhật trường active trong bảng user_tasks
5. Backend trả về thông tin Task đã cập nhật
6. Frontend cập nhật hiển thị trạng thái Task

**Dữ liệu đầu vào:**
```json
{
  "active": true
}
```

## Quản lý Step

### Xem danh sách Step

**Endpoint:** GET /user/tasks/{taskId}/steps

**Mô tả:** Lấy danh sách các Step của một Task cụ thể.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. Frontend gửi request GET đến endpoint /user/tasks/{taskId}/steps
3. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Truy vấn danh sách Step từ bảng user_steps với điều kiện taskId
   - Sắp xếp theo orderIndex
4. Backend trả về danh sách Step
5. Frontend hiển thị danh sách Step theo thứ tự

### Thêm Step mới

**Endpoint:** POST /user/tasks/{taskId}/steps

**Mô tả:** Thêm một Step mới vào Task.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Thêm Step"
3. User nhập thông tin Step:
   - Tên Step (bắt buộc)
   - Mô tả Step (tùy chọn)
   - Loại Step (bắt buộc): PROMPT, TRIGGER, ACTION, MEDIA
   - Cấu hình Step (tùy thuộc vào loại Step)
4. User nhấn nút "Lưu"
5. Frontend gửi request POST đến endpoint /user/tasks/{taskId}/steps với dữ liệu Step
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Validate dữ liệu đầu vào
   - Xác định orderIndex mới (lớn nhất hiện tại + 1)
   - Tạo bản ghi mới trong bảng user_steps
7. Backend trả về thông tin Step vừa tạo
8. Frontend cập nhật danh sách Step

**Dữ liệu đầu vào:**
```json
{
  "stepName": "Tên Step",
  "stepDescription": "Mô tả chi tiết về Step",
  "stepType": "PROMPT",
  "stepConfig": {
    "content": "Nội dung prompt",
    "variables": ["variable1", "variable2"],
    "options": {
      "temperature": 0.7
    }
  }
}
```

### Cập nhật Step

**Endpoint:** PUT /user/tasks/{taskId}/steps/{stepId}

**Mô tả:** Cập nhật thông tin của một Step.

**Luồng xử lý:**
1. User nhấp vào một Step trong danh sách
2. User nhấn nút "Chỉnh sửa"
3. User cập nhật thông tin Step
4. User nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /user/tasks/{taskId}/steps/{stepId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng user_steps
7. Backend trả về thông tin Step đã cập nhật
8. Frontend cập nhật hiển thị Step

**Dữ liệu đầu vào:**
```json
{
  "stepName": "Tên Step đã cập nhật",
  "stepDescription": "Mô tả chi tiết đã cập nhật",
  "stepConfig": {
    "content": "Nội dung prompt đã cập nhật",
    "variables": ["variable1", "variable2", "variable3"],
    "options": {
      "temperature": 0.8
    }
  }
}
```

### Sắp xếp thứ tự Step

**Endpoint:** PUT /user/tasks/{taskId}/steps/reorder

**Mô tả:** Sắp xếp lại thứ tự các Step trong Task.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User kéo thả các Step để sắp xếp lại thứ tự
3. User nhấn nút "Lưu thứ tự"
4. Frontend gửi request PUT đến endpoint /user/tasks/{taskId}/steps/reorder với danh sách ID Step theo thứ tự mới
5. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Cập nhật trường orderIndex cho từng Step trong bảng user_steps
6. Backend trả về danh sách Step đã cập nhật thứ tự
7. Frontend cập nhật hiển thị danh sách Step

**Dữ liệu đầu vào:**
```json
{
  "stepIds": ["step-id-1", "step-id-2", "step-id-3", "step-id-4"]
}
```

### Xóa Step

**Endpoint:** DELETE /user/tasks/{taskId}/steps/{stepId}

**Mô tả:** Xóa một Step khỏi Task.

**Luồng xử lý:**
1. User nhấp vào một Step trong danh sách
2. User nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. User xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /user/tasks/{taskId}/steps/{stepId}
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Xóa bản ghi từ bảng user_steps
   - Xóa các Connection liên quan đến Step này
   - Cập nhật lại orderIndex của các Step còn lại
7. Backend trả về thông báo xóa thành công
8. Frontend cập nhật danh sách Step

## Quản lý Connection

### Xem danh sách Connection

**Endpoint:** GET /user/tasks/{taskId}/connections

**Mô tả:** Lấy danh sách các Connection của một Task cụ thể.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. Frontend gửi request GET đến endpoint /user/tasks/{taskId}/connections
3. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Truy vấn danh sách Connection từ bảng user_step_connections với điều kiện taskId
4. Backend trả về danh sách Connection
5. Frontend hiển thị danh sách Connection hoặc biểu diễn dưới dạng đồ thị

### Tạo Connection mới

**Endpoint:** POST /user/tasks/{taskId}/connections

**Mô tả:** Tạo một Connection mới giữa hai Step.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Thêm Connection" hoặc kéo thả để tạo kết nối giữa hai Step
3. User cấu hình Connection:
   - Chọn Step nguồn (fromStepId)
   - Chọn Step đích (toStepId)
   - Nhập tên trường output từ Step nguồn (outputField)
   - Nhập tên trường input của Step đích (inputField)
4. User nhấn nút "Lưu"
5. Frontend gửi request POST đến endpoint /user/tasks/{taskId}/connections với dữ liệu Connection
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Validate dữ liệu đầu vào
   - Kiểm tra tính hợp lệ của Connection (không tạo vòng lặp, không trùng lặp)
   - Tạo bản ghi mới trong bảng user_step_connections
7. Backend trả về thông tin Connection vừa tạo
8. Frontend cập nhật hiển thị Connection

**Dữ liệu đầu vào:**
```json
{
  "fromStepId": "step-id-1",
  "toStepId": "step-id-2",
  "outputField": "result",
  "inputField": "prompt_variable"
}
```

### Cập nhật Connection

**Endpoint:** PUT /user/tasks/{taskId}/connections/{connectionId}

**Mô tả:** Cập nhật thông tin của một Connection.

**Luồng xử lý:**
1. User nhấp vào một Connection trong danh sách hoặc trên đồ thị
2. User nhấn nút "Chỉnh sửa"
3. User cập nhật thông tin Connection
4. User nhấn nút "Lưu"
5. Frontend gửi request PUT đến endpoint /user/tasks/{taskId}/connections/{connectionId} với dữ liệu đã cập nhật
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Validate dữ liệu đầu vào
   - Cập nhật bản ghi trong bảng user_step_connections
7. Backend trả về thông tin Connection đã cập nhật
8. Frontend cập nhật hiển thị Connection

**Dữ liệu đầu vào:**
```json
{
  "outputField": "updated_result",
  "inputField": "updated_prompt_variable"
}
```

### Xóa Connection

**Endpoint:** DELETE /user/tasks/{taskId}/connections/{connectionId}

**Mô tả:** Xóa một Connection khỏi Task.

**Luồng xử lý:**
1. User nhấp vào một Connection trong danh sách hoặc trên đồ thị
2. User nhấn nút "Xóa"
3. Hệ thống hiển thị hộp thoại xác nhận
4. User xác nhận xóa
5. Frontend gửi request DELETE đến endpoint /user/tasks/{taskId}/connections/{connectionId}
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Xóa bản ghi từ bảng user_step_connections
7. Backend trả về thông báo xóa thành công
8. Frontend cập nhật hiển thị Connection

## Thực thi Task

### Chạy Task

**Endpoint:** POST /user/tasks/{taskId}/execute

**Mô tả:** Thực thi một Task.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn nút "Chạy Task"
3. User nhập dữ liệu đầu vào (nếu cần)
4. User nhấn nút "Bắt đầu"
5. Frontend gửi request POST đến endpoint /user/tasks/{taskId}/execute với dữ liệu đầu vào
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Kiểm tra trạng thái Task (phải là ACTIVE)
   - Kiểm tra giới hạn tần suất thực thi
   - Tạo bản ghi mới trong bảng user_task_executions
   - Bắt đầu thực thi Task (có thể sử dụng worker/queue)
7. Backend trả về thông tin Execution vừa tạo
8. Frontend chuyển hướng đến trang theo dõi Execution

**Dữ liệu đầu vào:**
```json
{
  "initialInput": {
    "variable1": "value1",
    "variable2": "value2"
  }
}
```

### Xem lịch sử Execution

**Endpoint:** GET /user/tasks/{taskId}/executions

**Mô tả:** Lấy danh sách các lần thực thi (Execution) của một Task cụ thể.

**Luồng xử lý:**
1. User truy cập trang chi tiết Task
2. User nhấn tab "Lịch sử thực thi"
3. Frontend gửi request GET đến endpoint /user/tasks/{taskId}/executions với các tham số lọc và phân trang
4. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Truy vấn danh sách Execution từ bảng user_task_executions với điều kiện taskId
   - Áp dụng phân trang và sắp xếp
5. Backend trả về danh sách Execution kèm thông tin phân trang
6. Frontend hiển thị danh sách Execution

**Tham số:**
- page: Số trang (mặc định: 1)
- limit: Số lượng Execution trên mỗi trang (mặc định: 10)
- sortBy: Trường sắp xếp (mặc định: startTime)
- sortOrder: Thứ tự sắp xếp (ASC/DESC, mặc định: DESC)
- status: Lọc theo trạng thái (RUNNING, SUCCESS, FAILED, CANCELLED, PAUSED)

### Xem chi tiết Execution

**Endpoint:** GET /user/tasks/{taskId}/executions/{executionId}

**Mô tả:** Xem thông tin chi tiết của một lần thực thi cụ thể.

**Luồng xử lý:**
1. User nhấp vào một Execution trong danh sách
2. Frontend gửi request GET đến endpoint /user/tasks/{taskId}/executions/{executionId}
3. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Truy vấn thông tin Execution từ bảng user_task_executions
4. Backend trả về thông tin chi tiết Execution, bao gồm executionDetails
5. Frontend hiển thị thông tin chi tiết Execution, bao gồm:
   - Thời gian bắt đầu và kết thúc
   - Trạng thái tổng thể
   - Chi tiết thực thi từng Step
   - Dữ liệu đầu vào/đầu ra
   - Thông tin lỗi (nếu có)

### Hủy Execution đang chạy

**Endpoint:** POST /user/tasks/{taskId}/executions/{executionId}/cancel

**Mô tả:** Hủy một Execution đang chạy.

**Luồng xử lý:**
1. User truy cập trang chi tiết Execution
2. User nhấn nút "Hủy"
3. Hệ thống hiển thị hộp thoại xác nhận
4. User xác nhận hủy
5. Frontend gửi request POST đến endpoint /user/tasks/{taskId}/executions/{executionId}/cancel
6. Backend xử lý request:
   - Xác thực người dùng thông qua JWT token
   - Kiểm tra quyền sở hữu Task (userId = ID của người dùng hiện tại)
   - Kiểm tra trạng thái Execution (phải là RUNNING hoặc PAUSED)
   - Cập nhật trạng thái Execution thành CANCELLED
   - Dừng quá trình thực thi (nếu đang chạy)
7. Backend trả về thông tin Execution đã cập nhật
8. Frontend cập nhật hiển thị trạng thái Execution
