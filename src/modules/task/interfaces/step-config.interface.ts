// step-configs.ts
// <PERSON><PERSON><PERSON> nghĩa các interface TypeScript cho cấu hình step_config trong bảng user_steps
// Hỗ trợ các loại step: prompt, google_sheet, google_doc, google_calendar, email, facebook_page, gen_image, gen_video, agent

// Định nghĩa các loại step tương ứng với step_type trong bảng user_steps
export enum StepType {
  PROMPT = 'prompt',
  GOOGLE_SHEET = 'google_sheet',
  GOOGLE_DOC = 'google_doc',
  GOOGLE_CALENDAR = 'google_calendar',
  EMAIL = 'email',
  FACEBOOK_PAGE = 'facebook_page',
  GEN_IMAGE = 'gen_image', 
  GEN_VIDEO = 'gen_video',
  AGENT = 'agent'
}

// Interface cơ bản cho tất cả các step_config
interface BaseStepConfig {
  stepType: StepType;
  credentialId?: string; // UUID tham chiếu đến google_auth_credentials, facebook_admin_credentials, hoặc client_credentials
  metadata?: Record<string, any>; // Thông tin bổ sung (tùy chọn)
}

// Interface cho step loại Prompt
// Yêu cầu người dùng nhập dữ liệu
interface PromptStepConfig extends BaseStepConfig {
  stepType: StepType.PROMPT;
  promptText: string; // Văn bản hiển thị để hướng dẫn người dùng
  inputType: 'text' | 'number' | 'date' | 'boolean'; // Kiểu dữ liệu đầu vào
  required: boolean; // Có bắt buộc nhập không
  defaultValue?: string | number | boolean; // Giá trị mặc định (tùy chọn)
}

// Interface cho step loại Google Sheet
// Tương tác với Google Sheets (đọc/ghi dữ liệu)
interface GoogleSheetStepConfig extends BaseStepConfig {
  stepType: StepType.GOOGLE_SHEET;
  sheetId: string; // ID của Google Sheet
  range: string; // Vùng dữ liệu (ví dụ: "Sheet1!A1:B10")
  action: 'read' | 'write'; // Hành động thực hiện
  data?: Record<string, any>[]; // Dữ liệu để ghi (nếu action là write)
  credentialId: string; // Bắt buộc, tham chiếu đến google_auth_credentials
}

// Interface cho step loại Google Doc
// Tương tác với Google Docs (tạo, thêm, thay thế nội dung)
interface GoogleDocStepConfig extends BaseStepConfig {
  stepType: StepType.GOOGLE_DOC;
  docId?: string; // ID của Google Doc (tùy chọn nếu action là create)
  action: 'create' | 'append' | 'replace'; // Hành động thực hiện
  content: string; // Nội dung cần thêm/thay thế
  credentialId: string; // Bắt buộc, tham chiếu đến google_auth_credentials
}

// Interface cho step loại Google Calendar
// Tương tác với Google Calendar (tạo, cập nhật, xóa sự kiện)
interface GoogleCalendarStepConfig extends BaseStepConfig {
  stepType: StepType.GOOGLE_CALENDAR;
  calendarId: string; // ID của lịch (ví dụ: "primary")
  action: 'create_event' | 'update_event' | 'delete_event'; // Hành động thực hiện
  eventDetails: {
    eventId?: string; // Chỉ cần khi update hoặc delete
    summary: string; // Tiêu đề sự kiện
    description?: string; // Mô tả sự kiện
    start: { dateTime: string; timeZone?: string }; // Thời gian bắt đầu
    end: { dateTime: string; timeZone?: string }; // Thời gian kết thúc
    attendees?: { email: string }[]; // Danh sách người tham gia
  };
  credentialId: string; // Bắt buộc, tham chiếu đến google_auth_credentials
}

// Interface cho step loại Email
// Gửi email (tích hợp với Gmail hoặc SMTP)
interface EmailStepConfig extends BaseStepConfig {
  stepType: StepType.EMAIL;
  to: string[]; // Danh sách email người nhận
  subject: string; // Chủ đề email
  body: string; // Nội dung email
  credentialId?: string; // Tùy chọn, dùng cho Gmail (google_auth_credentials) hoặc SMTP (client_credentials)
}

// Interface cho step loại Facebook Page
// Tương tác với Facebook Page (đăng bài, bình luận, lấy insights)
interface FacebookPageStepConfig extends BaseStepConfig {
  stepType: StepType.FACEBOOK_PAGE;
  pageId: string; // ID của Fanpage
  action: 'post' | 'comment' | 'get_insights'; // Hành động thực hiện
  content: {
    message?: string; // Nội dung bài đăng hoặc bình luận
    link?: string; // Liên kết đính kèm (nếu có)
    commentId?: string; // ID của bình luận (khi action là comment)
    insightType?: string; // Loại insight (khi action là get_insights)
  };
  credentialId: string; // Bắt buộc, tham chiếu đến facebook_admin_credentials
}

// Interface cho step loại Gen Image
// Tạo ảnh từ mô tả văn bản (ví dụ: DALL·E, Stable Diffusion)
interface GenImageStepConfig extends BaseStepConfig {
  stepType: StepType.GEN_IMAGE;
  prompt: string; // Mô tả văn bản để tạo ảnh
  resolution: string; // Độ phân giải (ví dụ: "512x512")
  model?: string; // Mô hình AI (ví dụ: "dalle-2")
  credentialId?: string; // Tùy chọn, tham chiếu đến client_credentials
}

// Interface cho step loại Gen Video
// Tạo video từ mô tả văn bản hoặc tham số
interface GenVideoStepConfig extends BaseStepConfig {
  stepType: StepType.GEN_VIDEO;
  prompt: string; // Mô tả văn bản để tạo video
  duration: number; // Độ dài video (giây)
  resolution: string; // Độ phân giải (ví dụ: "1280x720")
  model?: string; // Mô hình AI (ví dụ: "runway")
  credentialId?: string; // Tùy chọn, tham chiếu đến client_credentials
}

// Interface cho step loại Agent
// Gọi một agent (AI hoặc script) để thực thi logic
interface AgentStepConfig extends BaseStepConfig {
  stepType: StepType.AGENT;
  agentId: string; // UUID tham chiếu đến bảng agents
  parameters: Record<string, any>; // Tham số tùy chỉnh cho agent
  credentialId?: string; // Tùy chọn, tham chiếu đến client_credentials
}

// Union type cho tất cả các loại step_config
type StepConfig =
  | PromptStepConfig
  | GoogleSheetStepConfig
  | GoogleDocStepConfig
  | GoogleCalendarStepConfig
  | EmailStepConfig
  | FacebookPageStepConfig
  | GenImageStepConfig
  | GenVideoStepConfig
  | AgentStepConfig;

// Interface đầy đủ cho một Step, ánh xạ bảng user_steps
interface Step {
  stepId: string; // UUID từ cột step_id
  taskId: string; // UUID từ cột task_id
  orderIndex: number; // Thứ tự bước từ cột order_index
  stepName: string; // Tên bước từ cột step_name
  stepDescription?: string; // Mô tả từ cột step_description
  stepType: StepType; // Loại bước từ cột step_type
  stepConfig: StepConfig; // Cấu hình từ cột step_config (JSONB)
  createdAt: number; // Unix epoch từ cột created_at
  updatedAt: number; // Unix epoch từ cột updated_at
}

// Export các type và interface để sử dụng trong ứng dụng
export {
  BaseStepConfig,
  PromptStepConfig,
  GoogleSheetStepConfig,
  GoogleDocStepConfig,
  GoogleCalendarStepConfig,
  EmailStepConfig,
  FacebookPageStepConfig,
  GenImageStepConfig,
  GenVideoStepConfig,
  AgentStepConfig,
  StepConfig,
  Step
};