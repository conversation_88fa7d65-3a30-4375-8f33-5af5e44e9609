import { ValidationPipe, ValidationError, BadRequestException } from '@nestjs/common';
import { AppException, ErrorCode } from '@common/exceptions';
import { TOOLS_ERROR_CODES } from '@modules/tools/exceptions';

/**
 * Mã lỗi validation chuyên biệt
 */
const VALIDATION_ERROR_CODES = {
  GENERAL_VALIDATION_ERROR: new ErrorCode(
    9001,
    'Dữ liệu đầu vào không hợp lệ',
    400, // HttpStatus.BAD_REQUEST
  ),
  FIELD_VALIDATION_ERROR: new ErrorCode(
    9002,
    'Một hoặc nhiều trường dữ liệu không hợp lệ',
    400, // HttpStatus.BAD_REQUEST
  ),
  NESTED_VALIDATION_ERROR: new ErrorCode(
    9003,
    'Dữ liệu nested object không hợp lệ',
    400, // HttpStatus.BAD_REQUEST
  ),
};

/**
 * Custom ValidationPipe để chuyển đổi lỗi validation thành AppException
 */
export class CustomValidationPipe extends ValidationPipe {
  constructor() {
    super({
      transform: true,
      whitelist: true,
      forbidNonWhitelisted: true,
      exceptionFactory: (errors: ValidationError[]) => {
        // Tìm lỗi liên quan đến toolName và pattern
        const toolNamePatternError = this.findToolNamePatternError(errors);

        // Nếu có lỗi toolName pattern, ném AppException với mã lỗi cụ thể
        if (toolNamePatternError) {
          throw new AppException(
            TOOLS_ERROR_CODES.TOOL_NAME_INVALID,
            'Tên tool chỉ được chứa a-z, A-Z, 0-9, hoặc dấu gạch dưới',
            { property: 'toolName', errors: toolNamePatternError }
          );
        }

        // Xử lý lỗi validation với thông tin chi tiết
        const detailedErrors = this.buildDetailedErrors(errors);
        const errorSummary = this.buildErrorSummary(detailedErrors);

        // Xác định loại lỗi validation
        const hasNestedErrors = detailedErrors.some(error => error.field.includes('.'));
        const errorCode = hasNestedErrors
          ? VALIDATION_ERROR_CODES.NESTED_VALIDATION_ERROR
          : detailedErrors.length > 1
            ? VALIDATION_ERROR_CODES.FIELD_VALIDATION_ERROR
            : VALIDATION_ERROR_CODES.GENERAL_VALIDATION_ERROR;

        console.log('=== VALIDATION ERROR DETAILS ===');
        console.log('Error Code:', errorCode.code);
        console.log('Error Summary:', errorSummary);
        console.log('Detailed Errors:', JSON.stringify(detailedErrors, null, 2));
        console.log('Has Nested Errors:', hasNestedErrors);
        console.log('Total Errors:', detailedErrors.length);
        console.log('================================');

        return new AppException(
          errorCode,
          errorSummary,
          {
            summary: errorSummary,
            details: detailedErrors,
            totalErrors: detailedErrors.length,
            hasNestedErrors,
            errorType: hasNestedErrors ? 'nested' : detailedErrors.length > 1 ? 'multiple' : 'single'
          }
        );
      },
    });
  }

  /**
   * Tìm lỗi liên quan đến toolName và pattern
   * @param errors Danh sách lỗi validation
   * @returns Lỗi toolName pattern nếu tìm thấy, null nếu không tìm thấy
   */
  private findToolNamePatternError(errors: ValidationError[]): string[] | null {
    for (const error of errors) {
      if (error.property === 'toolName' && error.constraints && error.constraints.matches) {
        return [error.constraints.matches];
      }

      // Tìm kiếm đệ quy trong các lỗi con
      if (error.children && error.children.length > 0) {
        const childError = this.findToolNamePatternError(error.children);
        if (childError) {
          return childError;
        }
      }
    }

    return null;
  }

  /**
   * Xây dựng thông tin lỗi validation chi tiết
   * @param errors Danh sách lỗi validation
   * @returns Danh sách lỗi chi tiết
   */
  private buildDetailedErrors(errors: ValidationError[]): any[] {
    const detailedErrors: any[] = [];

    const processError = (error: ValidationError, path: string = '') => {
      const currentPath = path ? `${path}.${error.property}` : error.property;

      if (error.constraints) {
        // Lỗi ở level hiện tại
        const constraintMessages = Object.entries(error.constraints).map(([key, message]) => ({
          constraint: key,
          message: message
        }));

        detailedErrors.push({
          field: currentPath,
          value: error.value,
          type: typeof error.value,
          constraints: constraintMessages,
          messages: Object.values(error.constraints)
        });
      }

      // Xử lý lỗi con (nested objects/arrays)
      if (error.children && error.children.length > 0) {
        error.children.forEach(child => {
          processError(child, currentPath);
        });
      }
    };

    errors.forEach(error => processError(error));
    return detailedErrors;
  }

  /**
   * Tạo tóm tắt lỗi dễ hiểu
   * @param detailedErrors Danh sách lỗi chi tiết
   * @returns Chuỗi tóm tắt lỗi
   */
  private buildErrorSummary(detailedErrors: any[]): string {
    if (detailedErrors.length === 0) return 'Lỗi validation không xác định';

    // Nhóm lỗi theo field chính (trước dấu chấm đầu tiên)
    const groupedErrors = new Map<string, any[]>();

    detailedErrors.forEach(error => {
      const mainField = error.field.split('.')[0];
      if (!groupedErrors.has(mainField)) {
        groupedErrors.set(mainField, []);
      }
      groupedErrors.get(mainField)!.push(error);
    });

    const fieldSummaries = Array.from(groupedErrors.entries()).map(([mainField, errors]) => {
      if (errors.length === 1) {
        const error = errors[0];
        const vietnameseMessage = this.translateErrorMessage(error.messages[0] || 'Giá trị không hợp lệ');
        return `${mainField} (${vietnameseMessage})`;
      } else {
        return `${mainField} (${errors.length} lỗi)`;
      }
    });

    const summary = fieldSummaries.join(', ');
    return detailedErrors.length > 3
      ? `${fieldSummaries.slice(0, 3).join(', ')} và ${detailedErrors.length - 3} lỗi khác`
      : summary;
  }

  /**
   * Dịch thông báo lỗi sang tiếng Việt
   * @param message Thông báo lỗi tiếng Anh
   * @returns Thông báo lỗi tiếng Việt
   */
  private translateErrorMessage(message: string): string {
    const translations: Record<string, string> = {
      'should not be empty': 'không được để trống',
      'must be a string': 'phải là chuỗi ký tự',
      'must be a number': 'phải là số',
      'must be an integer': 'phải là số nguyên',
      'must be a boolean': 'phải là true/false',
      'must be an array': 'phải là mảng',
      'must be an object': 'phải là object',
      'must be a valid email': 'phải là email hợp lệ',
      'must be a valid URL': 'phải là URL hợp lệ',
      'must be a valid date': 'phải là ngày hợp lệ',
      'must be positive': 'phải là số dương',
      'must be negative': 'phải là số âm',
      'must not be less than': 'không được nhỏ hơn',
      'must not be greater than': 'không được lớn hơn',
      'must be longer than': 'phải dài hơn',
      'must be shorter than': 'phải ngắn hơn',
      'must match': 'phải khớp với',
      'must be one of the following values': 'phải là một trong các giá trị',
      'nested property': 'thuộc tính con',
      'each value in': 'mỗi giá trị trong',
    };

    let translatedMessage = message;
    Object.entries(translations).forEach(([english, vietnamese]) => {
      translatedMessage = translatedMessage.replace(new RegExp(english, 'gi'), vietnamese);
    });

    return translatedMessage;
  }

  /**
   * Format lỗi validation thành dạng dễ đọc (legacy method)
   * @param errors Danh sách lỗi validation
   * @returns Danh sách lỗi đã được format
   */
  private formatErrors(errors: ValidationError[]): any[] {
    return errors.map(error => {
      const errorInfo: any = {
        property: error.property,
        value: error.value,
        constraints: error.constraints || {},
      };

      if (error.constraints) {
        errorInfo.messages = Object.values(error.constraints);
      }

      if (error.children && error.children.length > 0) {
        errorInfo.children = this.formatErrors(error.children);
      }

      return errorInfo;
    });
  }

  /**
   * Format lỗi validation thành cấu trúc chi tiết với thông tin đầy đủ
   * @param errors Danh sách lỗi validation
   * @returns Object chứa lỗi validation chi tiết theo từng field
   */
  private formatDetailedErrors(errors: ValidationError[]): Record<string, any> {
    const result: Record<string, any> = {};

    const processError = (error: ValidationError, parentPath = '') => {
      const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;

      // Khởi tạo object cho field hiện tại
      if (!result[currentPath]) {
        result[currentPath] = {
          property: error.property,
          value: error.value,
          constraints: [],
          messages: [],
          children: {}
        };
      }

      // Thêm constraints và messages
      if (error.constraints) {
        result[currentPath].constraints = Object.keys(error.constraints);
        result[currentPath].messages = Object.values(error.constraints);
      }

      // Xử lý lỗi con (nested objects/arrays)
      if (error.children && error.children.length > 0) {
        error.children.forEach(child => {
          processError(child, currentPath);
        });
      }
    };

    errors.forEach(error => processError(error));
    return result;
  }

  /**
   * Trích xuất thông báo lỗi dễ hiểu
   * @param errors Danh sách lỗi validation
   * @returns Mảng thông báo lỗi
   */
  private extractErrorMessages(errors: ValidationError[]): string[] {
    const messages: string[] = [];

    const extractFromError = (error: ValidationError, parentPath = '') => {
      const currentPath = parentPath ? `${parentPath}.${error.property}` : error.property;

      if (error.constraints) {
        Object.values(error.constraints).forEach(message => {
          messages.push(`${currentPath}: ${message}`);
        });
      }

      if (error.children && error.children.length > 0) {
        error.children.forEach(child => extractFromError(child, currentPath));
      }
    };

    errors.forEach(error => extractFromError(error));
    return messages;
  }
}
