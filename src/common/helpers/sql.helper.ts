import {
  DataSource,
  EntityManager,
  ObjectLiteral,
  Repository,
  SelectQueryBuilder,
} from 'typeorm';
import { PaginatedResult } from '@common/response/api-response-dto';
import { QueryDto } from '@common/dto/query.dto';
import { plainToInstance, ClassConstructor } from 'class-transformer';

/**
 * Type định nghĩa giá trị tham số SQL
 */
export type SqlParamValue = string | number | boolean | null | undefined;

/**
 * Type định nghĩa mảng tham số SQL
 */
export type SqlParamArray = SqlParamValue[];

/**
 * Type định nghĩa đối tượng dữ liệu
 */
export type DataRecord = Record<string, SqlParamValue>;

/**
 * Type định nghĩa kết quả truy vấn SQL
 */
export type SqlQueryResult<T> = T extends void ? void : T;

/**
 * Interface định nghĩa lỗi SQL
 */
export interface SqlError extends Error {
  code?: string;
  detail?: string;
  hint?: string;
  position?: string;
  internalQuery?: string;
  internalPosition?: string;
  where?: string;
  schema?: string;
  table?: string;
  column?: string;
  dataType?: string;
  constraint?: string;
}

/**
 * Interface định nghĩa tham số cho câu lệnh SQL
 */
export interface SqlParams {
  [key: string]: SqlParamValue;
}

/**
 * Interface định nghĩa tùy chọn cho câu lệnh SQL
 */
export interface SqlOptions {
  /**
   * Có trả về raw data không (không chuyển đổi thành entity)
   */
  raw?: boolean;
  /**
   * Có trả về kết quả dưới dạng stream không
   */
  stream?: boolean;
}

/**
 * Interface định nghĩa điều kiện WHERE
 */
export interface WhereCondition {
  /**
   * Điều kiện SQL (ví dụ: "column = :value")
   */
  condition: string;
  /**
   * Tham số cho điều kiện
   */
  params?: SqlParams;
}

/**
 * Interface định nghĩa thông tin JOIN
 */
export interface JoinInfo {
  /**
   * Loại JOIN (INNER, LEFT, RIGHT, FULL)
   */
  type: 'INNER' | 'LEFT' | 'RIGHT' | 'FULL';
  /**
   * Bảng cần JOIN
   */
  table: string;
  /**
   * Alias cho bảng JOIN
   */
  alias: string;
  /**
   * Điều kiện JOIN (ví dụ: "table1.id = table2.table1_id")
   */
  condition: string;
}

/**
 * Interface định nghĩa thông tin ORDER BY
 */
export interface OrderByInfo {
  /**
   * Tên cột cần sắp xếp
   */
  column: string;
  /**
   * Hướng sắp xếp (ASC hoặc DESC)
   */
  direction?: 'ASC' | 'DESC';
}

/**
 * Interface định nghĩa thông tin GROUP BY
 */
export interface GroupByInfo {
  /**
   * Tên cột cần nhóm
   */
  column: string;
}

/**
 * Interface định nghĩa thông tin HAVING
 */
export interface HavingCondition {
  /**
   * Điều kiện HAVING (ví dụ: "COUNT(*) > :count")
   */
  condition: string;
  /**
   * Tham số cho điều kiện
   */
  params?: SqlParams;
}

/**
 * Interface định nghĩa thông tin LIMIT và OFFSET
 */
export interface PaginationInfo {
  /**
   * Số lượng bản ghi tối đa
   */
  limit?: number;
  /**
   * Vị trí bắt đầu
   */
  offset?: number;
}

/**
 * Interface định nghĩa thông tin cập nhật
 */
export interface UpdateInfo {
  /**
   * Tên cột cần cập nhật
   */
  column: string;
  /**
   * Giá trị mới
   */
  value: SqlParamValue;
  /**
   * Có sử dụng tham số không (mặc định là true)
   */
  useParam?: boolean;
}

/**
 * Class chứa các hàm helper để thực hiện các câu lệnh SQL thuần
 */
export class SqlHelper {
  private dataSource: DataSource;
  private readonly enableLogging: boolean;

  constructor(dataSource: DataSource, options?: { enableLogging?: boolean }) {
    this.dataSource = dataSource;
    this.enableLogging = options?.enableLogging ?? false;
  }

  /**
   * Helper để xử lý điều kiện WHERE
   * @private
   */
  private processWhereConditions(
    whereConditions: WhereCondition[],
    params: SqlParamArray,
    paramIndex: number,
  ): {
    whereSql: string;
    newParams: SqlParamArray;
    newParamIndex: number;
  } {
    const whereParts: string[] = [];
    let currentParams = [...params];
    let currentParamIndex = paramIndex;

    for (const wc of whereConditions) {
      let condition = wc.condition;

      if (wc.params) {
        for (const [key, value] of Object.entries(wc.params)) {
          const paramPlaceholder = `:${key}`;
          if (condition.includes(paramPlaceholder)) {
            condition = condition.replace(
              new RegExp(paramPlaceholder, 'g'),
              `$${currentParamIndex}`,
            );
            currentParams.push(value);
            currentParamIndex++;
          }
        }
      }

      whereParts.push(condition);
    }

    return {
      whereSql:
        whereParts.length > 0 ? ` WHERE ${whereParts.join(' AND ')}` : '',
      newParams: currentParams,
      newParamIndex: currentParamIndex,
    };
  }

  /**
   * Helper để xử lý JOIN
   * @private
   */
  private processJoins(joinInfos: JoinInfo[]): string {
    let joinSql = '';

    if (joinInfos.length > 0) {
      for (const joinInfo of joinInfos) {
        joinSql += ` ${joinInfo.type} JOIN ${joinInfo.table} ${joinInfo.alias} ON ${joinInfo.condition}`;
      }
    }

    return joinSql;
  }

  /**
   * Helper để xử lý RETURNING
   * @private
   */
  private processReturning(returningColumns: string[]): string {
    if (returningColumns.length === 0) {
      return '';
    }

    // Quote column names to handle reserved keywords
    const quotedColumns = returningColumns.map(column => {
      // If column is already '*', don't quote it
      if (column === '*') return column;

      // Handle column aliases (format: "column as alias" or "column AS alias" or "column_name as columnName")
      const asIndex = column.toLowerCase().indexOf('as');
      if (asIndex > 0) {
        // Extract column name and alias, handling both formats with and without spaces
        let columnName, alias;

        if (column.toLowerCase().includes(' as ')) {
          // Format with spaces: "column as alias" or "column AS alias"
          [columnName, alias] = column.split(/ as /i).map(part => part.trim());
        } else {
          // Format without spaces: "column_name as columnName"
          columnName = column.substring(0, asIndex).trim();
          alias = column.substring(asIndex + 2).trim();
        }

        return `"${columnName}" AS "${alias}"`;
      }

      return `"${column}"`;
    });

    return ` RETURNING ${quotedColumns.join(', ')}`;
  }

  /**
   * Helper để log câu lệnh SQL
   * @private
   */
  private logQuery(
    type: string,
    query: string,
    values?: SqlParamArray | Record<string, unknown>,
  ): void {
    if (this.enableLogging) {
      console.log(`[SQL ${type}] Query:`, query);
      if (values) {
        if (Array.isArray(values) && values.length > 10) {
          console.log(`[SQL ${type}] Values count:`, values.length);
        } else {
          console.log(`[SQL ${type}] Values:`, values);
        }
      }
    }
  }

  /**
   * Helper để xử lý lỗi SQL
   * @private
   */
  private handleError(
    type: string,
    error: Error | SqlError,
    query?: string,
  ): never {
    console.error(`Error executing ${type} query:`, error);
    if (query && this.enableLogging) {
      console.error(`Failed query: ${query}`);
    }
    throw error;
  }

  /**
   * Thực thi câu lệnh SQL SELECT
   *
   * @param tableName Tên bảng
   * @param columns Danh sách cột cần lấy
   * @param whereConditions Điều kiện WHERE
   * @param joinInfos Thông tin JOIN
   * @param orderByInfos Thông tin ORDER BY
   * @param groupByInfos Thông tin GROUP BY
   * @param havingCondition Điều kiện HAVING
   * @param paginationInfo Thông tin phân trang
   * @param options Tùy chọn
   * @param dtoType Class constructor của DTO (tùy chọn)
   * @returns Kết quả truy vấn
   */
  async select<T = Record<string, unknown>>(
    tableName: string,
    columns: string[] = ['*'],
    whereConditions: WhereCondition[] = [],
    joinInfos: JoinInfo[] = [],
    orderByInfos: OrderByInfo[] = [],
    groupByInfos: GroupByInfo[] = [],
    havingCondition?: HavingCondition,
    paginationInfo?: PaginationInfo,
    options: SqlOptions = { raw: true },
    dtoType?: ClassConstructor<T>,
  ): Promise<T[]> {
    try {
      // Xây dựng câu lệnh SELECT
      let query = `SELECT ${columns.join(', ')}
                   FROM ${tableName}`;

      // Thêm các JOIN
      query += this.processJoins(joinInfos);

      // Chuẩn bị tham số và câu lệnh SQL
      const params: SqlParamArray = [];
      let paramIndex = 1;

      // Thêm điều kiện WHERE
      const { whereSql, newParams, newParamIndex } =
        this.processWhereConditions(whereConditions, params, paramIndex);
      query += whereSql;
      paramIndex = newParamIndex;

      // Thêm GROUP BY
      if (groupByInfos.length > 0) {
        const groupByClause = groupByInfos.map((gb) => gb.column).join(', ');
        query += ` GROUP BY ${groupByClause}`;
      }

      // Thêm HAVING
      if (havingCondition) {
        let havingClause = havingCondition.condition;

        if (havingCondition.params) {
          for (const [key, value] of Object.entries(havingCondition.params)) {
            const paramPlaceholder = `:${key}`;
            if (havingClause.includes(paramPlaceholder)) {
              havingClause = havingClause.replace(
                new RegExp(paramPlaceholder, 'g'),
                `$${paramIndex}`,
              );
              newParams.push(value);
              paramIndex++;
            }
          }
        }

        query += ` HAVING ${havingClause}`;
      }

      // Thêm ORDER BY
      if (orderByInfos.length > 0) {
        const orderByClause = orderByInfos
          .map((ob) => `${ob.column} ${ob.direction || 'ASC'}`)
          .join(', ');
        query += ` ORDER BY ${orderByClause}`;
      }

      // Thêm LIMIT và OFFSET
      if (paginationInfo) {
        if (paginationInfo.limit !== undefined) {
          query += ` LIMIT ${paginationInfo.limit}`;
        }
        if (paginationInfo.offset !== undefined) {
          query += ` OFFSET ${paginationInfo.offset}`;
        }
      }

      // Log câu lệnh SQL
      this.logQuery('SELECT', query, newParams);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(query, newParams);

      // Chuyển đổi kết quả sang DTO nếu có dtoType
      if (dtoType) {
        return result.map((item: Record<string, unknown>) =>
          plainToInstance(dtoType, item, {
            excludeExtraneousValues: true
          })
        );
      }

      return result;
    } catch (error) {
      return this.handleError('SELECT', error);
    }
  }

  /**
   * Thực thi câu lệnh SQL INSERT
   *
   * @param tableName Tên bảng
   * @param data Dữ liệu cần chèn
   * @param returningColumns Danh sách cột cần trả về sau khi chèn
   * @returns Kết quả chèn
   */
  async insert<T = Record<string, unknown>>(
    tableName: string,
    data: DataRecord,
    returningColumns: string[] = ['*'],
  ): Promise<T | null> {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map((_, index) => `$${index + 1}`);

      // Quote column names to handle reserved keywords
      const quotedColumns = columns.map(column => `"${column}"`);

      let query = `INSERT INTO ${tableName} (${quotedColumns.join(', ')})
                   VALUES (${placeholders.join(', ')})`;

      // Thêm RETURNING nếu cần
      query += this.processReturning(returningColumns);

      // Log câu lệnh SQL
      this.logQuery('INSERT', query, values);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(query, values);
      return returningColumns.length > 0 ? result[0] : null;
    } catch (error) {
      return this.handleError('INSERT', error);
    }
  }

  /**
   * Thực thi câu lệnh SQL UPDATE
   *
   * @param tableName Tên bảng
   * @param updates Thông tin cập nhật
   * @param whereConditions Điều kiện WHERE
   * @param returningColumns Danh sách cột cần trả về sau khi cập nhật
   * @returns Kết quả cập nhật
   */
  async update<T = Record<string, unknown>>(
    tableName: string,
    updates: UpdateInfo[],
    whereConditions: WhereCondition[] = [],
    returningColumns: string[] = [],
  ): Promise<T[]> {
    try {
      // Xây dựng câu lệnh UPDATE
      let query = `UPDATE ${tableName}
                   SET `;

      // Chuẩn bị tham số và câu lệnh SQL
      const params: SqlParamArray = [];
      let paramIndex = 1;

      // Thêm các cột cần cập nhật
      const updateClauses: string[] = [];

      for (const update of updates) {
        // Quote column name to handle reserved keywords
        const quotedColumn = `"${update.column}"`;

        if (update.useParam !== false) {
          updateClauses.push(`${quotedColumn} = $${paramIndex}`);
          params.push(update.value);
          paramIndex++;
        } else {
          updateClauses.push(`${quotedColumn} = ${update.value}`);
        }
      }

      query += updateClauses.join(', ');

      // Thêm điều kiện WHERE
      const { whereSql, newParams, newParamIndex } =
        this.processWhereConditions(whereConditions, params, paramIndex);
      query += whereSql;

      // Thêm RETURNING nếu cần
      query += this.processReturning(returningColumns);

      // Log câu lệnh SQL
      this.logQuery('UPDATE', query, newParams);

      // Thực thi câu lệnh SQL
      return await this.dataSource.query(query, newParams);
    } catch (error) {
      return this.handleError('UPDATE', error);
    }
  }

  /**
   * Thực thi câu lệnh SQL DELETE
   *
   * @param tableName Tên bảng
   * @param whereConditions Điều kiện WHERE
   * @param returningColumns Danh sách cột cần trả về sau khi xóa
   * @returns Kết quả xóa
   */
  async delete<T = Record<string, unknown>>(
    tableName: string,
    whereConditions: WhereCondition[] = [],
    returningColumns: string[] = [],
  ): Promise<T[]> {
    try {
      // Xây dựng câu lệnh DELETE
      let query = `DELETE
                   FROM ${tableName}`;

      // Chuẩn bị tham số và câu lệnh SQL
      const params: SqlParamArray = [];
      let paramIndex = 1;

      // Thêm điều kiện WHERE
      const { whereSql, newParams, newParamIndex } =
        this.processWhereConditions(whereConditions, params, paramIndex);
      query += whereSql;

      // Thêm RETURNING nếu cần
      query += this.processReturning(returningColumns);

      // Log câu lệnh SQL
      this.logQuery('DELETE', query, newParams);

      // Thực thi câu lệnh SQL
      return await this.dataSource.query(query, newParams);
    } catch (error) {
      return this.handleError('DELETE', error);
    }
  }

  /**
   * Kiểm tra sự tồn tại của bản ghi
   *
   * @param tableName Tên bảng
   * @param whereConditions Điều kiện WHERE
   * @returns true nếu tồn tại, false nếu không tồn tại
   */
  async exists(
    tableName: string,
    whereConditions: WhereCondition[] = [],
  ): Promise<boolean> {
    try {
      // Xây dựng câu lệnh EXISTS
      let query = `SELECT EXISTS(SELECT 1 FROM ${tableName}`;

      // Chuẩn bị tham số và câu lệnh SQL
      const params: SqlParamArray = [];
      let paramIndex = 1;

      // Thêm điều kiện WHERE
      const { whereSql, newParams, newParamIndex } =
        this.processWhereConditions(whereConditions, params, paramIndex);
      query += whereSql;

      query += `) AS "exists"`;

      // Log câu lệnh SQL
      this.logQuery('EXISTS', query, newParams);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(query, newParams);
      return result[0]?.exists === true;
    } catch (error) {
      this.handleError('EXISTS', error);
      return false; // Trả về false nếu có lỗi
    }
  }

  /**
   * Thực thi câu lệnh SQL COUNT
   *
   * @param tableName Tên bảng
   * @param whereConditions Điều kiện WHERE
   * @param joinInfos Thông tin JOIN
   * @returns Số lượng bản ghi
   */
  async count(
    tableName: string,
    whereConditions: WhereCondition[] = [],
    joinInfos: JoinInfo[] = [],
  ): Promise<number> {
    try {
      // Xây dựng câu lệnh COUNT
      // Thêm dấu ngoặc kép cho tên bảng để tránh xung đột với từ khóa dành riêng
      const quotedTableName = tableName.startsWith('"') ? tableName : `"${tableName}"`;
      let query = `SELECT COUNT(*) AS "count"
                   FROM ${quotedTableName}`;

      // Thêm các JOIN
      query += this.processJoins(joinInfos);

      // Chuẩn bị tham số và câu lệnh SQL
      const params: SqlParamArray = [];
      let paramIndex = 1;

      // Thêm điều kiện WHERE
      const { whereSql, newParams, newParamIndex } =
        this.processWhereConditions(whereConditions, params, paramIndex);
      query += whereSql;

      // Log câu lệnh SQL
      this.logQuery('COUNT', query, newParams);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(query, newParams);
      return parseInt(result[0]?.count || '0', 10);
    } catch (error) {
      this.handleError('COUNT', error);
      return 0; // Trả về 0 nếu có lỗi
    }
  }

  /**
   * Thực thi câu lệnh SQL tùy chỉnh
   *
   * @param sql Câu lệnh SQL
   * @param params Tham số
   * @param dtoType Class constructor của DTO (tùy chọn)
   * @returns Kết quả truy vấn
   */
  async executeRawQuery<T = Record<string, unknown>>(
    sql: string,
    params: SqlParamArray = [],
    dtoType?: ClassConstructor<T>
  ): Promise<T[]> {
    try {
      // Log câu lệnh SQL
      this.logQuery('RAW', sql, params);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(sql, params);

      // Chuyển đổi kết quả sang DTO nếu có dtoType và là truy vấn SELECT
      if (dtoType && sql.trim().toUpperCase().startsWith('SELECT')) {
        return result.map((item: Record<string, unknown>) =>
          plainToInstance(dtoType, item, {
            excludeExtraneousValues: true
          })
        );
      }

      return result;
    } catch (error) {
      return this.handleError('RAW', error, sql);
    }
  }

  /**
   * Thực thi nhiều câu lệnh SQL trong một transaction
   *
   * @param callback Hàm callback chứa các câu lệnh SQL cần thực thi
   * @param dtoType Class constructor của DTO (tùy chọn) để chuyển đổi kết quả cuối cùng
   * @returns Kết quả từ callback
   */
  async executeInTransaction<T = unknown, D = T>(
    callback: (entityManager: EntityManager) => Promise<T>,
    dtoType?: ClassConstructor<D>
  ): Promise<D | T> {
    const queryRunner = this.dataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      if (this.enableLogging) {
        console.log('[SQL TRANSACTION] Started');
      }

      // Thực thi callback trong transaction
      const result = await callback(queryRunner.manager);
      await queryRunner.commitTransaction();

      if (this.enableLogging) {
        console.log('[SQL TRANSACTION] Committed successfully');
      }

      // Chuyển đổi kết quả sang DTO nếu có dtoType và kết quả là một object hoặc mảng
      if (dtoType && result) {
        if (Array.isArray(result)) {
          // Nếu kết quả là mảng, chuyển đổi từng phần tử
          return result.map((item) =>
            plainToInstance(dtoType, item, {
              excludeExtraneousValues: true
            })
          ) as unknown as D;
        } else {
          // Nếu kết quả là object, chuyển đổi trực tiếp
          return plainToInstance(dtoType, result, {
            excludeExtraneousValues: true
          });
        }
      }

      return result as unknown as D | T;
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error('Error executing transaction:', error);

      if (this.enableLogging) {
        console.log('[SQL TRANSACTION] Rolled back due to error');
      }

      throw error;
    } finally {
      await queryRunner.release();

      if (this.enableLogging) {
        console.log('[SQL TRANSACTION] Resources released');
      }
    }
  }

  /**
   * Tạo câu lệnh SQL INSERT với nhiều bản ghi
   *
   * @param tableName Tên bảng
   * @param records Danh sách bản ghi cần chèn
   * @param returningColumns Danh sách cột cần trả về sau khi chèn
   * @returns Kết quả chèn
   */
  async bulkInsert<T = Record<string, unknown>>(
    tableName: string,
    records: DataRecord[],
    returningColumns: string[] = [],
  ): Promise<T[]> {
    try {
      if (records.length === 0) {
        return [];
      }

      // Lấy tất cả các cột từ bản ghi đầu tiên
      const columns = Object.keys(records[0]);

      // Xây dựng câu lệnh INSERT
      let query = `INSERT INTO ${tableName} (${columns.join(', ')}) VALUES `;

      const values: SqlParamArray = [];
      const placeholderRows: string[] = [];

      // Tạo placeholders và values cho mỗi bản ghi
      records.forEach((record, recordIndex) => {
        const rowPlaceholders: string[] = [];

        columns.forEach((column, columnIndex) => {
          const paramIndex = recordIndex * columns.length + columnIndex + 1;
          rowPlaceholders.push(`$${paramIndex}`);
          values.push(record[column]);
        });

        placeholderRows.push(`(${rowPlaceholders.join(', ')})`);
      });

      query += placeholderRows.join(', ');

      // Thêm RETURNING nếu cần
      query += this.processReturning(returningColumns);

      // Log câu lệnh SQL
      this.logQuery('BULK INSERT', query, { valueCount: values.length });

      // Thực thi câu lệnh SQL
      return await this.dataSource.query(query, values);
    } catch (error) {
      return this.handleError('BULK INSERT', error);
    }
  }

  /**
   * Tạo câu lệnh SQL UPSERT (INSERT ... ON CONFLICT DO UPDATE)
   *
   * @param tableName Tên bảng
   * @param data Dữ liệu cần chèn hoặc cập nhật
   * @param conflictColumns Các cột xung đột
   * @param updateColumns Các cột cần cập nhật khi xung đột
   * @param returningColumns Danh sách cột cần trả về
   * @returns Kết quả upsert
   */
  async upsert<T = Record<string, unknown>>(
    tableName: string,
    data: DataRecord,
    conflictColumns: string[],
    updateColumns: string[],
    returningColumns: string[] = [],
  ): Promise<T | null> {
    try {
      const columns = Object.keys(data);
      const values = Object.values(data);
      const placeholders = columns.map((_, index) => `$${index + 1}`);

      let query = `INSERT INTO ${tableName} (${columns.join(', ')})
                   VALUES (${placeholders.join(', ')})`;

      // Thêm ON CONFLICT
      query += ` ON CONFLICT (${conflictColumns.join(', ')}) DO UPDATE SET `;

      // Thêm các cột cần cập nhật
      const updateClauses = updateColumns.map(
        (column) => `${column} = EXCLUDED.${column}`,
      );
      query += updateClauses.join(', ');

      // Thêm RETURNING nếu cần
      query += this.processReturning(returningColumns);

      // Log câu lệnh SQL
      this.logQuery('UPSERT', query, values);

      // Thực thi câu lệnh SQL
      const result = await this.dataSource.query(query, values);
      return returningColumns.length > 0 ? result[0] : null;
    } catch (error) {
      return this.handleError('UPSERT', error);
    }
  }

  /**
   * Tạo đối tượng kết quả phân trang từ danh sách items và tổng số bản ghi
   * @private
   */
  private createPaginatedResult<T>(
    items: T[],
    totalItems: number,
    { page = 1, limit = 10 }: QueryDto,
  ): PaginatedResult<T> {
    return {
      items,
      meta: {
        totalItems,
        itemCount: items.length,
        itemsPerPage: limit,
        totalPages: Math.ceil(totalItems / limit),
        currentPage: page,
      },
    };
  }

  /**
   * Áp dụng select các trường cụ thể
   * @private
   */
  private applySelectParams<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    fields: (keyof T)[] | undefined,
    alias: string,
  ): SelectQueryBuilder<T> {
    if (fields && fields.length) {
      const selections = fields.map((field) => `${alias}.${String(field)}`);
      qb.select(selections);
    }
    return qb;
  }

  /**
   * Áp dụng tìm kiếm chung (từ khóa)
   * @private
   */
  private applySearchParams<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    keyword: string | undefined,
    fields: (keyof T)[] | undefined,
    alias: string,
  ): SelectQueryBuilder<T> {
    // Nếu không có keyword hoặc là 'all' thì không thêm điều kiện search
    if (!keyword || keyword.trim().toLowerCase() === 'all') {
      return qb;
    }

    // Nếu không có trường cụ thể, lấy các trường từ select hiện tại
    if (!fields || fields.length === 0) {
      const selected = qb.expressionMap.selects.map((s) => s.selection);
      fields = selected
        .filter((sel) => sel.startsWith(alias + '.'))
        .map((sel) => sel.replace(alias + '.', '')) as (keyof T)[];
    }

    if (fields.length > 0) {
      const param = `%${keyword}%`;
      const conditions = fields.map(
        (field) => `${alias}.${String(field)} ILIKE :keyword`,
      );
      qb.andWhere(`(${conditions.join(' OR ')})`, { keyword: param });
    }

    return qb;
  }

  /**
   * Áp dụng sắp xếp chung
   * @private
   */
  private applySortParams<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    { sortBy, sortDirection }: QueryDto,
    alias: string,
  ): SelectQueryBuilder<T> {
    if (sortBy) {
      qb.addOrderBy(`${alias}.${sortBy}`, sortDirection);
    }
    return qb;
  }

  /**
   * Áp dụng groupBy các trường
   * @private
   */
  private applyGroupByParams<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    fields: (keyof T)[] | undefined,
    alias: string,
  ): SelectQueryBuilder<T> {
    if (fields && fields.length) {
      fields.forEach((field) => qb.addGroupBy(`${alias}.${String(field)}`));
    }
    return qb;
  }

  /**
   * Áp dụng pagination basics
   * @private
   */
  private applyPaginationParams<T extends ObjectLiteral>(
    qb: SelectQueryBuilder<T>,
    { page = 1, limit = 10 }: QueryDto,
  ): SelectQueryBuilder<T> {
    qb.skip((page - 1) * limit).take(limit);
    return qb;
  }

  /**
   * Lấy kết quả phân trang sử dụng TypeORM QueryBuilder với chuyển đổi DTO
   *
   * @param repository Repository của entity
   * @param query Tham số truy vấn (page, limit, search, sortBy, sortDirection)
   * @param options Tùy chọn (alias, selectFields, searchFields, groupByFields, customize, dtoType)
   * @returns Kết quả phân trang với DTO
   */
  async getPaginatedData<T extends ObjectLiteral, D = T>(
    repository: Repository<T>,
    query: QueryDto,
    options?: {
      alias?: string;
      selectFields?: (keyof T)[];
      searchFields?: (keyof T)[];
      groupByFields?: (keyof T)[];
      customize?: (qb: SelectQueryBuilder<T>) => SelectQueryBuilder<T>;
      dtoType?: new () => D; // Thêm tham số dtoType
    },
  ): Promise<PaginatedResult<D>> {
    try {
      if (this.enableLogging) {
        console.log('[SQL PAGINATION] Query:', JSON.stringify(query));
        console.log(
          '[SQL PAGINATION] Options:',
          JSON.stringify(options, (key, value) => {
            if (key === 'customize' || key === 'dtoType') return '[Function]';
            return value;
          }),
        );
      }

      const alias = options?.alias ?? 'entity';
      let qb = repository.createQueryBuilder(alias);

      // Select cụ thể
      qb = this.applySelectParams(qb, options?.selectFields, alias);

      // Cho phép customize (join/where thêm)
      if (options?.customize) {
        qb = options.customize(qb);
      }

      // Search
      qb = this.applySearchParams(
        qb,
        query.search,
        options?.searchFields,
        alias,
      );

      // Sort cơ bản từ QueryDto
      qb = this.applySortParams(qb, query, alias);

      // GroupBy
      qb = this.applyGroupByParams(qb, options?.groupByFields, alias);

      // Pagination
      qb = this.applyPaginationParams(qb, query);

      // Thực thi
      const [items, totalItems] = await qb.getManyAndCount();

      // Chuyển đổi sang DTO nếu có dtoType
      let dtoItems: D[] = items as unknown as D[];
      if (options?.dtoType) {
        dtoItems = items.map(item =>
          plainToInstance(options.dtoType as any, item, {
            excludeExtraneousValues: true
          })
        );
      }

      return this.createPaginatedResult(dtoItems, totalItems, query);
    } catch (error) {
      console.error('Error executing paginated query:', error);
      throw error;
    }
  }
}
