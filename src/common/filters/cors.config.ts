import { CorsOptions } from '@nestjs/common/interfaces/external/cors-options.interface';

/**
 * <PERSON><PERSON><PERSON> hình CORS cho ứng dụng
 * Cho phép các frontend được chỉ định truy cập API
 */
export const corsConfig: CorsOptions = {
  origin: [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:5179',
    'http://localhost:5180',
    'http://*************:5173',
    'http://*************:5174',
    'http://*************:5175',
    'http://*************:5176',
    'http://*************:5177',
    'http://*************:5178',
    'http://*************:5179',
    'http://*************:5180',
    'https://v2.redai.vn',
  ],
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Http-Method-Override',
    'X-ZEvent-Signature',
    'X-ZEvent-Timestamp',
  ],
  exposedHeaders: ['Content-Disposition'],
  maxAge: 3600,
};

/**
 * Hàm kiểm tra origin có được phép truy cập không
 * @param origin Origin của request
 * @param callback Callback function
 */
export const corsOriginCallback = (
  origin: string,
  callback: (err: Error | null, allow?: boolean) => void,
) => {
  const allowedOrigins = [
    'http://localhost:5173',
    'http://localhost:5174',
    'http://localhost:5175',
    'http://localhost:5176',
    'http://localhost:5177',
    'http://localhost:5178',
    'http://localhost:5179',
    'http://localhost:5180',
    'http://*************:5173',
    'http://*************:5174',
    'http://*************:5175',
    'http://*************:5176',
    'http://*************:5177',
    'http://*************:5178',
    'http://*************:5179',
    'http://*************:5180',
    'https://v2.redai.vn',
  ];
  // Cho phép requests không có origin (như mobile apps hoặc curl requests)
  if (!origin) {
    return callback(null, true);
  }

  if (allowedOrigins.includes(origin)) {
    return callback(null, true);
  } else {
    return callback(new Error(`Origin ${origin} không được phép truy cập`));
  }
};

/**
 * Cấu hình CORS động sử dụng callback
 */
export const dynamicCorsConfig: CorsOptions = {
  origin: corsOriginCallback,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  credentials: true,
  allowedHeaders: [
    'Origin',
    'X-Requested-With',
    'Content-Type',
    'Accept',
    'Authorization',
    'X-Forwarded-For',
    'X-Forwarded-Proto',
    'X-Http-Method-Override',
    'Accept-Language',
    'X-Theme',
    'X-Country',
    'X-ZEvent-Signature',
    'X-ZEvent-Timestamp',
  ],
  exposedHeaders: [
    'Content-Disposition',
    'Accept-Language',
    'X-Theme',
    'X-Country',
  ],
  maxAge: 3600,
};
