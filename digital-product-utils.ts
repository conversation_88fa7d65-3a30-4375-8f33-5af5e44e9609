/**
 * Utility Functions cho Digital Product Form
 * Bao gồm validation, transformation và helper functions
 */

import {
  DigitalProductCreateDto,
  DigitalProductFormState,
  DigitalClassificationFormItem,
  DigitalProductValidationErrors,
  HasPriceDto,
  DigitalClassificationDto,
  CustomFieldInputDto,
  CustomFieldFormItem,
  VALIDATION_RULES,
  ProductTypeEnum,
} from './digital-product-interfaces';

// ==================== TRANSFORMATION FUNCTIONS ====================

/**
 * Chuyển đổi từ form state sang DTO để gửi API
 */
export const transformFormStateToDto = (formState: DigitalProductFormState): DigitalProductCreateDto => {
  return {
    productType: ProductTypeEnum.DIGITAL,
    name: formState.name.trim(),
    description: formState.description?.trim() || undefined,
    price: {
      listPrice: formState.listPrice,
      salePrice: formState.salePrice,
      currency: formState.currency,
    },
    imagesMediaTypes: formState.productImages.map(file => file.type),
    tags: formState.tags.filter(tag => tag.trim().length > 0),
    purchaseCount: formState.purchaseCount,
    digitalFulfillmentFlow: {
      deliveryMethod: formState.deliveryMethod,
      deliveryTiming: formState.deliveryTiming,
    },
    digitalOutput: {
      outputType: formState.outputType,
      accessLink: formState.accessLink.trim(),
      usageInstructions: formState.usageInstructions.trim(),
    },
    classifications: formState.classifications.map(transformClassificationFormItemToDto),
    customFields: formState.customFields.map(transformCustomFieldFormItemToDto),
  };
};

/**
 * Chuyển đổi classification form item sang DTO
 */
export const transformClassificationFormItemToDto = (item: DigitalClassificationFormItem): DigitalClassificationDto => {
  return {
    name: item.name.trim(),
    sku: item.sku.trim(),
    availableQuantity: item.availableQuantity,
    minQuantityPerPurchase: item.minQuantityPerPurchase,
    maxQuantityPerPurchase: item.maxQuantityPerPurchase,
    price: {
      listPrice: item.listPrice,
      salePrice: item.salePrice,
      currency: item.currency,
    },
    description: item.description.trim(),
    imagesMediaTypes: item.images.map(file => file.type),
    customFields: item.customFields.map(transformCustomFieldFormItemToDto),
  };
};

/**
 * Chuyển đổi custom field form item sang DTO
 */
export const transformCustomFieldFormItemToDto = (item: CustomFieldFormItem): CustomFieldInputDto => {
  return {
    fieldId: item.fieldId,
    fieldValue: item.fieldValue,
  };
};

// ==================== VALIDATION FUNCTIONS ====================

/**
 * Validate toàn bộ form
 */
export const validateDigitalProductForm = (formState: DigitalProductFormState): DigitalProductValidationErrors => {
  const errors: DigitalProductValidationErrors = {};

  // Validate basic info
  if (!formState.name.trim()) {
    errors.name = 'Tên sản phẩm là bắt buộc';
  } else if (formState.name.length > VALIDATION_RULES.NAME_MAX_LENGTH) {
    errors.name = `Tên sản phẩm không được vượt quá ${VALIDATION_RULES.NAME_MAX_LENGTH} ký tự`;
  }

  if (formState.description && formState.description.length > VALIDATION_RULES.DESCRIPTION_MAX_LENGTH) {
    errors.description = `Mô tả không được vượt quá ${VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} ký tự`;
  }

  // Validate price
  const priceErrors = validatePrice({
    listPrice: formState.listPrice,
    salePrice: formState.salePrice,
    currency: formState.currency,
  });
  if (Object.keys(priceErrors).length > 0) {
    errors.price = priceErrors;
  }

  // Validate digital output
  if (!formState.accessLink.trim()) {
    errors.digitalOutput = { accessLink: 'Link truy cập là bắt buộc' };
  }

  if (!formState.usageInstructions.trim()) {
    if (!errors.digitalOutput) errors.digitalOutput = {};
    errors.digitalOutput.usageInstructions = 'Hướng dẫn sử dụng là bắt buộc';
  }

  // Validate classifications
  if (formState.classifications.length === 0) {
    errors.classifications = { 0: { name: 'Ít nhất một phân loại là bắt buộc' } };
  } else {
    const classificationErrors: any = {};
    const usedNames = new Set<string>();
    const usedSkus = new Set<string>();

    formState.classifications.forEach((classification, index) => {
      const classificationError = validateClassification(classification, usedNames, usedSkus);
      if (Object.keys(classificationError).length > 0) {
        classificationErrors[index] = classificationError;
      }
    });

    if (Object.keys(classificationErrors).length > 0) {
      errors.classifications = classificationErrors;
    }
  }

  return errors;
};

/**
 * Validate price object
 */
export const validatePrice = (price: HasPriceDto): any => {
  const errors: any = {};

  if (!price.listPrice || price.listPrice <= 0) {
    errors.listPrice = 'Giá niêm yết phải lớn hơn 0';
  }

  if (!price.salePrice || price.salePrice <= 0) {
    errors.salePrice = 'Giá bán phải lớn hơn 0';
  }

  if (price.listPrice && price.salePrice && price.salePrice > price.listPrice) {
    errors.salePrice = 'Giá bán không được lớn hơn giá niêm yết';
  }

  if (!price.currency) {
    errors.currency = 'Đơn vị tiền tệ là bắt buộc';
  }

  return errors;
};

/**
 * Validate classification
 */
export const validateClassification = (
  classification: DigitalClassificationFormItem,
  usedNames: Set<string>,
  usedSkus: Set<string>
): any => {
  const errors: any = {};

  // Validate name
  if (!classification.name.trim()) {
    errors.name = 'Tên phân loại là bắt buộc';
  } else if (classification.name.length > VALIDATION_RULES.NAME_MAX_LENGTH) {
    errors.name = `Tên phân loại không được vượt quá ${VALIDATION_RULES.NAME_MAX_LENGTH} ký tự`;
  } else if (usedNames.has(classification.name.trim())) {
    errors.name = 'Tên phân loại đã tồn tại';
  } else {
    usedNames.add(classification.name.trim());
  }

  // Validate SKU
  if (!classification.sku.trim()) {
    errors.sku = 'SKU là bắt buộc';
  } else if (classification.sku.length > VALIDATION_RULES.SKU_MAX_LENGTH) {
    errors.sku = `SKU không được vượt quá ${VALIDATION_RULES.SKU_MAX_LENGTH} ký tự`;
  } else if (usedSkus.has(classification.sku.trim())) {
    errors.sku = 'SKU đã tồn tại';
  } else {
    usedSkus.add(classification.sku.trim());
  }

  // Validate quantities
  if (classification.availableQuantity < 0) {
    errors.availableQuantity = 'Số lượng có sẵn không được âm';
  }

  if (classification.minQuantityPerPurchase <= 0) {
    errors.minQuantityPerPurchase = 'Số lượng tối thiểu phải lớn hơn 0';
  }

  if (classification.maxQuantityPerPurchase <= 0) {
    errors.maxQuantityPerPurchase = 'Số lượng tối đa phải lớn hơn 0';
  }

  if (classification.minQuantityPerPurchase > classification.maxQuantityPerPurchase) {
    errors.maxQuantityPerPurchase = 'Số lượng tối đa phải lớn hơn hoặc bằng số lượng tối thiểu';
  }

  // Validate price
  const priceErrors = validatePrice({
    listPrice: classification.listPrice,
    salePrice: classification.salePrice,
    currency: classification.currency,
  });
  if (Object.keys(priceErrors).length > 0) {
    errors.price = priceErrors;
  }

  // Validate description
  if (classification.description && classification.description.length > VALIDATION_RULES.DESCRIPTION_MAX_LENGTH) {
    errors.description = `Mô tả không được vượt quá ${VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} ký tự`;
  }

  // Validate images
  if (classification.images.length > VALIDATION_RULES.MAX_IMAGES_PER_CLASSIFICATION) {
    errors.images = `Số lượng hình ảnh không được vượt quá ${VALIDATION_RULES.MAX_IMAGES_PER_CLASSIFICATION}`;
  }

  return errors;
};

// ==================== HELPER FUNCTIONS ====================

/**
 * Tạo classification mới với giá trị mặc định
 */
export const createNewClassification = (): DigitalClassificationFormItem => {
  return {
    id: `temp_${Date.now()}_${Math.random()}`,
    name: '',
    sku: '',
    availableQuantity: 0,
    minQuantityPerPurchase: 1,
    maxQuantityPerPurchase: 1,
    listPrice: 0,
    salePrice: 0,
    currency: 'VND',
    description: '',
    images: [],
    customFields: [],
  };
};

/**
 * Tạo custom field mới với giá trị mặc định
 */
export const createNewCustomField = (fieldId: number, fieldName?: string): CustomFieldFormItem => {
  return {
    fieldId,
    fieldName,
    fieldValue: '',
    isRequired: false,
  };
};

/**
 * Tạo form state mới với giá trị mặc định
 */
export const createInitialFormState = (): DigitalProductFormState => {
  return {
    name: '',
    description: '',
    tags: [],
    listPrice: 0,
    salePrice: 0,
    currency: 'VND',
    purchaseCount: 0,
    deliveryMethod: 'DASHBOARD' as any,
    deliveryTiming: 'IMMEDIATE' as any,
    outputType: 'DOWNLOAD_LINK' as any,
    accessLink: '',
    usageInstructions: '',
    productImages: [],
    classifications: [createNewClassification()],
    customFields: [],
  };
};

/**
 * Kiểm tra form có hợp lệ không
 */
export const isFormValid = (formState: DigitalProductFormState): boolean => {
  const errors = validateDigitalProductForm(formState);
  return Object.keys(errors).length === 0;
};

/**
 * Format giá tiền để hiển thị
 */
export const formatPrice = (price: number, currency: string = 'VND'): string => {
  if (currency === 'VND') {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
    }).format(price);
  }
  
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency,
  }).format(price);
};

/**
 * Generate SKU tự động dựa trên tên
 */
export const generateSku = (name: string, index?: number): string => {
  const cleanName = name
    .trim()
    .toUpperCase()
    .replace(/[^A-Z0-9]/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-|-$/g, '');
  
  const timestamp = Date.now().toString().slice(-6);
  const suffix = index !== undefined ? `-${index + 1}` : '';
  
  return `${cleanName}-${timestamp}${suffix}`;
};

/**
 * Validate file type cho hình ảnh
 */
export const isValidImageFile = (file: File): boolean => {
  const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];
  return validTypes.includes(file.type);
};

/**
 * Validate file size
 */
export const isValidFileSize = (file: File, maxSizeMB: number = 5): boolean => {
  const maxSizeBytes = maxSizeMB * 1024 * 1024;
  return file.size <= maxSizeBytes;
};
