### Digital Product Creation API Tests
### File: digital-product-create.http
### Chứa các DTO body chuẩn cho tạo sản phẩm số (DIGITAL)

@baseUrl = http://localhost:3000/api/v1
@token = your_jwt_token_here

### 1. T<PERSON><PERSON> sản phẩm số cơ bản - <PERSON>h<PERSON>a học online
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Khóa học React Advanced",
  "description": "Khóa học React nâng cao cho developers có kinh nghiệm",
  "price": {
    "listPrice": 2000000,
    "salePrice": 1500000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["react", "javascript", "frontend", "programming"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "ACCESS_CODE",
    "accessLink": "https://course.example.com/activate",
    "usageInstructions": "Sử dụng mã code để kích hoạt khóa học trên dashboard"
  },
  "classifications": [
    {
      "name": "Basic Package",
      "sku": "REACT-BASIC-001",
      "availableQuantity": 100,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 1500000,
        "salePrice": 1200000,
        "currency": "VND"
      },
      "description": "Gói cơ bản - Truy cập 6 tháng",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 1,
          "fieldValue": "6 months access"
        }
      ]
    },
    {
      "name": "Premium Package",
      "sku": "REACT-PREMIUM-001",
      "availableQuantity": 50,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 2500000,
        "salePrice": 2000000,
        "currency": "VND"
      },
      "description": "Gói cao cấp - Truy cập trọn đời + 1-on-1 mentoring",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 1,
          "fieldValue": "Lifetime access"
        },
        {
          "fieldId": 2,
          "fieldValue": "1-on-1 mentoring included"
        }
      ]
    }
  ],
  "customFields": [
    {
      "fieldId": 10,
      "fieldValue": "Online Course"
    },
    {
      "fieldId": 11,
      "fieldValue": "Advanced Level"
    }
  ]
}

### 2. Tạo sản phẩm số - Ebook với giao hàng qua Email
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Ebook Marketing Digital 2024",
  "description": "Sách điện tử về marketing digital mới nhất năm 2024",
  "price": {
    "listPrice": 500000,
    "salePrice": 350000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["ebook", "marketing", "digital", "2024"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "EMAIL",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://storage.example.com/ebooks/marketing-2024",
    "usageInstructions": "Link download sẽ được gửi qua email sau khi thanh toán thành công"
  },
  "classifications": [
    {
      "name": "PDF Version",
      "sku": "EBOOK-PDF-001",
      "availableQuantity": 1000,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 350000,
        "salePrice": 250000,
        "currency": "VND"
      },
      "description": "Phiên bản PDF - 250 trang",
      "imagesMediaTypes": [],
      "customFields": []
    },
    {
      "name": "PDF + Audio Bundle",
      "sku": "EBOOK-BUNDLE-001",
      "availableQuantity": 500,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 650000,
        "salePrice": 450000,
        "currency": "VND"
      },
      "description": "Combo PDF + Audio book (10 giờ)",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 5,
          "fieldValue": "Includes audio version"
        }
      ]
    }
  ]
}

### 3. Tạo sản phẩm số - Software License với giao hàng qua SMS
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "PhotoEdit Pro License",
  "description": "Phần mềm chỉnh sửa ảnh chuyên nghiệp với license 1 năm",
  "price": {
    "listPrice": 3000000,
    "salePrice": 2400000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg", "image/png"],
  "tags": ["software", "photo-editing", "license", "professional"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "SMS",
    "deliveryTiming": "DELAYED"
  },
  "digitalOutput": {
    "outputType": "ACCOUNT_INFO",
    "accessLink": "https://photoedit.example.com/activate",
    "usageInstructions": "Thông tin tài khoản và license key sẽ được gửi qua SMS trong vòng 24h"
  },
  "classifications": [
    {
      "name": "Personal License",
      "sku": "PHOTOEDIT-PERSONAL-001",
      "availableQuantity": 200,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 3,
      "price": {
        "listPrice": 2000000,
        "salePrice": 1600000,
        "currency": "VND"
      },
      "description": "License cá nhân - 1 máy tính",
      "imagesMediaTypes": [],
      "customFields": [
        {
          "fieldId": 20,
          "fieldValue": "Personal use only"
        }
      ]
    },
    {
      "name": "Commercial License",
      "sku": "PHOTOEDIT-COMMERCIAL-001",
      "availableQuantity": 100,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 10,
      "price": {
        "listPrice": 4000000,
        "salePrice": 3200000,
        "currency": "VND"
      },
      "description": "License thương mại - 5 máy tính",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 20,
          "fieldValue": "Commercial use allowed"
        },
        {
          "fieldId": 21,
          "fieldValue": "Up to 5 devices"
        }
      ]
    }
  ],
  "customFields": [
    {
      "fieldId": 30,
      "fieldValue": "Software License"
    },
    {
      "fieldId": 31,
      "fieldValue": "1 year validity"
    }
  ]
}

### 4. Tạo sản phẩm số - Game với auto activation
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Adventure Quest Premium",
  "description": "Game phiêu lưu RPG với nội dung premium và DLC",
  "price": {
    "listPrice": 800000,
    "salePrice": 600000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["game", "rpg", "adventure", "premium"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "AUTO_ACTIVE",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "CONTENT",
    "accessLink": "https://gamestore.example.com/library",
    "usageInstructions": "Game sẽ được tự động thêm vào thư viện của bạn"
  },
  "classifications": [
    {
      "name": "Standard Edition",
      "sku": "GAME-STANDARD-001",
      "availableQuantity": 1000,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 600000,
        "salePrice": 450000,
        "currency": "VND"
      },
      "description": "Phiên bản tiêu chuẩn - Game cơ bản",
      "imagesMediaTypes": [],
      "customFields": []
    },
    {
      "name": "Deluxe Edition",
      "sku": "GAME-DELUXE-001",
      "availableQuantity": 500,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 1000000,
        "salePrice": 750000,
        "currency": "VND"
      },
      "description": "Phiên bản cao cấp - Game + DLC + Soundtrack",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 40,
          "fieldValue": "Includes all DLC"
        },
        {
          "fieldId": 41,
          "fieldValue": "Digital soundtrack included"
        }
      ]
    }
  ]
}

### 5. Tạo sản phẩm số tối thiểu - Chỉ các trường bắt buộc
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Simple Digital Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Click vào link để download"
  },
  "classifications": [
    {
      "name": "Default",
      "sku": "SIMPLE-001",
      "availableQuantity": 100,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "Phiên bản mặc định",
      "imagesMediaTypes": []
    }
  ]
}

### 6. Tạo sản phẩm số với giao hàng qua Zalo
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Template PowerPoint Business",
  "description": "Bộ template PowerPoint chuyên nghiệp cho doanh nghiệp",
  "price": {
    "listPrice": 300000,
    "salePrice": 200000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg"],
  "tags": ["powerpoint", "template", "business", "presentation"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "ZALO",
    "deliveryTiming": "DELAYED"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://templates.example.com/business-pack",
    "usageInstructions": "Link download sẽ được gửi qua Zalo trong vòng 2 giờ"
  },
  "classifications": [
    {
      "name": "Basic Pack",
      "sku": "PPT-BASIC-001",
      "availableQuantity": 500,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 200000,
        "salePrice": 150000,
        "currency": "VND"
      },
      "description": "Gói cơ bản - 20 templates",
      "imagesMediaTypes": [],
      "customFields": [
        {
          "fieldId": 50,
          "fieldValue": "20 templates included"
        }
      ]
    },
    {
      "name": "Premium Pack",
      "sku": "PPT-PREMIUM-001",
      "availableQuantity": 200,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 400000,
        "salePrice": 300000,
        "currency": "VND"
      },
      "description": "Gói cao cấp - 50 templates + icons",
      "imagesMediaTypes": ["image/jpeg"],
      "customFields": [
        {
          "fieldId": 50,
          "fieldValue": "50 templates included"
        },
        {
          "fieldId": 51,
          "fieldValue": "1000+ icons included"
        }
      ]
    }
  ]
}

### 7. Tạo sản phẩm số với multiple images và custom fields phức tạp
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Complete Web Development Course",
  "description": "Khóa học phát triển web toàn diện từ cơ bản đến nâng cao",
  "price": {
    "listPrice": 5000000,
    "salePrice": 3500000,
    "currency": "VND"
  },
  "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"],
  "tags": ["web-development", "html", "css", "javascript", "react", "nodejs"],
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DIRECT_MESSAGE",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "ACCESS_CODE",
    "accessLink": "https://academy.example.com/courses/web-dev",
    "usageInstructions": "Mã truy cập sẽ được gửi qua tin nhắn riêng ngay sau khi thanh toán"
  },
  "classifications": [
    {
      "name": "Frontend Track",
      "sku": "WEBDEV-FRONTEND-001",
      "availableQuantity": 100,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 3000000,
        "salePrice": 2100000,
        "currency": "VND"
      },
      "description": "Chuyên sâu Frontend - HTML, CSS, JavaScript, React",
      "imagesMediaTypes": ["image/jpeg", "image/png"],
      "customFields": [
        {
          "fieldId": 60,
          "fieldValue": "Frontend specialization"
        },
        {
          "fieldId": 61,
          "fieldValue": "6 months access"
        },
        {
          "fieldId": 62,
          "fieldValue": "Certificate included"
        }
      ]
    },
    {
      "name": "Fullstack Track",
      "sku": "WEBDEV-FULLSTACK-001",
      "availableQuantity": 50,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 6000000,
        "salePrice": 4200000,
        "currency": "VND"
      },
      "description": "Fullstack - Frontend + Backend + Database + Deployment",
      "imagesMediaTypes": ["image/jpeg", "image/png", "image/webp"],
      "customFields": [
        {
          "fieldId": 60,
          "fieldValue": "Fullstack specialization"
        },
        {
          "fieldId": 61,
          "fieldValue": "12 months access"
        },
        {
          "fieldId": 62,
          "fieldValue": "Certificate included"
        },
        {
          "fieldId": 63,
          "fieldValue": "1-on-1 mentoring"
        },
        {
          "fieldId": 64,
          "fieldValue": "Job placement support"
        }
      ]
    }
  ],
  "customFields": [
    {
      "fieldId": 70,
      "fieldValue": "Programming Course"
    },
    {
      "fieldId": 71,
      "fieldValue": "Beginner to Advanced"
    },
    {
      "fieldId": 72,
      "fieldValue": "Vietnamese language"
    }
  ]
}

### 8. Test Validation Errors - Missing required fields
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Invalid Product"
  // Missing required fields: price, digitalFulfillmentFlow, digitalOutput, classifications
}

### 9. Test Validation Errors - Invalid price (sale > list)
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Invalid Price Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 150000,  // Sale price > List price (invalid)
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Test",
      "sku": "TEST-001",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 150000,  // Invalid price
        "currency": "VND"
      },
      "description": "Test classification",
      "imagesMediaTypes": []
    }
  ]
}

### 10. Test Validation Errors - Invalid quantity constraints
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Invalid Quantity Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Test",
      "sku": "TEST-001",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 5,  // Min > Max (invalid)
      "maxQuantityPerPurchase": 3,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "Test classification",
      "imagesMediaTypes": []
    }
  ]
}

### 11. Test Validation Errors - Duplicate classification names
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Duplicate Names Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Basic",  // Duplicate name
      "sku": "TEST-001",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "First classification",
      "imagesMediaTypes": []
    },
    {
      "name": "Basic",  // Duplicate name (should fail)
      "sku": "TEST-002",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "Second classification",
      "imagesMediaTypes": []
    }
  ]
}

### 12. Test Validation Errors - Duplicate SKUs
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Duplicate SKUs Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Basic",
      "sku": "DUPLICATE-001",  // Duplicate SKU
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "First classification",
      "imagesMediaTypes": []
    },
    {
      "name": "Premium",
      "sku": "DUPLICATE-001",  // Duplicate SKU (should fail)
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 200000,
        "salePrice": 160000,
        "currency": "VND"
      },
      "description": "Second classification",
      "imagesMediaTypes": []
    }
  ]
}

### 13. Test với maximum images per classification (10 images)
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Max Images Test Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Max Images",
      "sku": "MAX-IMG-001",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "Classification with maximum images",
      "imagesMediaTypes": [
        "image/jpeg",
        "image/png",
        "image/webp",
        "image/jpeg",
        "image/png",
        "image/webp",
        "image/jpeg",
        "image/png",
        "image/webp",
        "image/jpeg"
      ]
    }
  ]
}

### 14. Test với too many images (should fail - over 10 limit)
POST {{baseUrl}}/user/products
Authorization: Bearer {{token}}
Content-Type: application/json

{
  "productType": "DIGITAL",
  "name": "Too Many Images Product",
  "price": {
    "listPrice": 100000,
    "salePrice": 80000,
    "currency": "VND"
  },
  "purchaseCount": 0,
  "digitalFulfillmentFlow": {
    "deliveryMethod": "DASHBOARD",
    "deliveryTiming": "IMMEDIATE"
  },
  "digitalOutput": {
    "outputType": "DOWNLOAD_LINK",
    "accessLink": "https://example.com/download",
    "usageInstructions": "Test"
  },
  "classifications": [
    {
      "name": "Too Many Images",
      "sku": "TOO-MANY-001",
      "availableQuantity": 10,
      "minQuantityPerPurchase": 1,
      "maxQuantityPerPurchase": 1,
      "price": {
        "listPrice": 100000,
        "salePrice": 80000,
        "currency": "VND"
      },
      "description": "Classification with too many images",
      "imagesMediaTypes": [
        "image/jpeg", "image/png", "image/webp", "image/jpeg", "image/png",
        "image/webp", "image/jpeg", "image/png", "image/webp", "image/jpeg",
        "image/png"  // 11th image - should fail
      ]
    }
  ]
}

###
### DOCUMENTATION
###

### DTO Structure for Digital Product Creation:
###
### Required Fields:
### - productType: "DIGITAL"
### - name: string (max 255 chars)
### - price: HasPriceDto { listPrice, salePrice, currency }
### - purchaseCount: number (>= 0)
### - digitalFulfillmentFlow: { deliveryMethod, deliveryTiming }
### - digitalOutput: { outputType, accessLink, usageInstructions }
### - classifications: DigitalClassificationDto[] (at least 1)
###
### Optional Fields:
### - description: string (max 1000 chars)
### - imagesMediaTypes: string[] (product images)
### - tags: string[]
### - customFields: CustomFieldInputDto[]
###
### Classification Required Fields:
### - name: string (max 255 chars, unique)
### - sku: string (max 255 chars, unique)
### - availableQuantity: number (>= 0)
### - minQuantityPerPurchase: number (> 0)
### - maxQuantityPerPurchase: number (> 0, >= minQuantityPerPurchase)
### - price: HasPriceDto { listPrice, salePrice, currency }
### - description: string (max 1000 chars)
### - imagesMediaTypes: string[] (max 10 images)
###
### Classification Optional Fields:
### - customFields: CustomFieldInputDto[]
###
### Delivery Methods:
### - DASHBOARD: Giao qua dashboard
### - EMAIL: Giao qua email
### - SMS: Giao qua SMS
### - DIRECT_MESSAGE: Giao qua tin nhắn riêng
### - ZALO: Giao qua Zalo
### - AUTO_ACTIVE: Tự động kích hoạt
###
### Delivery Timing:
### - IMMEDIATE: Ngay lập tức
### - DELAYED: Có độ trễ
###
### Output Types:
### - DOWNLOAD_LINK: Link download
### - ACCESS_CODE: Mã truy cập
### - ACCOUNT_INFO: Thông tin tài khoản
### - CONTENT: Nội dung trực tiếp
###
### Validation Rules:
### 1. Price: salePrice <= listPrice, both > 0
### 2. Quantities: minQuantityPerPurchase <= maxQuantityPerPurchase, both > 0
### 3. Classification names: unique within product
### 4. Classification SKUs: unique within product
### 5. Images: max 10 per classification
### 6. Field lengths: name/sku max 255, description max 1000
###
### Response includes:
### - Created product information
### - Upload URLs for images (if imagesMediaTypes provided)
### - Classification upload URLs (if classification images provided)
###
